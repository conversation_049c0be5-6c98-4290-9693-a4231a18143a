(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-eHealthRecord-injury"],{"3b197":function(t,i,e){var a=e("c86c");i=a(!1),i.push([t.i,'.container[data-v-2b8b7436]{display:flex;flex-direction:column;height:100%;background-color:#f5f5f5}.tabs[data-v-2b8b7436]{display:flex;height:%?88?%;background-color:#fff;border-bottom:1px solid #ebeef5;flex-shrink:0;width:107%;position:relative;left:%?-24?%}.tab-item[data-v-2b8b7436]{flex:1;display:flex;justify-content:center;align-items:center;font-size:14px;color:#606266;position:relative}.tab-item.active[data-v-2b8b7436]{color:#409eff;font-weight:500}.tab-item.active[data-v-2b8b7436]::after{content:"";position:absolute;bottom:0;width:%?40?%;height:%?4?%;background-color:#409eff;border-radius:%?2?%}.content-area[data-v-2b8b7436]{flex:1;overflow:auto}.record-list[data-v-2b8b7436]{padding:%?20?%}.record-card[data-v-2b8b7436]{display:flex;margin-bottom:%?30?%}.time-line[data-v-2b8b7436]{width:%?120?%;display:flex;flex-direction:column;align-items:center;flex-shrink:0}.date-box[data-v-2b8b7436]{background-color:#409eff;padding:%?10?% %?20?%;border-radius:%?6?%}.date[data-v-2b8b7436]{color:#fff;font-size:12px}.line[data-v-2b8b7436]{width:%?2?%;height:100%;background-color:#dcdfe6;margin-top:%?20?%}.card-content[data-v-2b8b7436]{flex:1;background-color:#fff;border-radius:%?12?%;padding:%?30?%;margin-left:%?20?%;box-shadow:0 %?4?% %?12?% rgba(0,0,0,.05)}.info-item[data-v-2b8b7436]{margin-bottom:%?20?%}.label[data-v-2b8b7436]{color:#909399;font-size:14px}.value[data-v-2b8b7436]{color:#303133;font-size:14px}.highlight[data-v-2b8b7436]{color:#409eff;font-weight:500}.type-tag[data-v-2b8b7436]{display:inline-block;padding:%?6?% %?20?%;background-color:#ecf5ff;color:#409eff;border-radius:%?4?%;font-size:12px;margin-bottom:%?20?%}.first-identification .type-tag[data-v-2b8b7436]{background-color:#f0f9eb;color:#67c23a}.header[data-v-2b8b7436]{margin-bottom:%?20?%}.hospital[data-v-2b8b7436]{font-size:16px;color:#303133;font-weight:500;margin-right:%?20?%}.department[data-v-2b8b7436]{font-size:14px;color:#909399}.insurance-benefits[data-v-2b8b7436]{padding:%?20?%}.benefit-section[data-v-2b8b7436]{background-color:#fff;border-radius:%?12?%;padding:%?30?%;margin-bottom:%?30?%;box-shadow:0 %?4?% %?12?% rgba(0,0,0,.05)}.section-title[data-v-2b8b7436]{font-size:16px;color:#303133;font-weight:500;margin-bottom:%?30?%;padding-left:%?20?%;border-left:%?8?% solid #409eff}.benefit-item[data-v-2b8b7436]{display:flex;justify-content:space-between;margin-bottom:%?20?%}.item-label[data-v-2b8b7436]{color:#909399;font-size:14px}.item-value[data-v-2b8b7436]{color:#303133;font-size:14px;font-weight:500}.item-value.highlight[data-v-2b8b7436]{color:#409eff}.expand-btn[data-v-2b8b7436]{display:flex;align-items:center;justify-content:center;margin-top:%?20?%;color:#909399;font-size:14px}.empty-tip[data-v-2b8b7436]{display:flex;justify-content:center;align-items:center;padding:%?60?% 0}.empty-text[data-v-2b8b7436]{color:#909399;font-size:14px;text-align:center}',""]),t.exports=i},"547e":function(t,i,e){"use strict";var a=e("709a"),s=e.n(a);s.a},"709a":function(t,i,e){var a=e("3b197");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var s=e("967d").default;s("0baeda5d",a,!0,{sourceMap:!1,shadowMode:!1})},"7ba8":function(t,i,e){"use strict";e.r(i);var a=e("9459"),s=e.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(n);i["default"]=s.a},"85cf":function(t,i,e){"use strict";e.d(i,"b",(function(){return s})),e.d(i,"c",(function(){return n})),e.d(i,"a",(function(){return a}));var a={gracePage:e("1367").default},s=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[e("my-header",{attrs:{slot:"gHeader",title:"工伤保险"},slot:"gHeader"}),e("v-uni-view",{staticClass:"grace-body container",attrs:{slot:"gBody"},slot:"gBody"},[e("v-uni-view",{staticClass:"tabs"},t._l(t.tabs,(function(i,a){return e("v-uni-view",{key:a,staticClass:"tab-item",class:{active:t.currentTab===a},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.switchTab(a)}}},[e("v-uni-text",[t._v(t._s(i))])],1)})),1),e("v-uni-scroll-view",{staticClass:"content-area",attrs:{"scroll-y":!0}},[0===t.currentTab?e("v-uni-view",{staticClass:"record-list"},[t.injuryRecords&&t.injuryRecords.length>0?e("v-uni-view",t._l(t.injuryRecords,(function(i,a){return e("v-uni-view",{key:a,staticClass:"record-card"},[e("v-uni-view",{staticClass:"time-line"},[e("v-uni-view",{staticClass:"date-box"},[e("v-uni-text",{staticClass:"date"},[t._v(t._s(i.date))])],1),e("v-uni-view",{staticClass:"line"})],1),e("v-uni-view",{staticClass:"card-content"},[e("v-uni-view",{staticClass:"type-tag"},[t._v(t._s(i.status))]),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("事故时间：")]),e("v-uni-text",{staticClass:"value"},[t._v(t._s(i.accidentTime))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("事故地点：")]),e("v-uni-text",{staticClass:"value"},[t._v(t._s(i.location))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("事故原因：")]),e("v-uni-text",{staticClass:"value"},[t._v(t._s(i.cause))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("事故类型：")]),e("v-uni-text",{staticClass:"value"},[t._v(t._s(i.type))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("认定结果：")]),e("v-uni-text",{staticClass:"value highlight"},[t._v(t._s(i.result))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("认定部门：")]),e("v-uni-text",{staticClass:"value"},[t._v(t._s(i.department))])],1)],1)],1)})),1):e("v-uni-view",{staticClass:"empty-tip"},[e("v-uni-text",{staticClass:"empty-text"},[t._v("暂无工伤认定记录")])],1)],1):t._e(),1===t.currentTab?e("v-uni-view",{staticClass:"record-list"},[t.disabilityRecords&&t.disabilityRecords.length>0?e("v-uni-view",t._l(t.disabilityRecords,(function(i,a){return e("v-uni-view",{key:a,staticClass:"record-card",class:{"first-identification":i.level<=4}},[e("v-uni-view",{staticClass:"time-line"},[e("v-uni-view",{staticClass:"date-box"},[e("v-uni-text",{staticClass:"date"},[t._v(t._s(i.date))])],1),e("v-uni-view",{staticClass:"line"})],1),e("v-uni-view",{staticClass:"card-content"},[e("v-uni-view",{staticClass:"type-tag"},[t._v(t._s(i.level)+"级伤残")]),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("鉴定时间：")]),e("v-uni-text",{staticClass:"value"},[t._v(t._s(i.assessmentTime))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("鉴定类型：")]),e("v-uni-text",{staticClass:"value"},[t._v(t._s(i.type))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("鉴定结论：")]),e("v-uni-text",{staticClass:"value highlight"},[t._v(t._s(i.conclusion))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("劳动能力：")]),e("v-uni-text",{staticClass:"value"},[t._v(t._s(i.workAbility))])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("鉴定费用：")]),e("v-uni-text",{staticClass:"value"},[t._v(t._s(i.cost)+"元")])],1),e("v-uni-view",{staticClass:"info-item"},[e("v-uni-text",{staticClass:"label"},[t._v("鉴定机构：")]),e("v-uni-text",{staticClass:"value"},[t._v(t._s(i.institution))])],1)],1)],1)})),1):e("v-uni-view",{staticClass:"empty-tip"},[e("v-uni-text",{staticClass:"empty-text"},[t._v("暂无劳动鉴定记录")])],1)],1):t._e(),2===t.currentTab?e("v-uni-view",{staticClass:"record-list"},[e("v-uni-view",{staticClass:"insurance-benefits"},[e("v-uni-view",{staticClass:"benefit-section"},[e("v-uni-view",{staticClass:"section-title"},[t._v("医疗费用")]),e("v-uni-view",{staticClass:"benefit-items"},[e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("医疗费用：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.medical.treatment)+"元")])],1),e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("康复费用：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.medical.rehabilitation)+"元")])],1),e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("住院补助：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.medical.hospitalization)+"元")])],1),e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("交通食宿：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.medical.transportation)+"元")])],1)],1)],1),e("v-uni-view",{staticClass:"benefit-section"},[e("v-uni-view",{staticClass:"section-title"},[t._v("停工留薪")]),e("v-uni-view",{staticClass:"benefit-items"},[e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("停工期间：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.sickLeave.period))])],1),e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("工资待遇：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.sickLeave.salary)+"元/月")])],1),e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("福利待遇：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.sickLeave.welfare))])],1)],1)],1),e("v-uni-view",{staticClass:"benefit-section"},[e("v-uni-view",{staticClass:"section-title"},[t._v("伤残待遇")]),e("v-uni-view",{staticClass:"benefit-items"},[e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("伤残补助：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.disability.compensation)+"元")])],1),e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("伤残津贴：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.disability.allowance)+"元/月")])],1),e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("护理费用：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.disability.nursing)+"元/月")])],1),e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("辅助器具：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.disability.equipment)+"元")])],1)],1)],1),e("v-uni-view",{staticClass:"benefit-section"},[e("v-uni-view",{staticClass:"section-title"},[t._v("工亡待遇")]),e("v-uni-view",{staticClass:"benefit-items"},[e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("丧葬补助：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.death.funeral)+"元")])],1),e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("抚恤金：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.death.pension)+"元/月")])],1),e("v-uni-view",{staticClass:"benefit-item"},[e("v-uni-text",{staticClass:"item-label"},[t._v("一次性补助：")]),e("v-uni-text",{staticClass:"item-value"},[t._v(t._s(t.benefits.death.compensation)+"元")])],1)],1)],1)],1)],1):t._e()],1)],1)],1)},n=[]},9459:function(t,i,e){"use strict";e("6a54"),Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;i.default={data:function(){return{tabs:["工伤认定","劳动鉴定","保险待遇"],currentTab:0,isRefreshing:!1,injuryRecords:[],disabilityRecords:[],benefits:{medical:{treatment:0,rehabilitation:0,hospitalization:0,transportation:0},sickLeave:{period:"-",salary:0,welfare:"-"},disability:{compensation:0,allowance:0,nursing:0,equipment:0},death:{funeral:0,pension:0,compensation:0}}}},methods:{switchTab:function(t){this.currentTab=t},toggleExpand:function(t){this.medicalRecords[t].expanded=!this.medicalRecords[t].expanded}}}},a8e7:function(t,i,e){"use strict";e.r(i);var a=e("85cf"),s=e("7ba8");for(var n in s)["default"].indexOf(n)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(n);e("547e");var l=e("828b"),v=Object(l["a"])(s["default"],a["b"],a["c"],!1,null,"2b8b7436",null,!1,a["a"],void 0);i["default"]=v.exports}}]);