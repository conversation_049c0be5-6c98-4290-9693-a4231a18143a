(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_lifeCycle-pages-MedicationServices-MedicationScheme"],{"1dbe":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return i}));var i={gracePage:a("1367").default},n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF"}},[a("my-header",{attrs:{slot:"gHeader",title:"用药方案"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body container",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"section-title"},[t._v("基本信息")]),a("v-uni-view",{staticClass:"base-section"},[a("v-uni-text",{staticClass:"item"},[a("v-uni-text",{staticClass:"label"},[t._v("药品种类：")]),a("v-uni-text",[t._v(t._s(t.medicationList.medicineType))])],1),a("v-uni-text",{staticClass:"item"},[a("v-uni-text",{staticClass:"label"},[t._v("药品名称：")]),a("v-uni-text",[t._v(t._s(t.medicationList.medicineName))])],1),a("v-uni-text",{staticClass:"item"},[a("v-uni-text",{staticClass:"label"},[t._v("药品价格：")]),a("v-uni-text",[t._v(t._s(t.medicationList.medicinePrice))])],1),a("v-uni-text",{staticClass:"item"},[a("v-uni-text",{staticClass:"label"},[t._v("药品用量：")]),a("v-uni-text",[t._v(t._s(t.medicationList.medicineDosage)+"/次")])],1),a("v-uni-text",{staticClass:"item"},[a("v-uni-text",{staticClass:"label"},[t._v("药品数量：")]),a("v-uni-text",[t._v(t._s(t.medicationList.medicineQuantity))])],1),a("v-uni-text",{staticClass:"item"},[a("v-uni-text",{staticClass:"label"},[t._v("药品起效时间：")]),a("v-uni-text",[t._v(t._s(t.medicationList.onsetTime))])],1)],1),a("v-uni-view",{staticClass:"section-title"},[t._v("用药方案")]),a("v-uni-view",{staticClass:"base-section"},[a("v-uni-view",{staticClass:"content-box"},[a("v-uni-text",[t._v(t._s(t.medicationList.medicationPlan))])],1)],1),a("v-uni-view",{staticClass:"section-title"},[t._v("用药禁忌")]),a("v-uni-view",{staticClass:"base-section"},[a("v-uni-view",{staticClass:"content-box"},[a("v-uni-text",[t._v(t._s(t.medicationList.contraindications))])],1)],1),a("v-uni-view",{staticClass:"section-title"},[t._v("用药注意事项")]),a("v-uni-view",{staticClass:"base-section"},[a("v-uni-view",{staticClass:"content-box"},[a("v-uni-text",[t._v(t._s(t.medicationList.precautions))])],1)],1),a("v-uni-view",{staticClass:"section-title"},[t._v("药物治疗的疾病")]),a("v-uni-view",{staticClass:"base-section"},[a("v-uni-view",{staticClass:"content-box"},[a("v-uni-text",[t._v(t._s(t.medicationList.treatedDisease))])],1)],1),a("v-uni-view",{staticClass:"section-title"},[t._v("贮存药物的正确方式")]),a("v-uni-view",{staticClass:"base-section"},[a("v-uni-view",{staticClass:"content-box"},[a("v-uni-text",[t._v(t._s(t.medicationList.storagePractices))])],1)],1),a("v-uni-view",{staticClass:"section-title"},[t._v("服药的事宜时间")]),a("v-uni-view",{staticClass:"base-section"},[a("v-uni-view",{staticClass:"content-box"},[a("v-uni-text",[t._v(t._s(t.medicationList.appropriateTime))])],1)],1),a("v-uni-view",{staticClass:"section-title"},[t._v("用药适当的疗程")]),a("v-uni-view",{staticClass:"base-section"},[a("v-uni-view",{staticClass:"content-box"},[a("v-uni-text",[t._v(t._s(t.medicationList.treatmentCourse))])],1)],1),a("v-uni-view",{staticClass:"section-title"},[t._v("存在的不良反应")]),a("v-uni-view",{staticClass:"base-section"},[a("v-uni-view",{staticClass:"content-box"},[a("v-uni-text",[t._v(t._s(t.medicationList.sideEffect))])],1)],1)],1)],1)},s=[]},2246:function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("0518")),s={followupRecordList:function(t){return(0,n.default)({url:"manage/rehab/followupRecordList",method:"get",data:t})},treatmentInformationList:function(t){return(0,n.default)({url:"manage/rehab/treatmentInformationList",method:"get",data:t})},treatmentInformationDetail:function(t){return(0,n.default)({url:"manage/rehab/treatmentInformationDetail",method:"get",data:t})},medicationGuidanceList:function(t){return(0,n.default)({url:"manage/rehab/medicationGuidanceList",method:"get",data:t})},medicationGuidanceDetail:function(t){return(0,n.default)({url:"manage/rehab/medicationGuidanceDetail",method:"get",data:t})},recoveryInfo:function(t){return(0,n.default)({url:"manage/rehab/recoveryInfo",method:"get",data:t})},recoveryInfoUpload:function(t){return(0,n.default)({url:"manage/rehab/recoveryInfoUpload",method:"get",data:t})},personnel:function(t){return(0,n.default)({url:"manage/rehab/personnel",method:"get",data:t})},station:function(t){return(0,n.default)({url:"manage/rehab/station",method:"get",data:t})},appointment:function(t){return(0,n.default)({url:"manage/rehab/appointment",method:"get",data:t})},createAppointment:function(t){return(0,n.default)({url:"manage/rehab/createAppointment",method:"post",data:t})},createRehabGuideApplication:function(t){return(0,n.default)({url:"manage/rehab/createRehabGuideApplication",method:"post",data:t})},getDiseaseClassify:function(t){return(0,n.default)({url:"manage/eHealthRecord/getDiseaseClassify",method:"get",data:t})},medicationGuidanceApply:function(t){return(0,n.default)({url:"manage/rehab/medicationGuidanceApply",method:"get",data:t})},medicationGuidanceApplyAdd:function(t){return(0,n.default)({url:"manage/rehab/medicationGuidanceApplyAdd",method:"post",data:t})},medicationGuidanceApplyFileDelete:function(t){return(0,n.default)({url:"manage/rehab/medicationGuidanceApplyFileDelete",method:"delete",data:t})},medicationGuidanceApplyDetail:function(t){return(0,n.default)({url:"manage/rehab/medicationGuidanceApplyDetail",method:"get",data:t})},medicationGuidanceApplyCancel:function(t){return(0,n.default)({url:"manage/rehab/medicationGuidanceApplyCancel",method:"delete",data:t})},getMedicationGuidanceDetail:function(t){return(0,n.default)({url:"manage/rehab/getMedicationGuidanceDetail",method:"get",data:t})},informationNewsPagePatient:function(t){return(0,n.default)({url:"manage/rehab/informationNewsPagePatient",method:"get",data:t})},informationNewsDetail:function(t){return(0,n.default)({url:"manage/rehab/informationNewsDetail",method:"get",data:t})},rehabGuidanceApplyPage:function(t){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyPage",method:"get",data:t})},rehabGuidanceApplyAdd:function(t){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyAdd",method:"post",data:t})},rehabGuidanceApplyFileDelete:function(t){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyFileDelete",method:"post",data:t})},rehabGuidanceApplyDetail:function(t){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyDetail",method:"get",data:t})},rehabGuidanceApplyCancel:function(t){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyCancel",method:"delete",data:t})},informationNewsRehabPagePatient:function(t){return(0,n.default)({url:"manage/rehab/informationNewsRehabPagePatient",method:"get",data:t})},rehabGuidanceApplyExportPatient:function(t){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyExportPatient",method:"get",data:t})}},u=s;e.default=u},6593:function(t,e,a){"use strict";a.r(e);var i=a("9ffe"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(s);e["default"]=n.a},"9b552":function(t,e,a){var i=a("c86c");e=i(!1),e.push([t.i,".grace-body[data-v-3c82f8e2]{min-height:calc(100vh - %?120?%);padding-top:%?30?%;background-color:#f6f6f6}.base-section[data-v-3c82f8e2]{margin-bottom:%?40?%;border-radius:%?12?%;box-shadow:0 %?2?% %?12?% rgba(0,0,0,.1);padding:%?20?%}.base-section .item[data-v-3c82f8e2]{display:block;margin-bottom:%?16?%}.base-section .label[data-v-3c82f8e2]{font-weight:500;margin-right:%?10?%}.base-section[data-v-3c82f8e2]{margin-bottom:%?40?%}.section-title[data-v-3c82f8e2]{font-size:%?32?%;font-weight:600;margin-bottom:%?20?%;color:#333}",""]),t.exports=e},"9bcf":function(t,e,a){"use strict";a.r(e);var i=a("1dbe"),n=a("6593");for(var s in n)["default"].indexOf(s)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(s);a("ec9e");var u=a("828b"),o=Object(u["a"])(n["default"],i["b"],i["c"],!1,null,"3c82f8e2",null,!1,i["a"],void 0);e["default"]=o.exports},"9ffe":function(t,e,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(a("2634")),s=i(a("2fdc")),u=i(a("2246")),o={data:function(){return{detailId:"",medicationList:{}}},onLoad:function(t){this.detailId=t.id||"",this.detailId&&this.getGuidanceDetail()},created:function(){},methods:{getGuidanceDetail:function(){var t=this;return(0,s.default)((0,n.default)().mark((function e(){var a;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.default.getMedicationGuidanceDetail({id:t.detailId});case 2:a=e.sent,t.medicationList=a.data;case 4:case"end":return e.stop()}}),e)})))()}}};e.default=o},ec9e:function(t,e,a){"use strict";var i=a("fb36"),n=a.n(i);n.a},fb36:function(t,e,a){var i=a("9b552");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var n=a("967d").default;n("ebe8a264",i,!0,{sourceMap:!1,shadowMode:!1})}}]);