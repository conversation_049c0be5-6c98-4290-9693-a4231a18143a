(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-eHealthRecord-servic"],{"0cf2":function(e,t,i){"use strict";i.r(t);var s=i("9975"),a=i.n(s);for(var n in s)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(n);t["default"]=a.a},"24e1":function(e,t,i){"use strict";i.r(t);var s=i("4e01"),a=i("0cf2");for(var n in a)["default"].indexOf(n)<0&&function(e){i.d(t,e,(function(){return a[e]}))}(n);i("fbbe");var o=i("828b"),c=Object(o["a"])(a["default"],s["b"],s["c"],!1,null,"3f7e3d30",null,!1,s["a"],void 0);t["default"]=c.exports},"268d":function(e,t,i){var s=i("a7c3");s.__esModule&&(s=s.default),"string"===typeof s&&(s=[[e.i,s,""]]),s.locals&&(e.exports=s.locals);var a=i("967d").default;a("6772c27a",s,!0,{sourceMap:!1,shadowMode:!1})},"4e01":function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"c",(function(){return n})),i.d(t,"a",(function(){return s}));var s={gracePage:i("1367").default},a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[i("my-header",{attrs:{slot:"gHeader",title:"其他医疗卫生服务记录"},slot:"gHeader"}),i("v-uni-view",{staticClass:"grace-body container",attrs:{slot:"gBody"},slot:"gBody"},[i("v-uni-view",{staticClass:"section",staticStyle:{"margin-top":"24rpx"}},[i("v-uni-view",{staticClass:"section-header",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSection("referral")}}},[i("v-uni-text",{staticClass:"section-title"},[e._v("转诊记录 ("+e._s(e.referralRecords.length)+")")]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:e.referralExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.referralExpanded,expression:"referralExpanded"}],staticClass:"record-list"},[e.referralRecords&&e.referralRecords.length>0?i("v-uni-view",e._l(e.referralRecords,(function(t,s){return i("v-uni-view",{key:s,staticClass:"record-card"},[i("v-uni-view",{staticClass:"card-header"},[i("v-uni-text",{staticClass:"time"},[e._v(e._s(t.time))])],1),i("v-uni-view",{staticClass:"hospital-info"},[i("v-uni-view",{staticClass:"from-hospital"},[i("v-uni-text",{staticClass:"label"},[e._v("转出医院：")]),i("v-uni-text",[e._v(e._s(t.fromHospital))])],1),i("v-uni-view",{staticClass:"to-hospital"},[i("v-uni-text",{staticClass:"label"},[e._v("转入医院：")]),i("v-uni-text",[e._v(e._s(t.toHospital))])],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isExpanded,expression:"item.isExpanded"}]},[i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("转诊原因：")]),i("v-uni-text",[e._v(e._s(t.reason))])],1),i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("病情变化及处理措施：")]),i("v-uni-text",[e._v(e._s(t.treatment))])],1)],1),i("v-uni-view",{staticClass:"show-more",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleRecord(s,"referral")}}},[i("v-uni-text",[e._v(e._s(t.isExpanded?"收起":"查看更多"))]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:t.isExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1)],1)})),1):i("v-uni-view",{staticClass:"empty-tip"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无转诊记录")])],1)],1)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-header",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSection("consultation")}}},[i("v-uni-text",{staticClass:"section-title"},[e._v("会诊记录 ("+e._s(e.consultationRecords.length)+")")]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:e.consultationExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.consultationExpanded,expression:"consultationExpanded"}],staticClass:"record-list"},[e.consultationRecords&&e.consultationRecords.length>0?i("v-uni-view",e._l(e.consultationRecords,(function(t,s){return i("v-uni-view",{key:s,staticClass:"record-card"},[i("v-uni-view",{staticClass:"card-header"},[i("v-uni-text",{staticClass:"time"},[e._v(e._s(t.time))])],1),i("v-uni-view",{staticClass:"consultation-info"},[i("v-uni-text",{staticClass:"department"},[e._v(e._s(t.department))]),i("v-uni-view",{staticClass:"doctors"},[i("v-uni-text",{staticClass:"label"},[e._v("参与医生：")]),i("v-uni-text",[e._v(e._s(t.doctors.join("、")))])],1)],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isExpanded,expression:"item.isExpanded"}]},[i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("会诊意见：")]),i("v-uni-text",[e._v(e._s(t.opinion))])],1),i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("后续处理措施：")]),i("v-uni-text",[e._v(e._s(t.followUp))])],1)],1),i("v-uni-view",{staticClass:"show-more",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleRecord(s,"consultation")}}},[i("v-uni-text",[e._v(e._s(t.isExpanded?"收起":"查看更多"))]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:t.isExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1)],1)})),1):i("v-uni-view",{staticClass:"empty-tip"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无会诊记录")])],1)],1)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-header",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSection("medicalReport")}}},[i("v-uni-text",{staticClass:"section-title"},[e._v("医学报告记录 ("+e._s(e.medicalReports.length)+")")]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:e.medicalReportExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.medicalReportExpanded,expression:"medicalReportExpanded"}],staticClass:"record-list"},[e.medicalReports&&e.medicalReports.length>0?i("v-uni-view",e._l(e.medicalReports,(function(t,s){return i("v-uni-view",{key:s,staticClass:"record-card"},[i("v-uni-view",{staticClass:"card-header"},[i("v-uni-text",{staticClass:"time"},[e._v(e._s(t.time))])],1),i("v-uni-view",{staticClass:"report-info"},[i("v-uni-text",{staticClass:"report-type"},[e._v(e._s(t.type))]),i("v-uni-text",{staticClass:"department"},[e._v(e._s(t.department))])],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isExpanded,expression:"item.isExpanded"}]},[i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("检查结果：")]),i("v-uni-text",[e._v(e._s(t.result))])],1),i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("医生建议：")]),i("v-uni-text",[e._v(e._s(t.suggestion))])],1)],1),i("v-uni-view",{staticClass:"show-more",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleRecord(s,"medicalReport")}}},[i("v-uni-text",[e._v(e._s(t.isExpanded?"收起":"查看更多"))]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:t.isExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1)],1)})),1):i("v-uni-view",{staticClass:"empty-tip"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无医学报告记录")])],1)],1)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-header",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSection("publicHealth")}}},[i("v-uni-text",{staticClass:"section-title"},[e._v("公共卫生记录 ("+e._s(e.publicHealthRecords.length)+")")]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:e.publicHealthExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.publicHealthExpanded,expression:"publicHealthExpanded"}],staticClass:"record-list"},[e.publicHealthRecords&&e.publicHealthRecords.length>0?i("v-uni-view",e._l(e.publicHealthRecords,(function(t,s){return i("v-uni-view",{key:s,staticClass:"record-card"},[i("v-uni-view",{staticClass:"card-header"},[i("v-uni-text",{staticClass:"time"},[e._v(e._s(t.time))])],1),i("v-uni-view",{staticClass:"health-info"},[i("v-uni-text",{staticClass:"service-type"},[e._v(e._s(t.serviceType))])],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isExpanded,expression:"item.isExpanded"}]},[i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("随访内容：")]),i("v-uni-text",[e._v(e._s(t.content))])],1),i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("健康指导：")]),i("v-uni-text",[e._v(e._s(t.guidance))])],1)],1),i("v-uni-view",{staticClass:"show-more",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleRecord(s,"publicHealth")}}},[i("v-uni-text",[e._v(e._s(t.isExpanded?"收起":"查看更多"))]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:t.isExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1)],1)})),1):i("v-uni-view",{staticClass:"empty-tip"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无公共卫生记录")])],1)],1)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-header",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSection("tcm")}}},[i("v-uni-text",{staticClass:"section-title"},[e._v("中医药服务记录 ("+e._s(e.tcmRecords.length)+")")]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:e.tcmExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.tcmExpanded,expression:"tcmExpanded"}],staticClass:"record-list"},[e.tcmRecords&&e.tcmRecords.length>0?i("v-uni-view",e._l(e.tcmRecords,(function(t,s){return i("v-uni-view",{key:s,staticClass:"record-card"},[i("v-uni-view",{staticClass:"card-header"},[i("v-uni-text",{staticClass:"time"},[e._v(e._s(t.time))])],1),i("v-uni-view",{staticClass:"tcm-info"},[i("v-uni-text",{staticClass:"service-type"},[e._v(e._s(t.serviceType))])],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isExpanded,expression:"item.isExpanded"}]},[i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("诊断结果：")]),i("v-uni-text",[e._v(e._s(t.diagnosis))])],1),i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("治疗方案：")]),i("v-uni-text",[e._v(e._s(t.treatment))])],1)],1),i("v-uni-view",{staticClass:"show-more",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleRecord(s,"tcm")}}},[i("v-uni-text",[e._v(e._s(t.isExpanded?"收起":"查看更多"))]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:t.isExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1)],1)})),1):i("v-uni-view",{staticClass:"empty-tip"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无中医药服务记录")])],1)],1)],1),i("v-uni-view",{staticClass:"section"},[i("v-uni-view",{staticClass:"section-header",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSection("poisoning")}}},[i("v-uni-text",{staticClass:"section-title"},[e._v("化学中毒救治记录 ("+e._s(e.poisoningRecords.length)+")")]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:e.poisoningExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:e.poisoningExpanded,expression:"poisoningExpanded"}],staticClass:"record-list"},[e.poisoningRecords&&e.poisoningRecords.length>0?i("v-uni-view",e._l(e.poisoningRecords,(function(t,s){return i("v-uni-view",{key:s,staticClass:"record-card"},[i("v-uni-view",{staticClass:"card-header"},[i("v-uni-text",{staticClass:"time"},[e._v(e._s(t.time))])],1),i("v-uni-view",{staticClass:"poisoning-info"},[i("v-uni-text",{staticClass:"poisoning-type"},[e._v(e._s(t.poisonType))])],1),i("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:t.isExpanded,expression:"item.isExpanded"}]},[i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("治疗措施：")]),i("v-uni-text",[e._v(e._s(t.measures))])],1),i("v-uni-view",{staticClass:"detail-item"},[i("v-uni-text",{staticClass:"label"},[e._v("转归情况：")]),i("v-uni-text",[e._v(e._s(t.outcome))])],1)],1),i("v-uni-view",{staticClass:"show-more",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleRecord(s,"poisoning")}}},[i("v-uni-text",[e._v(e._s(t.isExpanded?"收起":"查看更多"))]),i("v-uni-text",{staticClass:"grace-grids-icon grace-icons",class:t.isExpanded?"icon-arrow-up":"icon-arrow-down",staticStyle:{"font-size":"1em","line-height":"1em",width:"1em",height:"1em"}})],1)],1)})),1):i("v-uni-view",{staticClass:"empty-tip"},[i("v-uni-text",{staticClass:"empty-text"},[e._v("暂无化学中毒救治记录")])],1)],1)],1)],1)],1)},n=[]},9975:function(e,t,i){"use strict";i("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{referralExpanded:!1,consultationExpanded:!1,medicalReportExpanded:!1,publicHealthExpanded:!1,tcmExpanded:!1,poisoningExpanded:!1,referralRecords:[],consultationRecords:[],medicalReports:[],publicHealthRecords:[],tcmRecords:[],poisoningRecords:[]}},methods:{toggleSection:function(e){"referral"===e?this.referralExpanded=!this.referralExpanded:"consultation"===e?this.consultationExpanded=!this.consultationExpanded:"medicalReport"===e?this.medicalReportExpanded=!this.medicalReportExpanded:"publicHealth"===e?this.publicHealthExpanded=!this.publicHealthExpanded:"tcm"===e?this.tcmExpanded=!this.tcmExpanded:"poisoning"===e&&(this.poisoningExpanded=!this.poisoningExpanded)},toggleRecord:function(e,t){"referral"===t?this.referralRecords[e].isExpanded=!this.referralRecords[e].isExpanded:"consultation"===t?this.consultationRecords[e].isExpanded=!this.consultationRecords[e].isExpanded:"medicalReport"===t?this.medicalReports[e].isExpanded=!this.medicalReports[e].isExpanded:"publicHealth"===t?this.publicHealthRecords[e].isExpanded=!this.publicHealthRecords[e].isExpanded:"tcm"===t?this.tcmRecords[e].isExpanded=!this.tcmRecords[e].isExpanded:"poisoning"===t&&(this.poisoningRecords[e].isExpanded=!this.poisoningRecords[e].isExpanded)},toggleDetail:function(e,t){if("reason"===t||"treatment"===t){var i=this.referralRecords[e];"reason"===t?i.reasonExpanded=!i.reasonExpanded:i.treatmentExpanded=!i.treatmentExpanded}else if("opinion"===t||"followUp"===t){var s=this.consultationRecords[e];"opinion"===t?s.opinionExpanded=!s.opinionExpanded:s.followUpExpanded=!s.followUpExpanded}}}}},a7c3:function(e,t,i){var s=i("c86c");t=s(!1),t.push([e.i,".container[data-v-3f7e3d30]{display:flex;flex-direction:column;height:100%;background-color:#f5f5f5}.header[data-v-3f7e3d30]{height:%?88?%;display:flex;align-items:center;justify-content:center;background-color:#fff;border-bottom:1px solid #eee;flex-shrink:0}.title[data-v-3f7e3d30]{font-size:%?32?%;font-weight:500;color:#333}.content[data-v-3f7e3d30]{flex:1;overflow:auto}.section[data-v-3f7e3d30]{margin-bottom:%?20?%;background-color:#fff}.section-header[data-v-3f7e3d30]{height:%?88?%;display:flex;align-items:center;justify-content:space-between;padding:0 %?30?%;border-bottom:1px solid #eee}.section-title[data-v-3f7e3d30]{font-size:%?30?%;font-weight:500;color:#333}.record-list[data-v-3f7e3d30]{padding:%?20?%}.record-card[data-v-3f7e3d30]{background-color:#fff;border-radius:%?12?%;padding:%?24?%;margin-bottom:%?20?%;box-shadow:0 %?2?% %?8?% rgba(0,0,0,.05)}.card-header[data-v-3f7e3d30]{display:flex;align-items:center;margin-bottom:%?20?%}.time[data-v-3f7e3d30]{margin-left:%?12?%;font-size:%?28?%;color:#2979ff}.hospital-info[data-v-3f7e3d30]{margin-bottom:%?20?%}.from-hospital[data-v-3f7e3d30],\n.to-hospital[data-v-3f7e3d30]{margin-bottom:%?12?%;font-size:%?28?%;color:#333}.label[data-v-3f7e3d30]{color:#666}.consultation-info[data-v-3f7e3d30],\n.report-info[data-v-3f7e3d30],\n.health-info[data-v-3f7e3d30],\n.tcm-info[data-v-3f7e3d30],\n.poisoning-info[data-v-3f7e3d30]{margin-bottom:%?20?%}.report-type[data-v-3f7e3d30],\n.service-type[data-v-3f7e3d30],\n.poisoning-type[data-v-3f7e3d30]{font-size:%?28?%;color:#333;margin-bottom:%?12?%;display:block}.department[data-v-3f7e3d30]{font-size:%?28?%;color:#333;margin-bottom:%?12?%;display:block}.doctors[data-v-3f7e3d30]{font-size:%?28?%;color:#333}.show-more[data-v-3f7e3d30]{display:flex;align-items:center;justify-content:center;padding:%?20?% 0;color:#2979ff;font-size:%?28?%}.detail-item[data-v-3f7e3d30]{margin-bottom:%?12?%;font-size:%?28?%;color:#333}.loading[data-v-3f7e3d30],\n.no-more[data-v-3f7e3d30]{height:%?100?%;display:flex;align-items:center;justify-content:center;color:#999;font-size:%?24?%}.loading uni-text[data-v-3f7e3d30]{margin-left:%?12?%}.empty-tip[data-v-3f7e3d30]{display:flex;justify-content:center;align-items:center;padding:%?60?% 0}.empty-text[data-v-3f7e3d30]{color:#909399;font-size:%?28?%;text-align:center}",""]),e.exports=t},fbbe:function(e,t,i){"use strict";var s=i("268d"),a=i.n(s);a.a}}]);