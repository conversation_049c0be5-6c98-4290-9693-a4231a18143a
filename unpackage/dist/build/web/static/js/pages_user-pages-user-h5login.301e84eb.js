(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-user-h5login"],{"242b":function(e,t,n){"use strict";n.r(t);var a=n("bb04"),r=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=r.a},7477:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){}));var a=function(){var e=this.$createElement;this._self._c;return this._m(0)},r=[function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"public-web-container breathe"},[t("p",[this._v("正在跳转登录中...")])])}]},"7ed3":function(e,t,n){"use strict";n.r(t);var a=n("7477"),r=n("242b");for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);n("f5e3");var i=n("828b"),c=Object(i["a"])(r["default"],a["b"],a["c"],!1,null,"292f77c2",null,!1,a["a"],void 0);t["default"]=c.exports},b692:function(e,t,n){"use strict";(function(e){n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("c1a3"),n("bf0f"),n("18f7"),n("de6c"),n("2797"),n("c223"),n("dc8a"),n("d4b5"),n("7a76"),n("c9b5"),n("aa9c"),n("8f71"),n("7f48");var r=a(n("2634")),o=a(n("2fdc")),i=a(n("fcf3")),c=a(n("80b1")),s=a(n("efe5")),u=function(){function t(){(0,c.default)(this,t),this.isReady=!1,this.readyCallbacks=[],this.messageHandlers=new Map,this.init()}return(0,s.default)(t,[{key:"init",value:function(){this.checkNativeReady(),this.setupMessageListener()}},{key:"checkNativeReady",value:function(){var t=this,n=setInterval((function(){t.isNativeAvailable()&&(t.isReady=!0,clearInterval(n),t.readyCallbacks.forEach((function(e){return e()})),t.readyCallbacks=[],e.log("原生桥接已准备就绪"))}),100);setTimeout((function(){clearInterval(n),t.isReady||e.warn("原生桥接初始化超时")}),5e3)}},{key:"isNativeAvailable",value:function(){return["android","Android","AndroidInterface","NativeInterface","JSBridge","WebViewJavaScriptBridge"].some((function(e){return window[e]&&"object"===(0,i.default)(window[e])}))}},{key:"setupMessageListener",value:function(){var e=this;window.addEventListener("message",(function(t){e.handleMessage(t.data)})),document.addEventListener("message",(function(t){e.handleMessage(t.data)}))}},{key:"handleMessage",value:function(e){if(e&&"object"===(0,i.default)(e)){e.type;var t=e.messageId;if(this.messageHandlers.has(t)){var n=this.messageHandlers.get(t);n(e),this.messageHandlers.delete(t)}}}},{key:"callNativeMethod",value:function(t){var n=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;return new Promise((function(o,i){try{e.log("调用原生方法: ".concat(t),a);var c=n.tryDirectCall(t,a);if(null!==c)return void o(c);n.tryPostMessageCall(t,a,r).then(o).catch(i)}catch(s){e.error("调用原生方法 ".concat(t," 失败:"),s),i(s)}}))}},{key:"tryDirectCall",value:function(t,n){for(var a=0,r=["android","Android","AndroidInterface","NativeInterface","JSBridge"];a<r.length;a++){var o=r[a],i=window[o];if(i&&"function"===typeof i[t]){e.log("通过 ".concat(o,".").concat(t," 调用"));try{var c=void 0;if(c=Object.keys(n).length>0?i[t](JSON.stringify(n)):i[t](),"string"===typeof c)try{c=JSON.parse(c)}catch(s){}return c}catch(u){e.error("直接调用 ".concat(o,".").concat(t," 失败:"),u);continue}}}return null}},{key:"tryPostMessageCall",value:function(e,t,n){var a=this;return new Promise((function(r,o){if(window.parent&&window.parent.postMessage){var i="".concat(Date.now(),"_").concat(Math.random());a.messageHandlers.set(i,(function(e){e.success?r(e.result):o(new Error(e.error||"原生方法调用失败"))})),window.parent.postMessage({type:"NATIVE_METHOD_CALL",method:e,params:t,messageId:i},"*"),setTimeout((function(){a.messageHandlers.has(i)&&(a.messageHandlers.delete(i),o(new Error("调用 ".concat(e," 超时"))))}),n)}else o(new Error("postMessage 不可用"))}))}},{key:"ready",value:function(e){this.isReady?e():this.readyCallbacks.push(e)}},{key:"getNativeInfo",value:function(){var e={available:[],methods:{}};return["android","Android","AndroidInterface","NativeInterface","JSBridge","WebViewJavaScriptBridge"].forEach((function(t){window[t]&&(e.available.push(t),"object"===(0,i.default)(window[t])&&(e.methods[t]=Object.getOwnPropertyNames(window[t]).filter((function(e){return"function"===typeof window[t][e]}))))})),e}},{key:"getToken",value:function(){var e=(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.callNativeMethod("getToken"));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getDeviceInfo",value:function(){var e=(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.callNativeMethod("getDeviceInfo"));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getUserInfo",value:function(){var e=(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.callNativeMethod("getUserInfo"));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"closePage",value:function(){var e=(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.callNativeMethod("closePage"));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"goBack",value:function(){var e=(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.callNativeMethod("goBack"));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"showToast",value:function(){var e=(0,o.default)((0,r.default)().mark((function e(t){var n,a=arguments;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=a.length>1&&void 0!==a[1]?a[1]:2e3,e.abrupt("return",this.callNativeMethod("showToast",{message:t,duration:n}));case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),t}(),d=new u,l=d;t.default=l,"undefined"!==typeof window&&(window.nativeBridge=d)}).call(this,n("ba7c")["default"])},bb04:function(e,t,n){"use strict";(function(e){n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("c9b5"),n("bf0f"),n("ab80"),n("2797"),n("18f7"),n("de6c"),n("2425"),n("5c47"),n("af8f"),n("23f4"),n("7d2f"),n("9c4e"),n("2c10");var r=a(n("2634")),o=a(n("2fdc")),i=a(n("570a")),c=a(n("b692")),s={data:function(){return{h5code:"",appToken:"",showCompanysFlag:!1,type:""}},computed:{currentUrl:function(){return window.location.href}},onLoad:function(t){e.log("onLoad 接收到的参数:",t),this.initPageData(t)},onShow:function(){this.h5code||this.appToken||this.initPageData()},methods:{initPageData:function(){var t=arguments,n=this;return(0,o.default)((0,r.default)().mark((function a(){var o,i;return(0,r.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return o=t.length>0&&void 0!==t[0]?t[0]:{},e.log("初始化页面数据:",o),n.type=o.type||"",a.next=5,n.tryGetTokenFromAndroid();case 5:n.h5code=o.code||"",i=o.token||"",i&&(n.appToken=i),e.log("解析后的参数:",{code:n.h5code,appToken:n.appToken,type:n.type}),n.h5code||n.appToken?n.getAccessToken():(e.warn("缺少必要的参数，无法进行授权"),uni.showToast({title:"缺少授权参数",icon:"none"}));case 10:case"end":return a.stop()}}),a)})))()},tryGetTokenFromAndroid:function(){var t=this;return(0,o.default)((0,r.default)().mark((function n(){var a;return(0,r.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.prev=0,e.log("尝试从 Android 原生获取 token..."),n.next=4,c.default.getToken();case 4:if(a=n.sent,!a){n.next=9;break}return t.appToken=a,e.log("从 Android 获取到 token:",a),n.abrupt("return",!0);case 9:return e.log("未从 Android 获取到 token"),n.abrupt("return",!1);case 13:return n.prev=13,n.t0=n["catch"](0),e.error("从 Android 获取 token 失败:",n.t0),n.abrupt("return",!1);case 17:case"end":return n.stop()}}),n,null,[[0,13]])})))()},testAndroidToken:function(){var t=this;return(0,o.default)((0,r.default)().mark((function n(){var a;return(0,r.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e.log("=== 测试获取 Android Token ==="),n.prev=1,n.next=4,c.default.getToken();case 4:a=n.sent,a?(t.appToken=a,uni.showToast({title:"获取到 Token: ".concat(a.toString().substring(0,10),"..."),icon:"success"})):uni.showToast({title:"未获取到 Token",icon:"none"}),n.next=12;break;case 8:n.prev=8,n.t0=n["catch"](1),e.error("获取 Token 失败:",n.t0),uni.showToast({title:"获取 Token 失败",icon:"error"});case 12:case"end":return n.stop()}}),n,null,[[1,8]])})))()},testNativeMethod:function(){return(0,o.default)((0,r.default)().mark((function t(){var n;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.log("=== 测试其他原生方法 ==="),t.prev=1,t.next=4,c.default.getDeviceInfo();case 4:n=t.sent,e.log("设备信息:",n),uni.showToast({title:"调用成功，查看控制台",icon:"success"}),t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](1),e.error("调用失败:",t.t0),uni.showToast({title:"调用失败",icon:"error"});case 13:case"end":return t.stop()}}),t,null,[[1,9]])})))()},checkNativeObjects:function(){e.log("=== 检查可用的原生对象 ===");var t=c.default.getNativeInfo();e.log("原生对象信息:",t),uni.showToast({title:"发现 ".concat(t.available.length," 个原生对象"),icon:t.available.length>0?"success":"none"}),t.available.forEach((function(n){e.log("原生对象 ".concat(n," 的方法:"),t.methods[n])}))},getAccessToken:function(){var t=this;if(e.log("获取token",this.h5code,this.appToken),!this.h5code&&!this.appToken)return e.error("缺少必要的授权参数"),void uni.showToast({title:"授权参数错误",icon:"none"});i.default.wxWorkAuthAction({code:this.h5code,appToken:this.appToken}).then((function(n){e.log("获取token",n),200===n.status?(n.data.userToken&&uni.setStorageSync("userToken",n.data.userToken),uni.showToast({title:n.message,icon:"success"}),t.goHome()):(uni.showToast({title:n.message,icon:"none"}),setTimeout((function(){uni.navigateTo({url:"/pages/login/login"})}),1e3))})).catch((function(t){e.error("授权请求失败:",t),uni.showToast({title:"授权失败，请重试",icon:"none"})}))},getUrlParameter:function(t){e.log("解析URL参数:",t),e.log("当前location:",window.location);try{if("undefined"!==typeof URLSearchParams&&window.location.search){var n=new URLSearchParams(window.location.search),a=n.get(t);return e.log("参数 ".concat(t," 的值:"),a),a}var r=new RegExp("(^|&)"+t+"=([^&]*)(&|$)","i"),o=window.location.search.substring(1).match(r);if(null!=o){var i=decodeURIComponent(o[2]);return e.log("参数 ".concat(t," 的值:"),i),i}}catch(c){e.error("解析URL参数失败:",c)}return e.log("未找到参数: ".concat(t)),null},goHome:function(){this.showCompanysFlag=!1,this.$store.dispatch("/user/loginVerification"),setTimeout((function(){uni.reLaunch({url:"/pages/index/index"})}),1e3)}}};t.default=s}).call(this,n("ba7c")["default"])},e85f:function(e,t,n){var a=n("c86c");t=a(!1),t.push([e.i,".public-web-container[data-v-292f77c2]{display:flex;flex-direction:column;align-items:center;padding:20px}.debug-info[data-v-292f77c2]{margin-top:20px;padding:15px;background-color:#f5f5f5;border-radius:8px;font-size:12px;text-align:left;width:100%;max-width:400px}.debug-info p[data-v-292f77c2]{margin:5px 0;word-break:break-all}.test-buttons[data-v-292f77c2]{margin-top:15px;display:flex;flex-direction:column;gap:10px}.test-btn[data-v-292f77c2]{padding:8px 16px;background-color:#007aff;color:#fff;border:none;border-radius:4px;font-size:12px;cursor:pointer;transition:background-color .3s}.test-btn[data-v-292f77c2]:hover{background-color:#0056b3}.test-btn[data-v-292f77c2]:active{background-color:#004085}@-webkit-keyframes breathe-data-v-292f77c2{0%{opacity:0}50%{opacity:1}100%{opacity:0}}@keyframes breathe-data-v-292f77c2{0%{opacity:0}50%{opacity:1}100%{opacity:0}}",""]),e.exports=t},ec35:function(e,t,n){var a=n("e85f");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var r=n("967d").default;r("0b448a46",a,!0,{sourceMap:!1,shadowMode:!1})},f5e3:function(e,t,n){"use strict";var a=n("ec35"),r=n.n(a);r.a}}]);