(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_lifeCycle-pages-MedicationServices-ApplyMedicationGuidance"],{"0a20":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return i}));var i={uniIcons:a("67fa").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-view",{staticClass:"uni-stat__select"},[e.label?a("span",{staticClass:"uni-label-text hide-on-phone"},[e._v(e._s(e.label+"："))]):e._e(),a("v-uni-view",{staticClass:"uni-stat-box",class:{"uni-stat__actived":e.current}},[a("v-uni-view",{staticClass:"uni-select",class:{"uni-select--disabled":e.disabled}},[a("v-uni-view",{staticClass:"uni-select__input-box",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSelector.apply(void 0,arguments)}}},[e.current?a("v-uni-view",{staticClass:"uni-select__input-text"},[e._v(e._s(e.textShow))]):a("v-uni-view",{staticClass:"uni-select__input-text uni-select__input-placeholder"},[e._v(e._s(e.typePlaceholder))]),e.current&&e.clear&&!e.disabled?a("v-uni-view",{on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.clearVal.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"clear",color:"#c0c4cc",size:"24"}})],1):a("v-uni-view",[a("uni-icons",{attrs:{type:e.showSelector?"top":"bottom",size:"14",color:"#999"}})],1)],1),e.showSelector?a("v-uni-view",{staticClass:"uni-select--mask",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSelector.apply(void 0,arguments)}}}):e._e(),e.showSelector?a("v-uni-view",{staticClass:"uni-select__selector",style:e.getOffsetByPlacement},[a("v-uni-view",{class:"bottom"==e.placement?"uni-popper__arrow_bottom":"uni-popper__arrow_top"}),a("v-uni-scroll-view",{staticClass:"uni-select__selector-scroll",attrs:{"scroll-y":"true"}},[0===e.mixinDatacomResData.length?a("v-uni-view",{staticClass:"uni-select__selector-empty"},[a("v-uni-text",[e._v(e._s(e.emptyTips))])],1):e._l(e.mixinDatacomResData,(function(t,i){return a("v-uni-view",{key:i,staticClass:"uni-select__selector-item",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.change(t)}}},[a("v-uni-text",{class:{"uni-select__selector__disabled":t.disable}},[e._v(e._s(e.formatItemName(t)))])],1)}))],2)],1):e._e()],1)],1)],1)},o=[]},1090:function(e,t,a){"use strict";a.r(t);var i=a("0a20"),n=a("9028");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("9ddd");var r=a("828b"),l=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"974dabca",null,!1,i["a"],void 0);t["default"]=l.exports},"193e":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,".grace-body[data-v-04c59af6]{min-height:calc(100vh - %?120?%);padding-top:%?30?%;background-color:#f6f6f6}.base-info .form-group[data-v-04c59af6]{margin-bottom:20px}uni-label[data-v-04c59af6]{display:block;font-size:14px;color:#333;margin-bottom:8px}.picker-input[data-v-04c59af6]{width:100%;height:40px;line-height:40px;border:1px solid #e5e5e5;border-radius:4px;padding:0 12px;font-size:14px}.allergy-textarea[data-v-04c59af6]{width:100%;height:120px;border:1px solid #e5e5e5;border-radius:4px;font-size:14px;resize:none}.history-section[data-v-04c59af6]{margin-top:30px}.medical-list[data-v-04c59af6]{height:150px;\n\t/* 控制滚动区域高度 */border:1px solid #e5e5e5;border-radius:4px;font-size:14px}.medical-item[data-v-04c59af6]{display:flex;justify-content:space-between;align-items:center;padding:10px;border-bottom:1px solid #e5e5e5}.delete-btn[data-v-04c59af6]{color:#ff4d4f;font-size:14px}.submit-btn[data-v-04c59af6]{width:100%;height:48px;line-height:48px;background-color:#1890ff;color:#fff;font-size:16px;border-radius:4px;margin-top:30px}",""]),e.exports=t},2246:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("0518")),o={followupRecordList:function(e){return(0,n.default)({url:"manage/rehab/followupRecordList",method:"get",data:e})},treatmentInformationList:function(e){return(0,n.default)({url:"manage/rehab/treatmentInformationList",method:"get",data:e})},treatmentInformationDetail:function(e){return(0,n.default)({url:"manage/rehab/treatmentInformationDetail",method:"get",data:e})},medicationGuidanceList:function(e){return(0,n.default)({url:"manage/rehab/medicationGuidanceList",method:"get",data:e})},medicationGuidanceDetail:function(e){return(0,n.default)({url:"manage/rehab/medicationGuidanceDetail",method:"get",data:e})},recoveryInfo:function(e){return(0,n.default)({url:"manage/rehab/recoveryInfo",method:"get",data:e})},recoveryInfoUpload:function(e){return(0,n.default)({url:"manage/rehab/recoveryInfoUpload",method:"get",data:e})},personnel:function(e){return(0,n.default)({url:"manage/rehab/personnel",method:"get",data:e})},station:function(e){return(0,n.default)({url:"manage/rehab/station",method:"get",data:e})},appointment:function(e){return(0,n.default)({url:"manage/rehab/appointment",method:"get",data:e})},createAppointment:function(e){return(0,n.default)({url:"manage/rehab/createAppointment",method:"post",data:e})},createRehabGuideApplication:function(e){return(0,n.default)({url:"manage/rehab/createRehabGuideApplication",method:"post",data:e})},getDiseaseClassify:function(e){return(0,n.default)({url:"manage/eHealthRecord/getDiseaseClassify",method:"get",data:e})},medicationGuidanceApply:function(e){return(0,n.default)({url:"manage/rehab/medicationGuidanceApply",method:"get",data:e})},medicationGuidanceApplyAdd:function(e){return(0,n.default)({url:"manage/rehab/medicationGuidanceApplyAdd",method:"post",data:e})},medicationGuidanceApplyFileDelete:function(e){return(0,n.default)({url:"manage/rehab/medicationGuidanceApplyFileDelete",method:"delete",data:e})},medicationGuidanceApplyDetail:function(e){return(0,n.default)({url:"manage/rehab/medicationGuidanceApplyDetail",method:"get",data:e})},medicationGuidanceApplyCancel:function(e){return(0,n.default)({url:"manage/rehab/medicationGuidanceApplyCancel",method:"delete",data:e})},getMedicationGuidanceDetail:function(e){return(0,n.default)({url:"manage/rehab/getMedicationGuidanceDetail",method:"get",data:e})},informationNewsPagePatient:function(e){return(0,n.default)({url:"manage/rehab/informationNewsPagePatient",method:"get",data:e})},informationNewsDetail:function(e){return(0,n.default)({url:"manage/rehab/informationNewsDetail",method:"get",data:e})},rehabGuidanceApplyPage:function(e){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyPage",method:"get",data:e})},rehabGuidanceApplyAdd:function(e){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyAdd",method:"post",data:e})},rehabGuidanceApplyFileDelete:function(e){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyFileDelete",method:"post",data:e})},rehabGuidanceApplyDetail:function(e){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyDetail",method:"get",data:e})},rehabGuidanceApplyCancel:function(e){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyCancel",method:"delete",data:e})},informationNewsRehabPagePatient:function(e){return(0,n.default)({url:"manage/rehab/informationNewsRehabPagePatient",method:"get",data:e})},rehabGuidanceApplyExportPatient:function(e){return(0,n.default)({url:"manage/rehab/rehabGuidanceApplyExportPatient",method:"get",data:e})}},r=o;t.default=r},4408:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return i}));var i={gracePage:a("1367").default,uniDataSelect:a("1090").default,uniFilePicker:a("b14a").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF"}},[a("my-header",{attrs:{slot:"gHeader",title:"申请用药指导"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body container",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"base-info"},[a("v-uni-view",{staticClass:"form-group"},[a("v-uni-label",[e._v("职业病类型")]),a("uni-data-select",{staticStyle:{"background-color":"#fff"},attrs:{localdata:e.categories},model:{value:e.postForm.diseaseCode,callback:function(t){e.$set(e.postForm,"diseaseCode",t)},expression:"postForm.diseaseCode"}})],1),a("v-uni-view",{staticClass:"form-group"},[a("v-uni-label",[e._v("过敏史")]),a("v-uni-textarea",{staticClass:"allergy-textarea",attrs:{placeholder:"默认填充个人信息中的过敏史，可编辑",maxlength:300},model:{value:e.postForm.allergyHistory,callback:function(t){e.$set(e.postForm,"allergyHistory",t)},expression:"postForm.allergyHistory"}})],1)],1),a("v-uni-view",{staticClass:"history-section"},[a("v-uni-text",[e._v("上传病历")]),a("uni-form-item",{attrs:{label:"",prop:"annex_application"}},[a("uni-file-picker",{ref:"annex_photo",attrs:{fileMediatype:"all",autoUpload:!1,"preview-fullimage":!0},on:{select:function(t){arguments[0]=t=e.$handleEvent(t),e.handleFileSelect.apply(void 0,arguments)},delete:function(t){arguments[0]=t=e.$handleEvent(t),e.handleFileDelete.apply(void 0,arguments)}},model:{value:e.postForm.fileList,callback:function(t){e.$set(e.postForm,"fileList",t)},expression:"postForm.fileList"}})],1)],1),a("v-uni-button",{staticClass:"submit-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleSubmit.apply(void 0,arguments)}}},[e._v("提交")])],1)],1)},o=[]},"444a":function(e,t,a){"use strict";var i=a("5950"),n=a.n(i);n.a},4468:function(e,t,a){var i=a("867c");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("6f184da2",i,!0,{sourceMap:!1,shadowMode:!1})},5950:function(e,t,a){var i=a("193e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("0e5a2b3f",i,!0,{sourceMap:!1,shadowMode:!1})},"60db":function(e,t,a){e.exports=a.p+"assets/uni.75745d34.ttf"},"67fa":function(e,t,a){"use strict";a.r(t);var i=a("df08"),n=a("8839");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("69f7");var r=a("828b"),l=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"5c0264f4",null,!1,i["a"],void 0);t["default"]=l.exports},"69f7":function(e,t,a){"use strict";var i=a("cf5b"),n=a.n(i);n.a},"867c":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'@charset "UTF-8";@media screen and (max-width:500px){.hide-on-phone[data-v-974dabca]{display:none}}.uni-stat__select[data-v-974dabca]{display:flex;align-items:center;cursor:pointer;width:100%;flex:1;box-sizing:border-box}.uni-stat-box[data-v-974dabca]{background-color:#fff;width:100%;flex:1}.uni-stat__actived[data-v-974dabca]{width:100%;flex:1}.uni-label-text[data-v-974dabca]{font-size:14px;font-weight:700;color:#6a6a6a;margin:auto 0;margin-right:5px}.uni-select[data-v-974dabca]{font-size:14px;border:1px solid #e5e5e5;box-sizing:border-box;border-radius:4px;padding:0 5px;padding-left:10px;position:relative;display:flex;-webkit-user-select:none;user-select:none;flex-direction:row;align-items:center;border-bottom:solid 1px #e5e5e5;width:100%;flex:1;height:35px}.uni-select--disabled[data-v-974dabca]{background-color:#f5f7fa;cursor:not-allowed}.uni-select__label[data-v-974dabca]{font-size:16px;height:35px;padding-right:10px;color:#909399}.uni-select__input-box[data-v-974dabca]{height:35px;width:0;position:relative;display:flex;flex:1;flex-direction:row;align-items:center}.uni-select__input[data-v-974dabca]{flex:1;font-size:14px;height:22px;line-height:22px}.uni-select__input-plac[data-v-974dabca]{font-size:14px;color:#909399}.uni-select__selector[data-v-974dabca]{box-sizing:border-box;position:absolute;left:0;width:100%;background-color:#fff;border:1px solid #ebeef5;border-radius:6px;box-shadow:0 2px 12px 0 rgba(0,0,0,.1);z-index:3;padding:4px 0}.uni-select__selector-scroll[data-v-974dabca]{max-height:200px;box-sizing:border-box}@media (min-width:768px){.uni-select__selector-scroll[data-v-974dabca]{max-height:600px}}.uni-select__selector-empty[data-v-974dabca],\n.uni-select__selector-item[data-v-974dabca]{display:flex;cursor:pointer;line-height:35px;font-size:14px;text-align:center;\n  /* border-bottom: solid 1px $uni-border-3; */padding:0 10px}.uni-select__selector-item[data-v-974dabca]:hover{background-color:#f9f9f9}.uni-select__selector-empty[data-v-974dabca]:last-child,\n.uni-select__selector-item[data-v-974dabca]:last-child{border-bottom:none}.uni-select__selector__disabled[data-v-974dabca]{opacity:.4;cursor:default}\n/* picker 弹出层通用的指示小三角 */.uni-popper__arrow_bottom[data-v-974dabca],\n.uni-popper__arrow_bottom[data-v-974dabca]::after,\n.uni-popper__arrow_top[data-v-974dabca],\n.uni-popper__arrow_top[data-v-974dabca]::after{position:absolute;display:block;width:0;height:0;border-color:transparent;border-style:solid;border-width:6px}.uni-popper__arrow_bottom[data-v-974dabca]{-webkit-filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));top:-6px;left:10%;margin-right:3px;border-top-width:0;border-bottom-color:#ebeef5}.uni-popper__arrow_bottom[data-v-974dabca]::after{content:" ";top:1px;margin-left:-6px;border-top-width:0;border-bottom-color:#fff}.uni-popper__arrow_top[data-v-974dabca]{-webkit-filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));bottom:-6px;left:10%;margin-right:3px;border-bottom-width:0;border-top-color:#ebeef5}.uni-popper__arrow_top[data-v-974dabca]::after{content:" ";bottom:1px;margin-left:-6px;border-bottom-width:0;border-top-color:#fff}.uni-select__input-text[data-v-974dabca]{width:100%;color:#333;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis;overflow:hidden}.uni-select__input-placeholder[data-v-974dabca]{color:#6a6a6a;font-size:12px}.uni-select--mask[data-v-974dabca]{position:fixed;top:0;bottom:0;right:0;left:0;z-index:2}',""]),e.exports=t},8839:function(e,t,a){"use strict";a.r(t);var i=a("f10a"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},"8f0c":function(e,t,a){"use strict";a.r(t);var i=a("4408"),n=a("d98f");for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);a("444a");var r=a("828b"),l=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"04c59af6",null,!1,i["a"],void 0);t["default"]=l.exports},9028:function(e,t,a){"use strict";a.r(t);var i=a("a332"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},"9ddd":function(e,t,a){"use strict";var i=a("4468"),n=a.n(i);n.a},a332:function(e,t,a){"use strict";(function(e){a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa"),a("aa77"),a("bf0f"),a("2797"),a("5c47"),a("a1c1"),a("23f4"),a("7d2f"),a("9c4e"),a("ab80"),a("5ef2"),a("c223");var i={name:"uni-data-select",mixins:[e.mixinDatacom||{}],props:{localdata:{type:Array,default:function(){return[]}},value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"请选择"},emptyTips:{type:String,default:"无选项"},clear:{type:Boolean,default:!0},defItem:{type:Number,default:0},disabled:{type:Boolean,default:!1},format:{type:String,default:""},placement:{type:String,default:"bottom"}},data:function(){return{showSelector:!1,current:"",mixinDatacomResData:[],apps:[],channels:[],cacheKey:"uni-data-select-lastSelectedValue"}},created:function(){var e=this;this.debounceGet=this.debounce((function(){e.query()}),300),this.collection&&!this.localdata.length&&this.debounceGet()},computed:{typePlaceholder:function(){var e=this.placeholder,t={"opendb-stat-app-versions":"版本","opendb-app-channels":"渠道","opendb-app-list":"应用"}[this.collection];return t?e+t:e},valueCom:function(){return this.value},textShow:function(){var e=this.current;return e},getOffsetByPlacement:function(){switch(this.placement){case"top":return"bottom:calc(100% + 12px);";case"bottom":return"top:calc(100% + 12px);"}}},watch:{localdata:{immediate:!0,handler:function(e,t){Array.isArray(e)&&t!==e&&(this.mixinDatacomResData=e)}},valueCom:function(e,t){this.initDefVal()},mixinDatacomResData:{immediate:!0,handler:function(e){e.length&&this.initDefVal()}}},methods:{debounce:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,a=null;return function(){for(var i=this,n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];a&&clearTimeout(a),a=setTimeout((function(){e.apply(i,o)}),t)}},query:function(){this.mixinDatacomEasyGet()},onMixinDatacomPropsChange:function(){this.collection&&this.debounceGet()},initDefVal:function(){var e="";if(!this.valueCom&&0!==this.valueCom||this.isDisabled(this.valueCom)){var t;if(this.collection&&(t=this.getCache()),t||0===t)e=t;else{var a="";this.defItem>0&&this.defItem<=this.mixinDatacomResData.length&&(a=this.mixinDatacomResData[this.defItem-1].value),e=a}(e||0===e)&&this.emit(e)}else e=this.valueCom;var i=this.mixinDatacomResData.find((function(t){return t.value===e}));this.current=i?this.formatItemName(i):""},isDisabled:function(e){var t=!1;return this.mixinDatacomResData.forEach((function(a){a.value===e&&(t=a.disable)})),t},clearVal:function(){this.emit(""),this.collection&&this.removeCache()},change:function(e){e.disable||(this.showSelector=!1,this.current=this.formatItemName(e),this.emit(e.value))},emit:function(e){this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("change",e),this.collection&&this.setCache(e)},toggleSelector:function(){this.disabled||(this.showSelector=!this.showSelector)},formatItemName:function(e){var t=e.text,a=e.value,i=e.channel_code;if(i=i?"(".concat(i,")"):"",this.format){var n="";for(var o in n=this.format,e)n=n.replace(new RegExp("{".concat(o,"}"),"g"),e[o]);return n}return this.collection.indexOf("app-list")>0?"".concat(t,"(").concat(a,")"):t||"未命名".concat(i)},getLoadData:function(){return this.mixinDatacomResData},getCurrentCacheKey:function(){return this.collection},getCache:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getCurrentCacheKey(),t=uni.getStorageSync(this.cacheKey)||{};return t[e]},setCache:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getCurrentCacheKey(),a=uni.getStorageSync(this.cacheKey)||{};a[t]=e,uni.setStorageSync(this.cacheKey,a)},removeCache:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getCurrentCacheKey(),t=uni.getStorageSync(this.cacheKey)||{};delete t[e],uni.setStorageSync(this.cacheKey,t)}}};t.default=i}).call(this,a("861b")["uniCloud"])},a922:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},cc46:function(e,t,a){"use strict";(function(e){a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("fd3c"),a("dd2b"),a("aa9c"),a("bd06");var n,o=i(a("39d8")),r=i(a("9b1b")),l=i(a("3471")),c=i(a("2634")),d=i(a("2fdc")),s=i(a("2246")),u=i(a("7703")),f={data:function(){return{typeList:["过敏性鼻炎","其他"],typeIndex:0,medicalRecords:[],postForm:{allergyHistory:"",diseaseCode:"",fileList:[],idCardCode:"",patientAreaCode:"",patientName:""},categories:[]}},created:function(){this.getDiseaseClassifyList()},methods:(n={getDiseaseClassifyList:function(){var e=this;return(0,d.default)((0,c.default)().mark((function t(){var a;return(0,c.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,s.default.getDiseaseClassify();case 2:a=t.sent,e.categories=a.data.map((function(e){return{id:e.id,value:e.code,text:e.name}}));case 4:case"end":return t.stop()}}),t)})))()},handleDelete:function(e){this.medicalRecords.splice(e,1)},handleFileSelect:function(t){var a=this;return(0,d.default)((0,c.default)().mark((function i(){var n,o,r,d,s,f;return(0,c.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:n=t.tempFiles,o=(0,l.default)(n),i.prev=2,o.s();case 4:if((r=o.n()).done){i.next=21;break}return d=r.value,i.prev=6,i.next=9,uni.uploadFile({url:u.default.apiServer+"manage/rehab/medicationGuidanceApplyFileUpload",filePath:d.path,name:"file",header:{Authorization:uni.getStorageSync("userToken")}});case 9:s=i.sent,f=JSON.parse(s&&s[1].data),200===f.data.status?(a.postForm.fileList.push({name:d.name,url:f.data.data.url,fileId:f.data.data.id}),uni.showToast({title:"上传成功",icon:"success"})):uni.showToast({title:"上传失败",icon:"none"}),i.next=19;break;case 14:i.prev=14,i.t0=i["catch"](6),a.postForm.fileList=[],e.error("文件上传失败:",i.t0),uni.showToast({title:"上传失败",icon:"none"});case 19:i.next=4;break;case 21:i.next=26;break;case 23:i.prev=23,i.t1=i["catch"](2),o.e(i.t1);case 26:return i.prev=26,o.f(),i.finish(26);case 29:case"end":return i.stop()}}),i,null,[[2,23,26,29],[6,14]])})))()}},(0,o.default)(n,"handleDelete",(function(t){var a=this;return(0,d.default)((0,c.default)().mark((function i(){var n;return(0,c.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:n=t.tempFile,uni.showModal({title:"确认删除",content:"您确定要删除这个文件吗？",success:function(){var t=(0,d.default)((0,c.default)().mark((function t(i){var o,r;return(0,c.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!i.confirm){t.next=16;break}if(n.fileId){t.next=3;break}return t.abrupt("return");case 3:return t.prev=3,t.next=6,s.default.medicationGuidanceApplyFileDelete({id:n.fileId});case 6:o=t.sent,o?(r=a.postForm.fileList.findIndex((function(e){return e.fileId===deletedFile.fileId})),-1!==r&&a.postForm.fileList.splice(r,1),uni.showToast({title:"删除成功"})):uni.showToast({title:"删除失败",icon:"none"}),t.next=14;break;case 10:t.prev=10,t.t0=t["catch"](3),e.error("文件删除失败:",t.t0),uni.showToast({title:"删除失败",icon:"none"});case 14:t.next=17;break;case 16:i.cancel&&e.log("用户取消了删除操作");case 17:case"end":return t.stop()}}),t,null,[[3,10]])})));return function(e){return t.apply(this,arguments)}}()});case 2:case"end":return i.stop()}}),i)})))()})),(0,o.default)(n,"handleSubmit",(function(){var e=this;return(0,d.default)((0,c.default)().mark((function t(){var a,i;return(0,c.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.postForm.diseaseCode){t.next=2;break}return t.abrupt("return",uni.showToast({title:"请选择职业病类型",icon:"none"}));case 2:if(e.postForm.allergyHistory){t.next=4;break}return t.abrupt("return",uni.showToast({title:"请填写过敏史",icon:"none"}));case 4:if(e.postForm.fileList.length){t.next=6;break}return t.abrupt("return",uni.showToast({title:"请上传病历",icon:"none"}));case 6:return a=(0,r.default)({},e.postForm),a.fileList=a.fileList.map((function(e){return{id:e.id,fileName:e.name,fileUrl:e.url}})),t.prev=8,t.next=11,s.default.medicationGuidanceApplyAdd(a);case 11:i=t.sent,i?(uni.showToast({title:"提交成功",icon:"success"}),setTimeout((function(){uni.navigateBack()}),1e3)):uni.showToast({title:"提交失败",icon:"error"}),t.next=18;break;case 15:t.prev=15,t.t0=t["catch"](8),uni.showToast({title:t.t0.message,icon:"error"});case 18:case"end":return t.stop()}}),t,null,[[8,15]])})))()})),n)};t.default=f}).call(this,a("ba7c")["default"])},cf5b:function(e,t,a){var i=a("f103");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("c24910a6",i,!0,{sourceMap:!1,shadowMode:!1})},d98f:function(e,t,a){"use strict";a.r(t);var i=a("cc46"),n=a.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);t["default"]=n.a},df08:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-text",{staticClass:"uni-icons",class:[e.customIcons,e.customIcons?e.type:""],style:{color:e.color,"font-size":e.size+"px"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e._onClick.apply(void 0,arguments)}}},[e._v(e._s(e.icons[e.type]))])},n=[]},f103:function(e,t,a){var i=a("c86c"),n=a("2ec5"),o=a("60db");t=i(!1);var r=n(o);t.push([e.i,"@font-face{font-family:uniicons;src:url("+r+') format("truetype")}.uni-icons[data-v-5c0264f4]{font-family:uniicons;text-decoration:none;text-align:center}',""]),e.exports=t},f10a:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa");var n=i(a("a922")),o={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:n.default}},methods:{_onClick:function(){this.$emit("click")}}};t.default=o}}]);