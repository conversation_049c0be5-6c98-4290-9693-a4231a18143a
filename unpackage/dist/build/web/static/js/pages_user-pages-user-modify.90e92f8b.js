(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-user-modify"],{"1aed":function(e,t,s){var a=s("f064");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var o=s("967d").default;o("091fb799",a,!0,{sourceMap:!1,shadowMode:!1})},"65f5":function(e,t,s){"use strict";s.d(t,"b",(function(){return o})),s.d(t,"c",(function(){return n})),s.d(t,"a",(function(){return a}));var a={gracePage:s("1367").default},o=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[s("my-header",{attrs:{slot:"gHeader",title:"修改密码"},slot:"gHeader"}),s("v-uni-view",{staticClass:"grace-body",attrs:{slot:"gBody"},slot:"gBody"},[s("v-uni-form",{staticClass:"grace-form",staticStyle:{"margin-top":"80rpx"},on:{submit:function(t){arguments[0]=t=e.$handleEvent(t),e.formSubmit.apply(void 0,arguments)}}},[s("v-uni-view",{staticClass:"grace-form-item grace-border-b"},[s("v-uni-text",{staticClass:"grace-form-label"},[e._v("新密码：")]),s("v-uni-view",{staticClass:"grace-form-body"},["password"==e.pwdType?s("v-uni-input",{staticClass:"grace-form-input",attrs:{type:"password",name:"pwd",placeholder:"请输入"},model:{value:e.mobile.passwordOne,callback:function(t){e.$set(e.mobile,"passwordOne","string"===typeof t?t.trim():t)},expression:"mobile.passwordOne"}}):e._e(),"text"==e.pwdType?s("v-uni-input",{staticClass:"grace-form-input",attrs:{type:"text",name:"pwd",placeholder:"请输入"},model:{value:e.mobile.passwordOne,callback:function(t){e.$set(e.mobile,"passwordOne","string"===typeof t?t.trim():t)},expression:"mobile.passwordOne"}}):e._e()],1),""!=e.mobile.passwordOne?s("v-uni-text",{staticClass:"grace-icons icon-close grace-form-icon grace-text-center",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.mobile.passwordOne=""}}}):e._e(),""!=e.mobile.passwordOne?s("v-uni-text",{staticClass:"grace-icons icon-eye grace-form-icon grace-text-center",class:["text"==e.pwdType?"grace-blue":""],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showPwd(1)}}}):e._e()],1),s("v-uni-view",{staticClass:"grace-form-item grace-border-b"},[s("v-uni-text",{staticClass:"grace-form-label"},[e._v("确认密码：")]),s("v-uni-view",{staticClass:"grace-form-body"},["password"==e.pwdType2?s("v-uni-input",{staticClass:"grace-form-input",attrs:{type:"password",name:"pwd2",placeholder:"请输入"},model:{value:e.mobile.passwordTwo,callback:function(t){e.$set(e.mobile,"passwordTwo","string"===typeof t?t.trim():t)},expression:"mobile.passwordTwo"}}):e._e(),"text"==e.pwdType2?s("v-uni-input",{staticClass:"grace-form-input",attrs:{type:"text",name:"pwd2",placeholder:"请输入"},model:{value:e.mobile.passwordTwo,callback:function(t){e.$set(e.mobile,"passwordTwo","string"===typeof t?t.trim():t)},expression:"mobile.passwordTwo"}}):e._e()],1),""!=e.mobile.passwordTwo?s("v-uni-text",{staticClass:"grace-icons icon-close grace-form-icon grace-text-center",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.mobile.passwordTwo=""}}}):e._e(),""!=e.mobile.passwordTwo?s("v-uni-text",{staticClass:"grace-icons icon-eye grace-form-icon grace-text-center",class:["text"==e.pwdType?"grace-blue":""],on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.showPwd(2)}}}):e._e()],1),s("br"),s("br"),s("v-uni-view",{staticClass:"grace-margin-top"},[s("v-uni-button",{staticClass:"grace-button grace-border-radius grace-gtbg-blue",staticStyle:{"line-height":"80rpx"},attrs:{"form-type":"submit",type:"primary"}},[e._v("提交")])],1)],1)],1)],1)},n=[]},"746b":function(e,t,s){"use strict";s("6a54");var a=s("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=a(s("9b1b")),n=a(s("570a")),i=s("8f59"),r={data:function(){return{pwdType:"password",pwdType2:"password",mobile:{passwordOne:"",passwordTwo:""},passwordReg:/^\w+$/,modifyMobile:!1}},computed:(0,o.default)({},(0,i.mapGetters)({userInfo:"userInfo"})),methods:{showPwd:function(e){1==e?this.pwdType="password"==this.pwdType?"text":"password":this.pwdType2="password"==this.pwdType2?"text":"password"},formSubmit:function(){this.handlerCheck(this.mobile),this.modifyMobile&&n.default.updateInfo({password:this.mobile.passwordOne}).then((function(e){200==e.status?(uni.showModal({content:"密码修改成功",showCancel:!1}),setTimeout((function(){uni.navigateBack({})}),1500)):uni.showToast({title:e.message,icon:"none"})}))},handlerCheck:function(e){e.passwordOne?e.passwordOne===e.passwordTwo?e.passwordOne.length<6?uni.showToast({title:"密码不能小于6位字符",icon:"none"}):this.modifyMobile=!0:uni.showModal({content:"新密码前后不一致，请重新输入",showCancel:!1}):uni.showToast({title:"请输入密码",icon:"none"})}}};t.default=r},8272:function(e,t,s){"use strict";var a=s("1aed"),o=s.n(a);o.a},db1a:function(e,t,s){"use strict";s.r(t);var a=s("746b"),o=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return a[e]}))}(n);t["default"]=o.a},e889:function(e,t,s){"use strict";s.r(t);var a=s("65f5"),o=s("db1a");for(var n in o)["default"].indexOf(n)<0&&function(e){s.d(t,e,(function(){return o[e]}))}(n);s("8272");var i=s("828b"),r=Object(i["a"])(o["default"],a["b"],a["c"],!1,null,"0cffe58f",null,!1,a["a"],void 0);t["default"]=r.exports},f064:function(e,t,s){var a=s("c86c");t=a(!1),t.push([e.i,".grace-form-label[data-v-0cffe58f]{min-width:calc(5em + 15px)}",""]),e.exports=t}}]);