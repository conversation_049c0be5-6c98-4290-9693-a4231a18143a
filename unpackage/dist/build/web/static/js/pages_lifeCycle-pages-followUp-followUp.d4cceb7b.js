(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_lifeCycle-pages-followUp-followUp"],{"11e6":function(e,t,a){"use strict";(function(e){a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("2634")),o=n(a("9b1b")),s=n(a("2fdc")),r=n(a("2246")),c={name:"followUp",data:function(){return{formDate:{range:"",physician:""},pageParams:{pageNum:1,pageSize:9999,isAsc:"desc",orderBy:"createTime"},recordsList:[]}},onLoad:function(){this.getRecordsList()},methods:{getRecordsList:function(){var t=this;return(0,s.default)((0,i.default)().mark((function a(){var n,s,c,l;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,c=(0,o.default)((0,o.default)({},t.pageParams),{},{followupPerson:t.formDate.physician||void 0,fromFollowupTime:(null===(n=t.formDate.range)||void 0===n?void 0:n[0])||void 0,toFollowupTime:(null===(s=t.formDate.range)||void 0===s?void 0:s[1])||void 0}),a.next=4,r.default.followupRecordList(c);case 4:l=a.sent,200===l.status&&(t.recordsList=l.data.list),a.next=11;break;case 8:a.prev=8,a.t0=a["catch"](0),e.error("获取随访记录列表失败:",a.t0);case 11:case"end":return a.stop()}}),a,null,[[0,8]])})))()},search:function(){this.pageParams.pageNum=1,this.getRecordsList(),this.$refs.popup.close()},reset:function(){this.formDate={range:"",physician:""},this.pageParams={pageNum:1,pageSize:9999,isAsc:"desc",orderBy:"createTime"},this.getRecordsList()},change:function(t){e.log(t)},openSearchPopup:function(){this.$refs.popup.open("right")}}};t.default=c}).call(this,a("ba7c")["default"])},5797:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return n}));var n={gracePage:a("1367").default,uniIcons:a("67fa").default,uniPopup:a("7ddc").default,uniDatetimePicker:a("ca85").default,uniEasyinput:a("e7ae").default},i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF"}},[a("my-header",{attrs:{slot:"gHeader",title:"随访记录"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body container",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"search"},[a("v-uni-view",{staticClass:"searchInfo",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openSearchPopup.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"search",size:"20",color:"dodgerblue"}}),a("v-uni-text",[e._v("查询")])],1)],1),a("v-uni-view",{staticClass:"search_content"},e._l(e.recordsList,(function(t){return a("v-uni-view",{key:t.id,staticClass:"records"},[a("v-uni-view",{staticClass:"text"},[a("v-uni-view",{staticClass:"label"},[e._v("随访时间:")]),a("v-uni-view",{staticClass:"content"},[e._v(e._s(t.followupPlanRowResponse?t.followupPlanRowResponse.followupTime:"-"))])],1),a("v-uni-view",{staticClass:"text"},[a("v-uni-view",{staticClass:"label"},[e._v("随访机构:")]),a("v-uni-view",{staticClass:"content"},[e._v(e._s(t.createBy))])],1),a("v-uni-view",{staticClass:"text"},[a("v-uni-view",{staticClass:"label"},[e._v("随访医师:")]),a("v-uni-view",{staticClass:"content"},[e._v(e._s(t.followupPlanRowResponse?t.followupPlanRowResponse.personnelName:"-"))])],1),a("v-uni-view",{staticClass:"text"},[a("v-uni-view",{staticClass:"label"},[e._v("随访内容:")]),a("v-uni-view",{staticClass:"content"},[e._v(e._s(t.followupPlanRowResponse?t.followupPlanRowResponse.followupPlanName:"-"))])],1)],1)})),1),a("uni-popup",{ref:"popup",staticStyle:{"z-index":"90"},attrs:{"background-color":"#fff","mask-background-color":"#0000000"},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.change.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"popup-content"},[a("v-uni-view",{staticClass:"forms"},[a("v-uni-view",{staticClass:"forms_item"},[a("v-uni-view",{staticClass:"label"},[e._v("随访时间")]),a("uni-datetime-picker",{attrs:{type:"daterange",rangeSeparator:"至"},model:{value:e.formDate.range,callback:function(t){e.$set(e.formDate,"range",t)},expression:"formDate.range"}})],1),a("v-uni-view",{staticClass:"forms_item"},[a("v-uni-view",{staticClass:"label"},[e._v("随访医师")]),a("uni-easyinput",{staticClass:"uni-mt-5",attrs:{trim:"all",placeholder:"请输入随访医师"},model:{value:e.formDate.physician,callback:function(t){e.$set(e.formDate,"physician",t)},expression:"formDate.physician"}})],1),a("v-uni-view",{staticClass:"forms_btn"},[a("v-uni-button",{attrs:{type:"default",plain:"true"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.reset.apply(void 0,arguments)}}},[e._v("重置")]),a("v-uni-button",{attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.search.apply(void 0,arguments)}}},[e._v("查询")])],1)],1)],1)],1)],1)],1)},o=[]},"8f593":function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,".grace-body[data-v-0ba8932f]{min-height:calc(100vh - %?120?%);padding-top:%?30?%;background-color:#f6f6f6}.grace-body .search[data-v-0ba8932f]{display:flex;justify-content:center;align-items:flex-end;flex-direction:column;color:#169bd5;margin-bottom:%?30?%}.grace-body .search .searchInfo[data-v-0ba8932f]{display:flex;align-items:center}.records[data-v-0ba8932f]{background-color:#fff;border-radius:%?8?%;padding:%?20?% %?60?%;margin-bottom:%?20?%}.records .text[data-v-0ba8932f]{display:flex;margin-bottom:%?6?%;align-items:center}.records .text .label[data-v-0ba8932f]{font-size:%?28?%;margin-right:%?10?%}.records .text .content[data-v-0ba8932f]{font-size:%?28?%}.popup-content[data-v-0ba8932f]{position:relative;width:70vw;height:88vh;padding:%?40?%;padding-top:%?120?%}.forms_item[data-v-0ba8932f]{margin-bottom:%?20?%}.forms_item .label[data-v-0ba8932f]{font-size:%?28?%;margin-bottom:%?20?%}.forms_item uni-input[data-v-0ba8932f]{border:1px solid #f6f6f6;border-radius:%?5?%}.forms_btn[data-v-0ba8932f]{position:absolute;bottom:5%;width:100%;left:0;display:flex;align-items:center;justify-content:space-around}.forms_btn uni-button[data-v-0ba8932f]{margin:0;font-size:%?28?%;padding:0 %?80?%}.reset_btn[data-v-0ba8932f]{background-color:#5b5b5b}.search_btn[data-v-0ba8932f]{background-color:#169bd5}",""]),e.exports=t},de6b:function(e,t,a){"use strict";a.r(t);var n=a("11e6"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(o);t["default"]=i.a},e836:function(e,t,a){"use strict";a.r(t);var n=a("5797"),i=a("de6b");for(var o in i)["default"].indexOf(o)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(o);a("eac8");var s=a("828b"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"0ba8932f",null,!1,n["a"],void 0);t["default"]=r.exports},eac8:function(e,t,a){"use strict";var n=a("eb56"),i=a.n(n);i.a},eb56:function(e,t,a){var n=a("8f593");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("71ffdb16",n,!0,{sourceMap:!1,shadowMode:!1})}}]);