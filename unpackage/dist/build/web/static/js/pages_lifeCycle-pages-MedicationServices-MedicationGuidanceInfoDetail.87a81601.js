(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_lifeCycle-pages-MedicationServices-MedicationGuidanceInfoDetail"],{"066b":function(e,t,a){var n=a("f589");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var i=a("967d").default;i("71b24b6a",n,!0,{sourceMap:!1,shadowMode:!1})},"09d5":function(e,t,a){"use strict";a.r(t);var n=a("8451"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);t["default"]=i.a},2246:function(e,t,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("0518")),r={followupRecordList:function(e){return(0,i.default)({url:"manage/rehab/followupRecordList",method:"get",data:e})},treatmentInformationList:function(e){return(0,i.default)({url:"manage/rehab/treatmentInformationList",method:"get",data:e})},treatmentInformationDetail:function(e){return(0,i.default)({url:"manage/rehab/treatmentInformationDetail",method:"get",data:e})},medicationGuidanceList:function(e){return(0,i.default)({url:"manage/rehab/medicationGuidanceList",method:"get",data:e})},medicationGuidanceDetail:function(e){return(0,i.default)({url:"manage/rehab/medicationGuidanceDetail",method:"get",data:e})},recoveryInfo:function(e){return(0,i.default)({url:"manage/rehab/recoveryInfo",method:"get",data:e})},recoveryInfoUpload:function(e){return(0,i.default)({url:"manage/rehab/recoveryInfoUpload",method:"get",data:e})},personnel:function(e){return(0,i.default)({url:"manage/rehab/personnel",method:"get",data:e})},station:function(e){return(0,i.default)({url:"manage/rehab/station",method:"get",data:e})},appointment:function(e){return(0,i.default)({url:"manage/rehab/appointment",method:"get",data:e})},createAppointment:function(e){return(0,i.default)({url:"manage/rehab/createAppointment",method:"post",data:e})},createRehabGuideApplication:function(e){return(0,i.default)({url:"manage/rehab/createRehabGuideApplication",method:"post",data:e})},getDiseaseClassify:function(e){return(0,i.default)({url:"manage/eHealthRecord/getDiseaseClassify",method:"get",data:e})},medicationGuidanceApply:function(e){return(0,i.default)({url:"manage/rehab/medicationGuidanceApply",method:"get",data:e})},medicationGuidanceApplyAdd:function(e){return(0,i.default)({url:"manage/rehab/medicationGuidanceApplyAdd",method:"post",data:e})},medicationGuidanceApplyFileDelete:function(e){return(0,i.default)({url:"manage/rehab/medicationGuidanceApplyFileDelete",method:"delete",data:e})},medicationGuidanceApplyDetail:function(e){return(0,i.default)({url:"manage/rehab/medicationGuidanceApplyDetail",method:"get",data:e})},medicationGuidanceApplyCancel:function(e){return(0,i.default)({url:"manage/rehab/medicationGuidanceApplyCancel",method:"delete",data:e})},getMedicationGuidanceDetail:function(e){return(0,i.default)({url:"manage/rehab/getMedicationGuidanceDetail",method:"get",data:e})},informationNewsPagePatient:function(e){return(0,i.default)({url:"manage/rehab/informationNewsPagePatient",method:"get",data:e})},informationNewsDetail:function(e){return(0,i.default)({url:"manage/rehab/informationNewsDetail",method:"get",data:e})},rehabGuidanceApplyPage:function(e){return(0,i.default)({url:"manage/rehab/rehabGuidanceApplyPage",method:"get",data:e})},rehabGuidanceApplyAdd:function(e){return(0,i.default)({url:"manage/rehab/rehabGuidanceApplyAdd",method:"post",data:e})},rehabGuidanceApplyFileDelete:function(e){return(0,i.default)({url:"manage/rehab/rehabGuidanceApplyFileDelete",method:"post",data:e})},rehabGuidanceApplyDetail:function(e){return(0,i.default)({url:"manage/rehab/rehabGuidanceApplyDetail",method:"get",data:e})},rehabGuidanceApplyCancel:function(e){return(0,i.default)({url:"manage/rehab/rehabGuidanceApplyCancel",method:"delete",data:e})},informationNewsRehabPagePatient:function(e){return(0,i.default)({url:"manage/rehab/informationNewsRehabPagePatient",method:"get",data:e})},rehabGuidanceApplyExportPatient:function(e){return(0,i.default)({url:"manage/rehab/rehabGuidanceApplyExportPatient",method:"get",data:e})}},o=r;t.default=o},8451:function(e,t,a){"use strict";(function(e){a("6a54");var n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(a("2246")),r=(n(a("7703")),{data:function(){return{docInfo:{name:"",publisher:"",publishTime:"",content:""}}},onLoad:function(e){this.detailId=e.id},created:function(){this.getDetailInfo()},methods:{getDetailInfo:function(){var t=this;i.default.informationNewsDetail({id:this.detailId}).then((function(e){t.docInfo=e.data})).catch((function(t){e.log(t)}))}}});t.default=r}).call(this,a("ba7c")["default"])},9961:function(e,t,a){"use strict";a.r(t);var n=a("f676"),i=a("09d5");for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);a("d87b");var o=a("828b"),d=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"4758aa07",null,!1,n["a"],void 0);t["default"]=d.exports},d87b:function(e,t,a){"use strict";var n=a("066b"),i=a.n(n);i.a},f589:function(e,t,a){var n=a("c86c");t=n(!1),t.push([e.i,".grace-body[data-v-4758aa07]{min-height:calc(100vh - %?120?%);padding-top:%?30?%\n  /* 标题区（含关注按钮） */\n  /* 发布信息栏 */\n  /* 正文区域 */\n  /* 深度选择器穿透scoped，作用于v-html内的img */}.grace-body .header[data-v-4758aa07]{position:relative;margin-bottom:%?20?%}.grace-body .title[data-v-4758aa07]{font-size:%?36?%;font-weight:700;line-height:%?50?%\n  /* 行高适配 */}.grace-body .meta-bar[data-v-4758aa07]{display:flex;justify-content:space-between;font-size:%?24?%;color:#999;margin-bottom:%?30?%;line-height:%?36?%\n  /* 行高适配 */}.grace-body .meta-item[data-v-4758aa07]{margin-right:%?20?%}.grace-body .content[data-v-4758aa07]{width:100%;font-size:%?28?%;line-height:1.6;overflow-x:hidden\n  /* 防止极端情况的水平溢出 */\n  /* 行间距适配 */}.grace-body .paragraph[data-v-4758aa07]{width:100%;display:block;margin-bottom:%?20?%;text-indent:%?56?%}.grace-body .paragraph[data-v-4758aa07]  img{max-width:100%!important;\n  /* 强制覆盖内联width */height:auto!important;\n  /* 保持宽高比 */display:block;\n  /* 独占一行，避免和文字混排 */margin:%?20?% auto\n  /* 上下间距，居中显示（可选） */}",""]),e.exports=t},f676:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return n}));var n={gracePage:a("1367").default},i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF"}},[a("my-header",{attrs:{slot:"gHeader",title:"用药指导资讯详情"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body container",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"header"},[a("v-uni-text",{staticClass:"title"},[e._v(e._s(e.docInfo.name))])],1),a("v-uni-view",{staticStyle:{"margin-bottom":"20rpx"}},[e._v("发布人："+e._s(e.docInfo.publisher))]),a("v-uni-view",{staticClass:"meta-bar"},[a("v-uni-text",{staticClass:"meta-item"},[e._v("浏览量："+e._s(e.docInfo.viewsCount))]),a("v-uni-text",{staticClass:"meta-item"},[e._v("发布时间："+e._s(e.docInfo.publishTime))])],1),a("v-uni-view",{staticClass:"content"},[e.docInfo.content?a("v-uni-view",{staticClass:"paragraph",domProps:{innerHTML:e._s(e.docInfo.content)}}):e._e()],1)],1)],1)},r=[]}}]);