(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-eHealthRecord-auth"],{"355d":function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,'@charset "UTF-8";.header[data-v-59028db3]{height:%?60?%}.grace-td[data-v-59028db3]{height:%?120?%;line-height:normal;display:flex;align-items:center;justify-content:center;\n  /* 超过两行以...显示 */overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;-webkit-line-clamp:3\n  /* 限制显示文本的行数为3行 */}.circleBtn[data-v-59028db3]{height:%?48?%;width:%?48?%;font-size:%?16?%;border-radius:99px;display:flex;align-items:center;justify-content:center;margin:0}.health-check-item[data-v-59028db3]{background-color:#fff;border-radius:%?12?%;padding:%?30?%;margin-bottom:%?20?%;box-shadow:0 2px 6px rgba(0,0,0,.04)}.health-check-item[data-v-59028db3]:first-child{margin-top:%?20?%}.health-check-item .item-header[data-v-59028db3]{display:flex;justify-content:space-between;align-items:center;padding-bottom:%?20?%;border-bottom:1px solid #f0f0f0}.health-check-item .item-header .date[data-v-59028db3]{font-size:14px;color:#666}.health-check-item .item-header .org-name[data-v-59028db3]{display:flex;align-items:center;\n  /* margin-bottom: 20rpx; */font-size:16px;color:#333}.health-check-item .item-header .org-name uni-text[data-v-59028db3]{margin-left:%?10?%}.health-check-item .item-content[data-v-59028db3]{padding:%?20?% 0;border-bottom:1px solid #f0f0f0}.health-check-item .check-info[data-v-59028db3]{margin-top:%?20?%}.health-check-item .check-info .info-item[data-v-59028db3]{margin-bottom:%?16?%;font-size:14px;color:#666}.health-check-item .check-info .info-item .label[data-v-59028db3]{color:#999}.health-check-item .item-footer[data-v-59028db3]{margin-top:%?20?%;display:flex;justify-content:flex-end}.conclusion-normal[data-v-59028db3]{color:#52c41a}.conclusion-warning[data-v-59028db3]{color:#fa8c16}.conclusion-danger[data-v-59028db3]{color:#f5222d}.conclusion-alert[data-v-59028db3]{color:#722ed1}.container[data-v-59028db3]{display:flex;flex-direction:column;height:100%;background-color:#f5f5f5}.empty-data[data-v-59028db3]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?100?% 0}.empty-data .empty-icon[data-v-59028db3]{margin-bottom:%?20?%;color:#c0c4cc}.empty-data .empty-text[data-v-59028db3]{font-size:%?28?%;color:#909399}.search-box[data-v-59028db3]{padding:15px;background:#fff;display:flex;flex-direction:column;gap:10px;margin-bottom:%?20?%}.search-box uni-input[data-v-59028db3]{height:40px;border:1px solid #ddd;border-radius:4px;padding:0 10px;font-size:14px}.search-box uni-button[data-v-59028db3]{height:40px;background:#007aff;color:#fff;border-radius:4px;font-size:14px;display:flex;align-items:center;justify-content:center}',""]),t.exports=e},"3d1c":function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("0518")),o={getEHealthRecordAuthList:function(t){return(0,i.default)({url:"manage/eHealthRecord/getEHealthRecordAuthList",method:"get",data:t})},handleEHealthRecordAuth:function(t){return(0,i.default)({url:"manage/eHealthRecord/handleEHealthRecordAuth",method:"post",data:t})},getEHealthRecordBaseInfo:function(t){return(0,i.default)({url:"manage/eHealthRecord/getEHealthRecordBaseInfo",method:"get",data:t})},updateEHealthRecordBaseInfo:function(t){return(0,i.default)({url:"manage/eHealthRecord/updateEHealthRecordBaseInfo",method:"post",data:t})},getSupervisionList:function(t){return(0,i.default)({url:"manage/eHealthRecord/getSupervisionList",method:"post",data:t})},postComplaint:function(t){return(0,i.default)({url:"manage/eHealthRecord/postComplaint",method:"post",data:t})},getLaborIsEdit:function(t){return(0,i.default)({url:"manage/eHealthRecord/getLaborIsEdit",method:"get",data:t})},addEmploymentHistory:function(t){return(0,i.default)({url:"manage/eHealthRecord/addEmploymentHistory",method:"post",data:t})},editEmploymentHistory:function(t){return(0,i.default)({url:"manage/eHealthRecord/editEmploymentHistory",method:"post",data:t})},deleteEmploymentHistory:function(t){return(0,i.default)({url:"manage/eHealthRecord/deleteEmploymentHistory",method:"post",data:t})},getEmploymentHistory:function(t){return(0,i.default)({url:"manage/eHealthRecord/getEmploymentHistory",method:"get",data:t})},findDistricts:function(t){return(0,i.default)({url:"app/user/findDistricts",method:"get",data:t})},getProvinces:function(){return this.findDistricts({level:0})},getCitiesByProvince:function(t){return this.findDistricts({level:1,parent_code:t})},getDistrictsByCity:function(t){return this.findDistricts({level:2,parent_code:t})},getTownsByDistrict:function(t){return this.findDistricts({level:3,parent_code:t})},getDiaList:function(t){return(0,i.default)({url:"manage/eHealthRecord/getDiaList",method:"post",data:t})},getIdentificationList:function(t){return(0,i.default)({url:"manage/eHealthRecord/getIdentificationList",method:"post",data:t})},findHarmFactors:function(t){return(0,i.default)({url:"manage/eHealthRecord/findHarmFactors",method:"get",data:t})}};e.default=o},4613:function(t,e,a){"use strict";var n=a("9f55"),i=a.n(n);i.a},"88c6":function(t,e,a){"use strict";a.r(e);var n=a("aea0"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},"9f55":function(t,e,a){var n=a("355d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("fca75106",n,!0,{sourceMap:!1,shadowMode:!1})},"9f88":function(t,e,a){"use strict";a.r(e);var n=a("e5b4"),i=a("88c6");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("4613");var r=a("828b"),c=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"59028db3",null,!1,n["a"],void 0);e["default"]=c.exports},aea0:function(t,e,a){"use strict";a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a("2634")),o=n(a("9b1b")),r=n(a("2fdc")),c=n(a("3d1c")),s=n(a("cc2e")),d={data:function(){return{list:[],searchForm:{name:""}}},created:function(){this.getData()},methods:{handleSearch:function(){this.getData()},reset:function(){this.searchForm={name:"",contract:"",phoneNum:""},this.getData()},getData:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.default.getEHealthRecordAuthList((0,o.default)({},t.searchForm));case 2:a=e.sent,t.list=a.data;case 4:case"end":return e.stop()}}),e)})))()},getFormatedDate:function(t){return(0,s.default)(t).format("YYYY-MM-DD")},getType:function(t){return"qy"==t?"用人单位":"jg"==t?"管理单位":"未知"},getStatus:function(t){return 1==t?"待处理":2==t?"已授权":3==t?"已拒绝":"-"},getConclusionClass:function(t){return{2:"conclusion-normal",1:"conclusion-warning",3:"conclusion-danger","":"conclusion-alert"}[t]||""},handle:function(t,e){var a=this;return(0,r.default)((0,i.default)().mark((function n(){var o;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,c.default.handleEHealthRecordAuth({_id:t,status:e});case 2:o=n.sent,200==o.status?(uni.showToast({title:"操作成功",icon:"success"}),a.getData()):uni.showToast({title:o.message,icon:"none"});case 4:case"end":return n.stop()}}),n)})))()}}};e.default=d},e5b4:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={gracePage:a("1367").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[a("my-header",{attrs:{slot:"gHeader",title:"授权管理"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body  container",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"search-box"},[a("v-uni-input",{attrs:{type:"text",placeholder:"请输入单位名称"},model:{value:t.searchForm.name,callback:function(e){t.$set(t.searchForm,"name",e)},expression:"searchForm.name"}}),a("v-uni-view",{staticClass:"btn-box",staticStyle:{display:"flex","justify-content":"space-between"}},[a("v-uni-button",{staticStyle:{width:"40%"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleSearch.apply(void 0,arguments)}}},[t._v("搜索")]),a("v-uni-button",{staticStyle:{width:"40%",background:"#fff",color:"#007AFF"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.reset.apply(void 0,arguments)}}},[t._v("重置")])],1)],1),t.list.length>0?t._l(t.list,(function(e,n){return a("v-uni-view",{key:n,staticClass:"health-check-item"},[a("v-uni-view",{staticClass:"item-header"},[a("v-uni-view",{staticClass:"org-name"},[t._v(t._s(e.orgName||e.EnterpriseID.cname))])],1),a("v-uni-view",{staticClass:"item-content"},[a("v-uni-view",{staticClass:"check-info"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("申请时间：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(t.getFormatedDate(e.createdAt)))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("单位类型：")]),a("v-uni-text",{staticClass:"value"},[t._v(t._s(t.getType(e.source)))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[t._v("授权状态：")]),a("v-uni-text",{staticClass:"value type-tag",class:t.getConclusionClass(e.status)},[t._v(t._s(t.getStatus(e.status)))])],1)],1)],1),a("v-uni-view",{staticClass:"item-footer",staticStyle:{display:"flex","justify-content":"end",gap:".5em",height:"40rpx","line-height":"normal"}},[1==e.status?a("v-uni-button",{staticClass:"grace-button circleBtn",attrs:{size:"mini",type:"warn"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.handle(e._id,3)}}},[a("v-uni-text",{staticClass:"grace-grids-icon grace-icons icon-close3",staticStyle:{"font-size":"28rpx",width:"1em"}})],1):t._e(),1==e.status?a("v-uni-button",{staticClass:"grace-button circleBtn",attrs:{size:"mini",type:"success"},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.handle(e._id,2)}}},[a("v-uni-text",{staticClass:"grace-grids-icon grace-icons icon-right",staticStyle:{"font-size":"28rpx",width:"1em"}})],1):t._e()],1)],1)})):[a("v-uni-view",{staticClass:"empty-data"},[a("v-uni-view",{staticClass:"empty-icon"},[a("v-uni-text",{staticClass:"grace-grids-icon grace-icons icon-info",staticStyle:{"font-size":"80rpx"}})],1),a("v-uni-view",{staticClass:"empty-text"},[t._v("暂无授权申请记录")])],1)]],2)],1)},o=[]}}]);