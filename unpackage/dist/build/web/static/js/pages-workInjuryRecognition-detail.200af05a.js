(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-workInjuryRecognition-detail"],{2059:function(t,e,i){"use strict";i.r(e);var n=i("7219"),a=i("f1b1");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("df83");var l=i("828b"),u=Object(l["a"])(a["default"],n["b"],n["c"],!1,null,"2b5fb029",null,!1,n["a"],void 0);e["default"]=u.exports},"2bf0":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-icon",class:["u-icon--"+t.labelPos],on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[t.isImg?i("v-uni-image",{staticClass:"u-icon__img",style:[t.imgStyle,t.$u.addStyle(t.customStyle)],attrs:{src:t.name,mode:t.imgMode}}):i("v-uni-text",{staticClass:"u-icon__icon",class:t.uClasses,style:[t.iconStyle,t.$u.addStyle(t.customStyle)],attrs:{"hover-class":t.hoverClass}},[t._v(t._s(t.icon))]),""!==t.label?i("v-uni-text",{staticClass:"u-icon__label",style:{color:t.labelColor,fontSize:t.$u.addUnit(t.labelSize),marginLeft:"right"==t.labelPos?t.$u.addUnit(t.space):0,marginTop:"bottom"==t.labelPos?t.$u.addUnit(t.space):0,marginRight:"left"==t.labelPos?t.$u.addUnit(t.space):0,marginBottom:"top"==t.labelPos?t.$u.addUnit(t.space):0}},[t._v(t._s(t.label))]):t._e()],1)},a=[]},"2fc9":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("e70e")),o=(n(i("b2a95")),n(i("ec16")),n(i("7f2f"))),l={name:"u--text",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default,o.default],computed:{valueStyle:function(){var t={textDecoration:this.decoration,fontWeight:this.bold?"bold":"normal",wordWrap:this.wordWrap,fontSize:uni.$u.addUnit(this.size)};return!this.type&&(t.color=this.color),this.isNvue&&this.lines&&(t.lines=this.lines),this.lineHeight&&(t.lineHeight=uni.$u.addUnit(this.lineHeight)),!this.isNvue&&this.block&&(t.display="block"),uni.$u.deepMerge(t,uni.$u.addStyle(this.customStyle))},isNvue:function(){return!1},isMp:function(){return!1}},data:function(){return{}},methods:{clickHandler:function(){this.call&&"phone"===this.mode&&uni.makePhoneCall({phoneNumber:this.text}),this.$emit("click")}}};e.default=l},"33a2":function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,'uni-view[data-v-59765974], uni-scroll-view[data-v-59765974], uni-swiper-item[data-v-59765974]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}@font-face{font-family:uicon-iconfont;src:url(https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf) format("truetype")}.u-icon[data-v-59765974]{display:flex;align-items:center}.u-icon--left[data-v-59765974]{flex-direction:row-reverse;align-items:center}.u-icon--right[data-v-59765974]{flex-direction:row;align-items:center}.u-icon--top[data-v-59765974]{flex-direction:column-reverse;justify-content:center}.u-icon--bottom[data-v-59765974]{flex-direction:column;justify-content:center}.u-icon__icon[data-v-59765974]{font-family:uicon-iconfont;position:relative;display:flex;flex-direction:row;align-items:center}.u-icon__icon--primary[data-v-59765974]{color:#3c9cff}.u-icon__icon--success[data-v-59765974]{color:#5ac725}.u-icon__icon--error[data-v-59765974]{color:#f56c6c}.u-icon__icon--warning[data-v-59765974]{color:#f9ae3d}.u-icon__icon--info[data-v-59765974]{color:#909399}.u-icon__img[data-v-59765974]{height:auto;will-change:transform}.u-icon__label[data-v-59765974]{line-height:1}',""]),t.exports=e},"365b":function(t,e,i){var n=i("f2f3");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("73c3b0c7",n,!0,{sourceMap:!1,shadowMode:!1})},"3fa0":function(t,e,i){"use strict";i.r(e);var n=i("2fc9"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},4194:function(t,e,i){var n=i("33a2");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("39d62e96",n,!0,{sourceMap:!1,shadowMode:!1})},"4e83":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.downloadFile=void 0;e.downloadFile=function(t,e){var i=document.createElement("a");i.href=e,i.download=t||"file.ext",i.style.display="none",document.body.appendChild(i),i.click(),document.body.removeChild(i)}},"5abb":function(t,e,i){var n=i("b1ec");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("9e8b1232",n,!0,{sourceMap:!1,shadowMode:!1})},"60db":function(t,e,i){t.exports=i.p+"assets/uni.75745d34.ttf"},"663a":function(t,e,i){"use strict";var n=i("6d7d"),a=i.n(n);a.a},"67fa":function(t,e,i){"use strict";i.r(e);var n=i("df08"),a=i("8839");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("69f7");var l=i("828b"),u=Object(l["a"])(a["default"],n["b"],n["c"],!1,null,"5c0264f4",null,!1,n["a"],void 0);e["default"]=u.exports},"69f7":function(t,e,i){"use strict";var n=i("cf5b"),a=i.n(n);a.a},"6d7d":function(t,e,i){var n=i("e2b6");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("8fd2620a",n,!0,{sourceMap:!1,shadowMode:!1})},"6ec4":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var n={props:{color:{type:String,default:uni.$u.props.link.color},fontSize:{type:[String,Number],default:uni.$u.props.link.fontSize},underLine:{type:Boolean,default:uni.$u.props.link.underLine},href:{type:String,default:uni.$u.props.link.href},mpTips:{type:String,default:uni.$u.props.link.mpTips},lineColor:{type:String,default:uni.$u.props.link.lineColor},text:{type:String,default:uni.$u.props.link.text}}};e.default=n},"6f26":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("7d9c")),o=n(i("7f2f")),l={name:"u--text",mixins:[uni.$u.mpMixin,o.default,uni.$u.mixin],components:{uvText:a.default}};e.default=l},7219:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{staticClass:"u-link",style:[t.linkStyle,t.$u.addStyle(t.customStyle)],on:{click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.openLink.apply(void 0,arguments)}}},[t._v(t._s(t.text))])},a=[]},"7d9c":function(t,e,i){"use strict";i.r(e);var n=i("e9d9"),a=i("3fa0");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("663a");var l=i("828b"),u=Object(l["a"])(a["default"],n["b"],n["c"],!1,null,"ed1d90b6",null,!1,n["a"],void 0);e["default"]=u.exports},"7f2f":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var n={props:{type:{type:String,default:uni.$u.props.text.type},show:{type:Boolean,default:uni.$u.props.text.show},text:{type:[String,Number],default:uni.$u.props.text.text},prefixIcon:{type:String,default:uni.$u.props.text.prefixIcon},suffixIcon:{type:String,default:uni.$u.props.text.suffixIcon},mode:{type:String,default:uni.$u.props.text.mode},href:{type:String,default:uni.$u.props.text.href},format:{type:[String,Function],default:uni.$u.props.text.format},call:{type:Boolean,default:uni.$u.props.text.call},openType:{type:String,default:uni.$u.props.text.openType},bold:{type:Boolean,default:uni.$u.props.text.bold},block:{type:Boolean,default:uni.$u.props.text.block},lines:{type:[String,Number],default:uni.$u.props.text.lines},color:{type:String,default:uni.$u.props.text.color},size:{type:[String,Number],default:uni.$u.props.text.size},iconStyle:{type:[Object,String],default:uni.$u.props.text.iconStyle},decoration:{type:String,default:uni.$u.props.text.decoration},margin:{type:[Object,String,Number],default:uni.$u.props.text.margin},lineHeight:{type:[String,Number],default:uni.$u.props.text.lineHeight},align:{type:String,default:uni.$u.props.text.align},wordWrap:{type:String,default:uni.$u.props.text.wordWrap}}};e.default=n},"857c":function(t,e,i){"use strict";i.r(e);var n=i("a7e3"),a=i("d741");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);var l=i("828b"),u=Object(l["a"])(a["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=u.exports},8839:function(t,e,i){"use strict";i.r(e);var n=i("f10a"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},"88d0":function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("aa9c"),i("4626"),i("5ac7"),i("5ef2");var a=n(i("9abe")),o=n(i("ad57")),l={name:"u-icon",data:function(){return{}},mixins:[uni.$u.mpMixin,uni.$u.mixin,o.default],computed:{uClasses:function(){var t=[];return t.push(this.customPrefix+"-"+this.name),this.color&&uni.$u.config.type.includes(this.color)&&t.push("u-icon__icon--"+this.color),t},iconStyle:function(){var t={};return t={fontSize:uni.$u.addUnit(this.size),lineHeight:uni.$u.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:uni.$u.addUnit(this.top)},this.color&&!uni.$u.config.type.includes(this.color)&&(t.color=this.color),t},isImg:function(){return-1!==this.name.indexOf("/")},imgStyle:function(){var t={};return t.width=this.width?uni.$u.addUnit(this.width):uni.$u.addUnit(this.size),t.height=this.height?uni.$u.addUnit(this.height):uni.$u.addUnit(this.size),t},icon:function(){return a.default["uicon-"+this.name]||this.name}},methods:{clickHandler:function(t){this.$emit("click",this.index),this.stop&&this.preventEvent(t)}}};e.default=l},"9abe":function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={"uicon-level":"","uicon-column-line":"","uicon-checkbox-mark":"","uicon-folder":"","uicon-movie":"","uicon-star-fill":"","uicon-star":"","uicon-phone-fill":"","uicon-phone":"","uicon-apple-fill":"","uicon-chrome-circle-fill":"","uicon-backspace":"","uicon-attach":"","uicon-cut":"","uicon-empty-car":"","uicon-empty-coupon":"","uicon-empty-address":"","uicon-empty-favor":"","uicon-empty-permission":"","uicon-empty-news":"","uicon-empty-search":"","uicon-github-circle-fill":"","uicon-rmb":"","uicon-person-delete-fill":"","uicon-reload":"","uicon-order":"","uicon-server-man":"","uicon-search":"","uicon-fingerprint":"","uicon-more-dot-fill":"","uicon-scan":"","uicon-share-square":"","uicon-map":"","uicon-map-fill":"","uicon-tags":"","uicon-tags-fill":"","uicon-bookmark-fill":"","uicon-bookmark":"","uicon-eye":"","uicon-eye-fill":"","uicon-mic":"","uicon-mic-off":"","uicon-calendar":"","uicon-calendar-fill":"","uicon-trash":"","uicon-trash-fill":"","uicon-play-left":"","uicon-play-right":"","uicon-minus":"","uicon-plus":"","uicon-info":"","uicon-info-circle":"","uicon-info-circle-fill":"","uicon-question":"","uicon-error":"","uicon-close":"","uicon-checkmark":"","uicon-android-circle-fill":"","uicon-android-fill":"","uicon-ie":"","uicon-IE-circle-fill":"","uicon-google":"","uicon-google-circle-fill":"","uicon-setting-fill":"","uicon-setting":"","uicon-minus-square-fill":"","uicon-plus-square-fill":"","uicon-heart":"","uicon-heart-fill":"","uicon-camera":"","uicon-camera-fill":"","uicon-more-circle":"","uicon-more-circle-fill":"","uicon-chat":"","uicon-chat-fill":"","uicon-bag-fill":"","uicon-bag":"","uicon-error-circle-fill":"","uicon-error-circle":"","uicon-close-circle":"","uicon-close-circle-fill":"","uicon-checkmark-circle":"","uicon-checkmark-circle-fill":"","uicon-question-circle-fill":"","uicon-question-circle":"","uicon-share":"","uicon-share-fill":"","uicon-shopping-cart":"","uicon-shopping-cart-fill":"","uicon-bell":"","uicon-bell-fill":"","uicon-list":"","uicon-list-dot":"","uicon-zhihu":"","uicon-zhihu-circle-fill":"","uicon-zhifubao":"","uicon-zhifubao-circle-fill":"","uicon-weixin-circle-fill":"","uicon-weixin-fill":"","uicon-twitter-circle-fill":"","uicon-twitter":"","uicon-taobao-circle-fill":"","uicon-taobao":"","uicon-weibo-circle-fill":"","uicon-weibo":"","uicon-qq-fill":"","uicon-qq-circle-fill":"","uicon-moments-circel-fill":"","uicon-moments":"","uicon-qzone":"","uicon-qzone-circle-fill":"","uicon-baidu-circle-fill":"","uicon-baidu":"","uicon-facebook-circle-fill":"","uicon-facebook":"","uicon-car":"","uicon-car-fill":"","uicon-warning-fill":"","uicon-warning":"","uicon-clock-fill":"","uicon-clock":"","uicon-edit-pen":"","uicon-edit-pen-fill":"","uicon-email":"","uicon-email-fill":"","uicon-minus-circle":"","uicon-minus-circle-fill":"","uicon-plus-circle":"","uicon-plus-circle-fill":"","uicon-file-text":"","uicon-file-text-fill":"","uicon-pushpin":"","uicon-pushpin-fill":"","uicon-grid":"","uicon-grid-fill":"","uicon-play-circle":"","uicon-play-circle-fill":"","uicon-pause-circle-fill":"","uicon-pause":"","uicon-pause-circle":"","uicon-eye-off":"","uicon-eye-off-outline":"","uicon-gift-fill":"","uicon-gift":"","uicon-rmb-circle-fill":"","uicon-rmb-circle":"","uicon-kefu-ermai":"","uicon-server-fill":"","uicon-coupon-fill":"","uicon-coupon":"","uicon-integral":"","uicon-integral-fill":"","uicon-home-fill":"","uicon-home":"","uicon-hourglass-half-fill":"","uicon-hourglass":"","uicon-account":"","uicon-plus-people-fill":"","uicon-minus-people-fill":"","uicon-account-fill":"","uicon-thumb-down-fill":"","uicon-thumb-down":"","uicon-thumb-up":"","uicon-thumb-up-fill":"","uicon-lock-fill":"","uicon-lock-open":"","uicon-lock-opened-fill":"","uicon-lock":"","uicon-red-packet-fill":"","uicon-photo-fill":"","uicon-photo":"","uicon-volume-off-fill":"","uicon-volume-off":"","uicon-volume-fill":"","uicon-volume":"","uicon-red-packet":"","uicon-download":"","uicon-arrow-up-fill":"","uicon-arrow-down-fill":"","uicon-play-left-fill":"","uicon-play-right-fill":"","uicon-rewind-left-fill":"","uicon-rewind-right-fill":"","uicon-arrow-downward":"","uicon-arrow-leftward":"","uicon-arrow-rightward":"","uicon-arrow-upward":"","uicon-arrow-down":"","uicon-arrow-right":"","uicon-arrow-left":"","uicon-arrow-up":"","uicon-skip-back-left":"","uicon-skip-forward-right":"","uicon-rewind-right":"","uicon-rewind-left":"","uicon-arrow-right-double":"","uicon-arrow-left-double":"","uicon-wifi-off":"","uicon-wifi":"","uicon-empty-data":"","uicon-empty-history":"","uicon-empty-list":"","uicon-empty-page":"","uicon-empty-order":"","uicon-man":"","uicon-woman":"","uicon-man-add":"","uicon-man-add-fill":"","uicon-man-delete":"","uicon-man-delete-fill":"","uicon-zh":"","uicon-en":""}},a7e3:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("uvText",{attrs:{type:t.type,show:t.show,text:t.text,prefixIcon:t.prefixIcon,suffixIcon:t.suffixIcon,mode:t.mode,href:t.href,format:t.format,call:t.call,openType:t.openType,bold:t.bold,block:t.block,lines:t.lines,color:t.color,decoration:t.decoration,size:t.size,iconStyle:t.iconStyle,margin:t.margin,lineHeight:t.lineHeight,align:t.align,wordWrap:t.wordWrap,customStyle:t.customStyle},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.$emit("click")}}})},a=[]},a922:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},aa10:function(t,e,i){"use strict";i.r(e);var n=i("2bf0"),a=i("d5f4");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("e026");var l=i("828b"),u=Object(l["a"])(a["default"],n["b"],n["c"],!1,null,"59765974",null,!1,n["a"],void 0);e["default"]=u.exports},aa59:function(t,e,i){"use strict";i.r(e);var n=i("ff18"),a=i("d4cc");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(o);i("ec5c");var l=i("828b"),u=Object(l["a"])(a["default"],n["b"],n["c"],!1,null,"2df9d603",null,!1,n["a"],void 0);e["default"]=u.exports},ad57:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var n={props:{name:{type:String,default:uni.$u.props.icon.name},color:{type:String,default:uni.$u.props.icon.color},size:{type:[String,Number],default:uni.$u.props.icon.size},bold:{type:Boolean,default:uni.$u.props.icon.bold},index:{type:[String,Number],default:uni.$u.props.icon.index},hoverClass:{type:String,default:uni.$u.props.icon.hoverClass},customPrefix:{type:String,default:uni.$u.props.icon.customPrefix},label:{type:[String,Number],default:uni.$u.props.icon.label},labelPos:{type:String,default:uni.$u.props.icon.labelPos},labelSize:{type:[String,Number],default:uni.$u.props.icon.labelSize},labelColor:{type:String,default:uni.$u.props.icon.labelColor},space:{type:[String,Number],default:uni.$u.props.icon.space},imgMode:{type:String,default:uni.$u.props.icon.imgMode},width:{type:[String,Number],default:uni.$u.props.icon.width},height:{type:[String,Number],default:uni.$u.props.icon.height},top:{type:[String,Number],default:uni.$u.props.icon.top},stop:{type:Boolean,default:uni.$u.props.icon.stop}}};e.default=n},b1ec:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,"uni-view[data-v-2b5fb029], uni-scroll-view[data-v-2b5fb029], uni-swiper-item[data-v-2b5fb029]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-link[data-v-2b5fb029]{line-height:1;display:flex;flex-direction:row;flex-wrap:wrap;flex:1}",""]),t.exports=e},cf5b:function(t,e,i){var n=i("f103");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var a=i("967d").default;a("c24910a6",n,!0,{sourceMap:!1,shadowMode:!1})},d4cc:function(t,e,i){"use strict";i.r(e);var n=i("fd10"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},d5f4:function(t,e,i){"use strict";i.r(e);var n=i("88d0"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},d741:function(t,e,i){"use strict";i.r(e);var n=i("6f26"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},dc5a:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("6ec4")),o={name:"u-link",mixins:[uni.$u.mpMixin,uni.$u.mixin,a.default],computed:{linkStyle:function(){var t={color:this.color,fontSize:uni.$u.addUnit(this.fontSize),lineHeight:uni.$u.addUnit(uni.$u.getPx(this.fontSize)+2),textDecoration:this.underLine?"underline":"none"};return t}},methods:{openLink:function(){window.open(this.href),this.$emit("click")}}};e.default=o},df08:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-text",{staticClass:"uni-icons",class:[t.customIcons,t.customIcons?t.type:""],style:{color:t.color,"font-size":t.size+"px"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t._onClick.apply(void 0,arguments)}}},[t._v(t._s(t.icons[t.type]))])},a=[]},df83:function(t,e,i){"use strict";var n=i("5abb"),a=i.n(n);a.a},e026:function(t,e,i){"use strict";var n=i("4194"),a=i.n(n);a.a},e2b6:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,"uni-view[data-v-ed1d90b6], uni-scroll-view[data-v-ed1d90b6], uni-swiper-item[data-v-ed1d90b6]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-text[data-v-ed1d90b6]{display:flex;flex-direction:row;align-items:center;flex-wrap:nowrap;flex:1;width:100%}.u-text__price[data-v-ed1d90b6]{font-size:14px;color:#606266}.u-text__value[data-v-ed1d90b6]{font-size:14px;display:flex;flex-direction:row;color:#606266;flex-wrap:wrap;text-overflow:ellipsis;align-items:center}.u-text__value--primary[data-v-ed1d90b6]{color:#3c9cff}.u-text__value--warning[data-v-ed1d90b6]{color:#f9ae3d}.u-text__value--success[data-v-ed1d90b6]{color:#5ac725}.u-text__value--info[data-v-ed1d90b6]{color:#909399}.u-text__value--error[data-v-ed1d90b6]{color:#f56c6c}.u-text__value--main[data-v-ed1d90b6]{color:#303133}.u-text__value--content[data-v-ed1d90b6]{color:#606266}.u-text__value--tips[data-v-ed1d90b6]{color:#909193}.u-text__value--light[data-v-ed1d90b6]{color:#c0c4cc}",""]),t.exports=e},e70e:function(t,e,i){"use strict";i("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("5c47"),i("0506"),i("c223");var n={computed:{value:function(){var t=this.text,e=this.mode,i=this.format,n=this.href;return"price"===e?(/^\d+(\.\d+)?$/.test(t)||uni.$u.error("金额模式下，text参数需要为金额格式"),uni.$u.test.func(i)?i(t):uni.$u.priceFormat(t,2)):"date"===e?(!uni.$u.test.date(t)&&uni.$u.error("日期模式下，text参数需要为日期或时间戳格式"),uni.$u.test.func(i)?i(t):i?uni.$u.timeFormat(t,i):uni.$u.timeFormat(t,"yyyy-mm-dd")):"phone"===e?uni.$u.test.func(i)?i(t):"encrypt"===i?"".concat(t.substr(0,3),"****").concat(t.substr(7)):t:"name"===e?("string"!==typeof t&&uni.$u.error("姓名模式下，text参数需要为字符串格式"),uni.$u.test.func(i)?i(t):"encrypt"===i?this.formatName(t):t):"link"===e?(!uni.$u.test.url(n)&&uni.$u.error("超链接模式下，href参数需要为URL格式"),t):t}},methods:{formatName:function(t){var e="";if(2===t.length)e=t.substr(0,1)+"*";else if(t.length>2){for(var i="",n=0,a=t.length-2;n<a;n++)i+="*";e=t.substr(0,1)+i+t.substr(-1,1)}else e=t;return e}}};e.default=n},e9d9:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uIcon:i("aa10").default,uLink:i("2059").default},a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.show?i("v-uni-view",{staticClass:"u-text",class:[],style:{margin:t.margin,justifyContent:"left"===t.align?"flex-start":"center"===t.align?"center":"flex-end"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},["price"===t.mode?i("v-uni-text",{class:["u-text__price",t.type&&"u-text__value--"+t.type],style:[t.valueStyle]},[t._v("￥")]):t._e(),t.prefixIcon?i("v-uni-view",{staticClass:"u-text__prefix-icon"},[i("u-icon",{attrs:{name:t.prefixIcon,customStyle:t.$u.addStyle(t.iconStyle)}})],1):t._e(),"link"===t.mode?i("u-link",{attrs:{text:t.value,href:t.href,underLine:!0}}):t.openType&&t.isMp?[i("v-uni-button",{staticClass:"u-reset-button u-text__value",style:[t.valueStyle],attrs:{"data-index":t.index,openType:t.openType,lang:t.lang,"session-from":t.sessionFrom,"send-message-title":t.sendMessageTitle,"send-message-path":t.sendMessagePath,"send-message-img":t.sendMessageImg,"show-message-card":t.showMessageCard,"app-parameter":t.appParameter},on:{getuserinfo:function(e){arguments[0]=e=t.$handleEvent(e),t.onGetUserInfo.apply(void 0,arguments)},contact:function(e){arguments[0]=e=t.$handleEvent(e),t.onContact.apply(void 0,arguments)},getphonenumber:function(e){arguments[0]=e=t.$handleEvent(e),t.onGetPhoneNumber.apply(void 0,arguments)},error:function(e){arguments[0]=e=t.$handleEvent(e),t.onError.apply(void 0,arguments)},launchapp:function(e){arguments[0]=e=t.$handleEvent(e),t.onLaunchApp.apply(void 0,arguments)},opensetting:function(e){arguments[0]=e=t.$handleEvent(e),t.onOpenSetting.apply(void 0,arguments)}}},[t._v(t._s(t.value))])]:i("v-uni-text",{staticClass:"u-text__value",class:[t.type&&"u-text__value--"+t.type,t.lines&&"u-line-"+t.lines],style:[t.valueStyle]},[t._v(t._s(t.value))]),t.suffixIcon?i("v-uni-view",{staticClass:"u-text__suffix-icon"},[i("u-icon",{attrs:{name:t.suffixIcon,customStyle:t.$u.addStyle(t.iconStyle)}})],1):t._e()],2):t._e()},o=[]},ec5c:function(t,e,i){"use strict";var n=i("365b"),a=i.n(n);a.a},f103:function(t,e,i){var n=i("c86c"),a=i("2ec5"),o=i("60db");e=n(!1);var l=a(o);e.push([t.i,"@font-face{font-family:uniicons;src:url("+l+') format("truetype")}.uni-icons[data-v-5c0264f4]{font-family:uniicons;text-decoration:none;text-align:center}',""]),t.exports=e},f10a:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("64aa");var a=n(i("a922")),o={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:a.default}},methods:{_onClick:function(){this.$emit("click")}}};e.default=o},f1b1:function(t,e,i){"use strict";i.r(e);var n=i("dc5a"),a=i.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);e["default"]=a.a},f2f3:function(t,e,i){var n=i("c86c");e=n(!1),e.push([t.i,".add[data-v-2df9d603]{width:100%;padding:0 %?30?%;box-sizing:border-box}.add .nav-left[data-v-2df9d603]{display:flex;align-items:center;width:auto;color:#fff}.add .nav-left uni-image[data-v-2df9d603]{width:%?40?%;height:%?40?%}.add_body[data-v-2df9d603]{box-sizing:border-box;width:100%;display:flex;flex-direction:column;align-items:center}.add_body .add_content[data-v-2df9d603]{width:%?720?%;height:100%}.add_body .add_content .basicFrom .row[data-v-2df9d603]{margin-bottom:%?22?%;display:flex;align-items:center;width:100%}.add_body .add_content .basicFrom .row uni-text[data-v-2df9d603]{font-family:Source Han Sans;font-size:%?28?%;color:#555;margin-right:%?32?%}.add_body .add_content .basicFrom .row .title[data-v-2df9d603]{width:%?340?%;text-align:right}.add_body .add_content .basicFrom .row .content[data-v-2df9d603]{flex:1}.add_body .add_content .annexFile .annex[data-v-2df9d603]{margin-bottom:32px;display:flex;flex-direction:column;gap:12px}.add_body .add_content .annexFile .annex .title[data-v-2df9d603]{font-size:16px}.add_body .add_content .auditResult .row[data-v-2df9d603]{margin-bottom:%?22?%;display:flex;align-items:center;width:100%}.add_body .add_content .auditResult .row uni-text[data-v-2df9d603]{font-family:Source Han Sans;font-size:%?28?%;color:#555;margin-right:%?32?%}.add_body .add_content .auditResult .row .title[data-v-2df9d603]{width:%?200?%;text-align:right}.add_body .add_content .auditResult .row .content[data-v-2df9d603]{flex:1}.add_body .add_content .operator[data-v-2df9d603]{display:flex;padding:16px 0;gap:16px}",""]),t.exports=e},f7d8:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.addOrEditWorkInjuryRecognition=function(t){return(0,a.default)({url:"manage/adminorg/workInjuryRecognition",data:t,method:"post"})},e.uploadFile=e.getWorkInjuryRecognitionList=e.getWorkInjuryRecognitionDetail=e.downloadTemplateFile=e.deleteWorkInjuryRecognition=e.deleteFile=void 0;var a=n(i("0518"));e.getWorkInjuryRecognitionList=function(t){return(0,a.default)({url:"manage/adminorg/workInjuryRecognition",data:t,method:"get"})};e.getWorkInjuryRecognitionDetail=function(t){return(0,a.default)({url:"manage/adminorg/workInjuryRecognitionDetail?_id="+t._id,method:"get"})};e.deleteWorkInjuryRecognition=function(t){return(0,a.default)({url:"manage/adminorg/workInjuryRecognition",method:"delete",data:t})};e.downloadTemplateFile=function(t){return(0,a.default)({url:"manage/adminorg/recognitionTemp",data:t,method:"get"})};e.uploadFile=function(t){return(0,a.default)({url:"app/file",data:t,method:"post",header:{"Content-Type":"multipart/form-data"}})};e.deleteFile=function(t){return(0,a.default)({url:"app/file?filePath="+t.filePath,method:"delete"})}},fd10:function(t,e,i){"use strict";i("6a54");var n=i("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("aa9c"),i("5c47"),i("a1c1");var a=n(i("2634")),o=n(i("2fdc")),l=n(i("7703")),u=i("f7d8"),c=(i("4e83"),{name:"WIRAdd",data:function(){return{tabList:[{name:"基本信息"},{name:"工伤附件"}],curTab:"基本信息",formData:{employee_name:"",gender:"",birthday:"",id_code:"",employee_phone:"",employee_address:"",employee_postcode:"",enterprise_name:"",enterprise_phone:"",enterprise_address:"",enterprise_postcode:"",employee_job:"",employee_work_date:"",accident_time:"",accident_place:"",main_reason:"",diagnosis_time:"",injury_part:"",disease_name:"",exposure_post:"",exposure_time:"",description:"",apply_matter:"",annex_application:[],annex_labor_contract:[],annex_certificate:[],annex_id:[],annex_photo:[],annex_investigation:[]}}},onLoad:function(t){var e=this;return(0,o.default)((0,a.default)().mark((function i(){var n,o;return(0,a.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(n=t._id,!n){i.next=7;break}return i.next=4,(0,u.getWorkInjuryRecognitionDetail)({_id:n});case 4:o=i.sent,e.formData=o.data,"2"!==e.formData.status&&"3"!==e.formData.status||e.tabList.push({name:"审核结果"});case 7:case"end":return i.stop()}}),i)})))()},methods:{back:function(){uni.navigateBack()},handleTabChange:function(t){var e=t.name;this.curTab=e},handleCancel:function(){uni.navigateTo({url:"/pages/workInjuryRecognition/index"})},handleDownloadFile:function(t,e){window.open(l.default.apiServer+e.replace(/^\//,""))}}});e.default=c},ff18:function(t,e,i){"use strict";i.d(e,"b",(function(){return a})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return n}));var n={uniNavBar:i("27af").default,uTabs:i("7560").default,uButton:i("d9f5").default,"u-Text":i("857c").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"add"},[n("uni-nav-bar",{attrs:{leftWidth:"180rpx",fixed:!0,"background-color":"#007AFF",border:"{false}"},on:{clickLeft:function(e){arguments[0]=e=t.$handleEvent(e),t.back.apply(void 0,arguments)}}},[n("template",{attrs:{slot:"left"},slot:"left"},[n("v-uni-view",{staticClass:"nav-left"},[n("v-uni-image",{attrs:{src:i("00a9"),mode:""}}),t._v("详情")],1)],1)],2),n("v-uni-view",{staticClass:"add_body"},[n("v-uni-view",{staticClass:"add_content"},[n("u-tabs",{attrs:{list:t.tabList},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.handleTabChange.apply(void 0,arguments)}}}),"基本信息"===t.curTab?n("v-uni-view",{staticClass:"basicFrom"},[n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("员工姓名：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.employee_name))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("性别：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.gender))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("出生年月：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.birthday))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("身份证号码：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.id_code))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("联系电话：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.employee_phone))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("家庭住址：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.employee_address))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("邮政编码：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.employee_postcode))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("工作单位：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.enterprise_name))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("联系电话(单位)：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.enterprise_phone))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("单位地址：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.enterprise_address))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("单位邮政编码：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.enterprise_postcode))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("职业、工种或工作岗位：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.employee_job))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("参加工作时间：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.employee_work_date))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("事故时间：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.accident_time))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("事故地点：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.accident_place))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("主要原因：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.main_reason))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("诊断时间：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.diagnosis_time))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("受伤部位：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.injury_part))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("职业病名称：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.disease_name))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("接触职业病危害岗位：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.exposure_post))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("接触职业病危害时间：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.exposure_time))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("受伤经过简述：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.description))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("申请事项：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.apply_matter))])],1)],1),n("v-uni-view",{staticClass:"operator"},[n("u-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleCancel.apply(void 0,arguments)}}},[t._v("关闭")])],1)],1):"工伤附件"===t.curTab?n("v-uni-view",{staticClass:"annexFile"},[n("v-uni-view",{staticClass:"annex"},[n("v-uni-view",{staticClass:"title"},[t._v("工伤认定申请表")]),t._l(t.formData.annex_application,(function(e){return n("u--text",{key:e,staticClass:"file-item",attrs:{type:"primary",text:e.split("/").pop()},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleDownloadFile(e.split("/").pop(),e)}}})}))],2),n("v-uni-view",{staticClass:"annex"},[n("v-uni-view",{staticClass:"title"},[t._v("劳动关系证明")]),t._l(t.formData.annex_labor_contract,(function(e){return n("u--text",{key:e,staticClass:"file-item",attrs:{type:"primary",text:e.split("/").pop()},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleDownloadFile(e.split("/").pop(),e)}}})}))],2),n("v-uni-view",{staticClass:"annex"},[n("v-uni-view",{staticClass:"title"},[t._v("诊断证明")]),t._l(t.formData.annex_certificate,(function(e){return n("u--text",{key:e,staticClass:"file-item",attrs:{type:"primary",text:e.split("/").pop()},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleDownloadFile(e.split("/").pop(),e)}}})}))],2),n("v-uni-view",{staticClass:"annex"},[n("v-uni-view",{staticClass:"title"},[t._v("身份证复件")]),t._l(t.formData.annex_id,(function(e){return n("u--text",{key:e,staticClass:"file-item",attrs:{type:"primary",text:e.split("/").pop()},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleDownloadFile(e.split("/").pop(),e)}}})}))],2),n("v-uni-view",{staticClass:"annex"},[n("v-uni-view",{staticClass:"title"},[t._v("一寸照片")]),t._l(t.formData.annex_photo,(function(e){return n("u--text",{key:e,staticClass:"file-item",attrs:{type:"primary",text:e.split("/").pop()},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleDownloadFile(e.split("/").pop(),e)}}})}))],2),n("v-uni-view",{staticClass:"annex"},[n("v-uni-view",{staticClass:"title"},[t._v("调查报告")]),t._l(t.formData.annex_investigation,(function(e){return n("u--text",{key:e,staticClass:"file-item",attrs:{type:"primary",text:e.split("/").pop()},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleDownloadFile(e.split("/").pop(),e)}}})}))],2),n("v-uni-view",{staticClass:"operator"},[n("u-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleCancel.apply(void 0,arguments)}}},[t._v("关闭")])],1)],1):"审核结果"===t.curTab?n("v-uni-view",{staticClass:"auditResult"},[n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("审核人：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.audit_user))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("审核时间：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.audit_time))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("审核意见：")])],1),n("v-uni-view",{staticClass:"content"},[n("v-uni-text",[t._v(t._s(t.formData.audit_opinion))])],1)],1),n("v-uni-view",{staticClass:"row"},[n("v-uni-view",{staticClass:"title"},[n("v-uni-text",[t._v("工伤认定书：")])],1),n("v-uni-view",{staticClass:"content"},t._l(t.formData.annex_recognition,(function(e){return n("u--text",{key:e,attrs:{type:"primary",text:"工伤认定书"},on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.handleDownloadFile("工伤认定书",e)}}})})),1)],1),n("v-uni-view",{staticClass:"operator"},[n("u-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.handleCancel.apply(void 0,arguments)}}},[t._v("关闭")])],1)],1):t._e()],1)],1)],1)},o=[]}}]);