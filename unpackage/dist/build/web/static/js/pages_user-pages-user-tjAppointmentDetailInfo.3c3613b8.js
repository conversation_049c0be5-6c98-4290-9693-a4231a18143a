(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-user-tjAppointmentDetailInfo"],{"074d":function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,".infoCard[data-v-e9c57622]{padding:15px;border-radius:10px;margin-top:12.5px;position:relative}.infoCard .cardTitle[data-v-e9c57622]{margin-bottom:16px}.infoCard .cardTitle .titleText[data-v-e9c57622]{font-weight:600;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555}.infoCard .cardTitle .titlePoint[data-v-e9c57622]{position:absolute;left:0;top:13px;width:6px;height:20px;border-radius:3px 3px 0 3px;opacity:1;background:#3e73fe}.infoCard .cardItem[data-v-e9c57622]{display:flex;margin-bottom:12px;justify-content:space-between;font-size:%?24?%;font-weight:400;line-height:%?28?%;letter-spacing:0;color:#555}.infoCard .cardItem[data-v-e9c57622]:last-child{margin-bottom:0}.itemsCard[data-v-e9c57622]{padding:15px;border-radius:10px;margin-top:12.5px;position:relative}.itemsCard .cardTitle[data-v-e9c57622]{margin-bottom:16px}.itemsCard .cardTitle .titleText[data-v-e9c57622]{font-weight:600;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555}.itemsCard .cardTitle .titlePoint[data-v-e9c57622]{position:absolute;left:0;top:13px;width:6px;height:20px;border-radius:3px 3px 0 3px;opacity:1;background:#3e73fe}.itemsCard .cardItem[data-v-e9c57622]{display:flex;margin-bottom:12px;justify-content:space-between}.itemsCard .cardItem[data-v-e9c57622]:last-child{margin-bottom:0}.itemsCard .cardItem .itemLabel[data-v-e9c57622]{font-weight:400;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555}.itemsCard .cardItem .itemContent[data-v-e9c57622]{font-weight:400;font-size:%?24?%;line-height:%?28?%;letter-spacing:0;color:#2a91fc}.itemsCard .cardBtns[data-v-e9c57622]{display:flex;justify-content:flex-end}.itemsCard .cardBtns .cancelBtn[data-v-e9c57622]{color:#3e73fe;background-color:#fff;border:1px solid #3e73fe;border-radius:4px}.itemsCard .cardBtns .updateBtn[data-v-e9c57622]{color:#fff;background-color:#3e73fe;border:1px solid #3e73fe;border-radius:4px}.itemDialog .dialogContent[data-v-e9c57622]{padding:0 %?52?%;font-size:%?32?%;letter-spacing:0;color:#666;margin-bottom:%?32?%;min-height:120px;height:auto!important}.itemDialog .dialogBtns[data-v-e9c57622]{border-top:1px solid #e9e9e9}.itemDialog .dialogBtns .IKnowBtn[data-v-e9c57622]{width:auto;font-size:%?34?%;font-weight:500;letter-spacing:%?2?%;color:#8c8c8c}.splitLine[data-v-e9c57622]{margin-bottom:12px;width:100%;height:0;opacity:1;border-top:1px solid #e9e9e9}.desc[data-v-e9c57622]{display:flex;flex-direction:column;justify-content:center;align-items:center;font-size:%?24?%;line-height:%?28?%;color:#999}.totalCount[data-v-e9c57622]{width:100%;height:%?66?%;padding:0 %?20?%;margin:%?20?% %?30?% %?32?% %?30?%;display:flex;flex-wrap:nowrap;align-items:center;justify-content:flex-end}.totalCount uni-button[data-v-e9c57622]{color:#fff;background-color:#3e73fe;border:1px solid #3e73fe;border-radius:4px}.popHeader[data-v-e9c57622]{height:%?140?%;line-height:7vh;width:100%;position:fixed;top:%?-100?%;color:#000;background-color:#fff;font-size:18px;font-weight:700;text-align:center;border-radius:12px 12px 0 0}.popBody[data-v-e9c57622]{height:%?750?%;overflow:auto}.none[data-v-e9c57622]{display:none}",""]),t.exports=e},"07c4":function(t,e,a){"use strict";a.r(e);var n=a("b6f1"),i=a("1770");for(var o in i)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(o);a("6ecc");var s=a("828b"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"e9c57622",null,!1,n["a"],void 0);e["default"]=r.exports},"16a0":function(t,e,a){var n=a("074d");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("773033de",n,!0,{sourceMap:!1,shadowMode:!1})},1770:function(t,e,a){"use strict";a.r(e);var n=a("dc65"),i=a.n(n);for(var o in n)["default"].indexOf(o)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(o);e["default"]=i.a},"6ecc":function(t,e,a){"use strict";var n=a("16a0"),i=a.n(n);i.a},b6f1:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return n}));var n={gracePage:a("1367").default,graceDialog:a("6fb7").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[a("my-header",{attrs:{slot:"gHeader",title:"套餐信息"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"infoCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[t._v("基本信息")])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",[a("v-uni-text",[t._v("姓名："+t._s(t.userInfo.name))])],1),a("v-uni-view",[a("v-uni-text",[t._v("年龄："+t._s(t.tjPlan.age))])],1),a("v-uni-view",[a("v-uni-text",[t._v("性别："+t._s("0"===t.tjPlan.gender?"男":"女"))])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",[a("v-uni-text",[t._v("手机："+t._s(t.userInfo.phoneNum))])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",[a("v-uni-text",[t._v("证件号："+t._s(t.userInfo.idNo))])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",[a("v-uni-text",[t._v("选检额度："+t._s(t.selectionQuota))])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",[a("v-uni-text",[t._v("体检时间："+t._s(t.appointDate)+" "+t._s(t.getTimeRange(t.appointmentDetailInfo.appointTime)))])],1)],1)],1),a("v-uni-view",{staticClass:"itemsCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[t._v("套餐项目信息")])],1)],1),t._l(t.requiredItems,(function(e,n){return a("v-uni-view",{key:n},[a("v-uni-view",{class:n>5&&t.contract?"none":""},[a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticClass:"itemLabel"},[a("v-uni-text",[t._v(t._s(e.name))])],1),a("v-uni-view",{staticClass:"itemContent",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.openDialog(e.comments)}}},[a("v-uni-text",[t._v("详情")])],1)],1),a("v-uni-view",{staticClass:"splitLine"})],1)],1)})),a("v-uni-view",{staticClass:"cardBtns"},[a("v-uni-view",[a("v-uni-button",{staticClass:"cancelBtn",attrs:{size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.contract=!t.contract}}},[t._v(t._s(t.contract?"展开更多":"收起"))])],1)],1)],2),a("v-uni-view",{staticClass:"itemsCard grace-box-shadow",staticStyle:{"padding-bottom":"calc(15px - 12px)"}},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[t._v("职业病体检")])],1)],1),t._l(t.occupationalItems,(function(e,n){return a("v-uni-view",{key:n},[a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticClass:"itemLabel"},[a("v-uni-text",[t._v(t._s(e.name))])],1),a("v-uni-view",{staticClass:"itemContent",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.openDialog(e.comments)}}},[a("v-uni-text",[t._v("详情")])],1)],1),a("v-uni-view",{staticClass:"splitLine"})],1)})),0===t.occupationalItems.length?a("v-uni-view",{staticClass:"cardItem desc",staticStyle:{"margin-bottom":"12px"}},[a("v-uni-view",[t._v("无需检查")])],1):t._e()],2),a("v-uni-view",{staticClass:"itemsCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[t._v("选检项目")])],1)],1),t._l(t.optionalItemsSelected,(function(e,n){return a("v-uni-view",{key:n},[a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticClass:"itemLabel"},[a("v-uni-text",[t._v(t._s(e.name))])],1),a("v-uni-view",{staticClass:"itemContent",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.openDialog(e.comments)}}},[a("v-uni-text",[t._v("详情")])],1)],1),a("v-uni-view",{staticClass:"splitLine"})],1)})),a("v-uni-view",{staticClass:"cardItem desc"},[a("v-uni-view",[t._v("加项项目为套餐外项目，超过单位付费额")]),a("v-uni-view",[t._v("度需额外收费，体检时到缴费处结清即可")])],1)],2),a("v-uni-view",{staticStyle:{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center","margin-bottom":"130rpx"}}),a("graceDialog",{ref:"detailDialog",staticClass:"itemDialog",attrs:{title:"检查项目名称",titleSize:"34rpx",show:t.showDialog,isCloseBtn:!1},on:{closeDialog:function(e){arguments[0]=e=t.$handleEvent(e),t.closeDialog.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"dialogContent",attrs:{slot:"content"},slot:"content"},t._l(t.dialogContent,(function(e){return a("v-uni-view",{key:e},[a("v-uni-text",[t._v(t._s(e))])],1)})),1),a("v-uni-view",{staticClass:"dialogBtns",attrs:{slot:"btns"},slot:"btns"},[a("v-uni-text",{staticClass:"grace-dialog-buttons IKnowBtn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeDialog.apply(void 0,arguments)}}},[t._v("我知道了")])],1)],1)],1),a("v-uni-view",{staticClass:"grace-footer grace-nowrap grace-box-shadow",attrs:{slot:"gFooter"},slot:"gFooter"},[a("v-uni-view",{staticClass:"totalCount"},[a("v-uni-view",{staticStyle:{display:"flex","flex-direction":"column","align-items":"flex-end"}},[a("v-uni-view",{staticStyle:{display:"flex"}},[a("v-uni-text",[t._v("共计：")]),a("v-uni-text",{staticStyle:{color:"#EA5C3F"}},[t._v("¥"+t._s(t.totalPrice))]),a("v-uni-text",{staticStyle:{"margin-left":"24rpx"}},[t._v("自费：")]),a("v-uni-text",{staticStyle:{"font-size":"24rpx",color:"#EA5C3F"}},[t._v("¥")]),a("v-uni-text",{staticStyle:{"font-size":"32rpx",color:"#EA5C3F"}},[t._v(t._s(t.selfPay))])],1),a("v-uni-view",[a("v-uni-view",{staticStyle:{"font-size":"22rpx",color:"#EA5C3F"}},[t._v("单位付费¥"+t._s(t.enterprisePay))])],1)],1)],1)],1)],1)},o=[]},dc65:function(t,e,a){"use strict";(function(t){a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("473f"),a("bf0f"),a("c223"),a("fd3c"),a("aa9c"),a("8f71"),a("c1a3"),a("18f7"),a("de6c"),a("d4b5"),a("64aa"),a("2797"),a("4626"),a("5ac7");var i=n(a("2634")),o=n(a("2fdc")),s=n(a("b7c7")),r=n(a("9b1b")),c=n(a("265c")),l=n(a("6fb7")),u=n(a("d544")),d=n(a("0510")),p=a("8f59"),f=n(a("cc2e")),v={name:"TjAppointmentInfo",components:{graceDate:c.default,graceDialog:l.default,graceHeaderAlert:u.default},data:function(){return{contract:!1,showDialog:!1,dialogContent:[],showGraceDate:!1,selectedDate:"",userInfo:{},tjPlan:{},userId:"",tjPlanId:"",adminUserId:"",requiredItems:[],occupationalItems:[],selectionQuota:0,maxPrice:0}},computed:(0,r.default)((0,r.default)({},(0,p.mapGetters)(["tjPlanInfo","appointmentInfo","optionalItemsSelected","optionalItems","appointmentDetailInfo"])),{},{appointDate:function(){return(0,f.default)(this.appointmentDetailInfo.appointDate).format("YYYY-MM-DD")},checkItems:function(){return[].concat((0,s.default)(this.requiredItems),(0,s.default)(this.occupationalItems),(0,s.default)(this.optionalItemsSelected)).reduce((function(e,a){t.log("🍊cur",a.type);var n={checkItemId:a._id,name:a.name,comments:a.comments,price:a.price,payType:a.payType,type:a.type};return e.map.has(n.checkItemId)?"1"===e.map.get(n.checkItemId)&&(e.array=e.array.filter((function(t){return t.checkItemId!==n.checkItemId})),e.array.push(n)):(e.map.set(n.checkItemId,n.payType),e.array.push(n)),e}),{map:new Map,array:[]}).array},totalPrice:function(){return this.appointmentDetailInfo.totalPrice},selfPay:function(){return this.appointmentDetailInfo.selfPay},enterprisePay:function(){return this.appointmentDetailInfo.enterprisePay}}),onLoad:function(t){var e=this;return(0,o.default)((0,i.default)().mark((function a(){return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.userId=t.userId,e.tjPlanId=t.tjPlanId,!e.userId||!e.tjPlanId){a.next=7;break}return a.next=5,e.proxyReserve({userId:e.userId,tjPlanId:e.tjPlanId});case 5:a.next=10;break;case 7:e.tjPlan=e.tjPlanInfo,e.userInfo=e.$store.state.user.userInfo,e.userId=e.$store.state.user.userInfo._id;case 10:e.getRequiredCheckItemList(),e.getOccupationalHealth({harmFactors:JSON.stringify(e.tjPlan.harmFactors||[])}),e.getOptionalCheckItemList();case 13:case"end":return a.stop()}}),a)})))()},created:function(){var e=this;return(0,o.default)((0,i.default)().mark((function a(){return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.log("🍊this.tjPlanInfo",e.tjPlanInfo),a.next=3,e.getTjAppointed(e.tjPlanInfo._id,e.tjPlanInfo.employeeId);case 3:case"end":return a.stop()}}),a)})))()},methods:{selectQuota:function(e){return e=Number(e),t.log("🍊age",e),e>=40?this.tjPlan.selectionQuota||0:this.tjPlan.selectionQuota2||0},getOptionalCheckItemList:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){var a,n,o,s,c;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={organizationId:t.tjPlan.physicalExaminationOrgID,userId:t.userId,tjPlanId:t.tjPlanInfo._id},t.appointmentDetailInfo&&t.appointmentDetailInfo._id&&(a.appointmentId=t.appointmentDetailInfo._id),e.next=4,d.default.getOptionalCheckItemList(a);case 4:n=e.sent,o=[],s=[],t.appointmentDetailInfo&&t.appointmentDetailInfo.checkItems&&(s=t.appointmentDetailInfo.checkItems.map((function(t){return t.checkItemId}))),n.data.forEach((function(t){if(!["乳腺彩超检查","阴道分泌物检查","妇科彩超检查"].includes(t.name)){var e=!1;s.length&&s.includes(t._id)&&(e=!0),o.push((0,r.default)((0,r.default)({},t),{},{checked:e,price:t.oldPrice>=0?t.oldPrice:t.price}))}})),t.$store.commit("setOptionalItems",o),c=o.filter((function(t){return t.checked})),t.$store.commit("setOptionalItemsSelected",c);case 12:case"end":return e.stop()}}),e)})))()},getRequiredCheckItemList:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){var a,n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={organizationId:t.tjPlan.physicalExaminationOrgID,userId:t.userId,tjPlanId:t.tjPlanInfo._id},t.appointmentDetailInfo&&t.appointmentDetailInfo._id&&(a.appointmentId=t.appointmentDetailInfo._id),e.next=4,d.default.getRequiredCheckItemList(a);case 4:n=e.sent,t.requiredItems=n.data.map((function(t){return(0,r.default)((0,r.default)({},t),{},{price:t.oldPrice>=0?t.oldPrice:t.price})})),t.selectionQuota=t.selectQuota(t.tjPlan.age),t.maxPrice=t.requiredItems.filter((function(t){return"0"===t.payType})).reduce((function(t,e){return t+e.price}),0)+t.selectionQuota,t.contract=t.requiredItems.length>5;case 9:case"end":return e.stop()}}),e)})))()},getOccupationalHealth:function(t){var e=this;return(0,o.default)((0,i.default)().mark((function a(){var n,o;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(n=t.harmFactors,!(e.tjPlan.harmFactors&&e.tjPlan.harmFactors.length>0)){a.next=6;break}return a.next=4,d.default.getOccupationalHealth({harmFactors:n,organizationId:e.tjPlan.physicalExaminationOrgID});case 4:o=a.sent,e.occupationalItems=o.data.map((function(t){return(0,r.default)((0,r.default)({},t),{},{price:t.oldPrice>=0?t.oldPrice:t.price})}));case 6:case"end":return a.stop()}}),a)})))()},getTimeRange:function(t){var e=this.appointmentDetailInfo.timeRangeOptions[t];return e.start+"~"+e.end},gotoAddItems:function(){uni.navigateTo({url:"/pages_user/pages/user/addItems"})},openPop:function(){this.$refs.popup.open("bottom"),this.showGraceDate=!0},openDialog:function(t){this.showDialog=!0,this.dialogContent=t.map((function(t){return t.name}))},closeDialog:function(){this.showDialog=!1,this.dialogContent=""},closeDate:function(){this.$refs.graceDate.hide()},showDate:function(){this.$refs.graceDate.open()},changeDate:function(t){var e=this;return(0,o.default)((0,i.default)().mark((function a(){var n,o,s;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e.selectedDate=t,n=e.checkItems.map((function(t){return(0,r.default)((0,r.default)({},t),{},{comments:t.comments.map((function(t){return{cnCode:t.cnCode,name:t.name}}))})})),o={appointDate:new Date(e.selectedDate).setHours(0,0,0,0),tjPlanId:e.tjPlan._id,userId:e.userId,adminUserId:e.adminUserId,appointmentStatus:e.tjPlan.appointmentStatus||0,physicalExaminationOrgId:e.tjPlan.physicalExaminationOrgID,physicalExaminationOrgName:e.tjPlan.physicalExaminationOrgName,address:e.tjPlan.address,checkItems:n,totalPrice:e.totalPrice,enterprisePay:e.enterprisePay,selfPay:e.selfPay,selectionQuota:e.selectionQuota},s=null,!e.appointmentDetailInfo||!e.appointmentDetailInfo._id){a.next=12;break}return o._id=e.appointmentDetailInfo._id,o.tjPlanId=e.$store.state.user.tjPlanInfo._id,a.next=9,d.default.updateTjAppointment(o);case 9:s=a.sent,a.next=15;break;case 12:return a.next=14,d.default.createTjAppointment(o);case 14:s=a.sent;case 15:200===s.status?(e.closeDate(),e.getTjPlan(),uni.navigateBack(),e.$store.state.user.userInfo.employeeId===e.userId?e.$store.commit("setAppointedInfo",s.data||o):e.$store.commit("setAppointedInfo",null),uni.showToast({title:"预约成功"})):uni.showToast({title:"预约失败",icon:"error"});case 16:case"end":return a.stop()}}),a)})))()},proxyReserve:function(t){var e=this;return(0,o.default)((0,i.default)().mark((function a(){var n;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,d.default.getTjPlan(t);case 2:n=a.sent,200===n.status&&(e.userInfo=n.data,e.tjPlan=n.data,e.tjPlan.proxyReserve=e.tjPlanInfo.proxyReserve,e.adminUserId=e.$store.state.user.userInfo.employeeId,n.data.totalPrice?(e.$store.commit("setAppointedInfo",n.data),e.$store.commit("setTjAppointInfo",{_id:n.data.tjPlanId})):(e.$store.commit("setAppointedInfo",null),e.$store.commit("setTjAppointInfo",e.tjPlan)));case 4:case"end":return a.stop()}}),a)})))()},getTjPlan:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){var a,n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,d.default.getTjPlan();case 2:a=e.sent,n=a.data.map((function(t){return(0,r.default)((0,r.default)({},t),{},{contract:t.contract||"-",phoneNum:t.phoneNum||"-"})})),t.$store.commit("setTjAppointInfo",n[0]);case 5:case"end":return e.stop()}}),e)})))()},getTjAppointed:function(t,e){var a=this;return(0,o.default)((0,i.default)().mark((function n(){var o,s;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,d.default.getTjAppointment({tjPlanId:t,employeeId:e});case 2:return o=n.sent,s=o.data,a.$store.commit("setAppointedDetailInfo",s[0]),n.abrupt("return",o);case 6:case"end":return n.stop()}}),n)})))()}}};e.default=v}).call(this,a("ba7c")["default"])}}]);