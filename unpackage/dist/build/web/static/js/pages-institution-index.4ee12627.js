(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-institution-index"],{"0342":function(t,n,i){"use strict";i.r(n);var e=i("128a"),a=i.n(e);for(var o in e)["default"].indexOf(o)<0&&function(t){i.d(n,t,(function(){return e[t]}))}(o);n["default"]=a.a},"05d6":function(t,n,i){"use strict";i.d(n,"b",(function(){return a})),i.d(n,"c",(function(){return o})),i.d(n,"a",(function(){return e}));var e={gracePage:i("1367").default},a=function(){var t=this,n=t.$createElement,i=t._self._c||n;return i("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[i("my-header",{attrs:{slot:"gHeader",title:"体检信息"},slot:"gHeader"}),i("v-uni-view",{staticClass:"grace-body content",attrs:{slot:"gBody"},slot:"gBody"},[i("v-uni-view",{staticClass:"content_main"},[i("v-uni-view",{staticClass:"content_maina"},[i("v-uni-view",{staticClass:"content_text"},[t._v("体检")]),t._l(t.healthCheckData,(function(n,e){return[i("v-uni-view",{key:e+"_0",staticClass:"content_list"},[i("v-uni-view",{staticClass:"right-content"},[i("v-uni-view",{staticClass:"top_img"},[i("v-uni-image",{staticClass:"content_img",attrs:{src:n.image,alt:""}})],1),i("v-uni-view",{staticClass:"top"},[i("v-uni-view",{staticClass:"top_title"},[t._v(t._s(n.title))]),i("v-uni-view",{staticClass:"top_list"},[t._v(t._s(n.description))])],1)],1),i("v-uni-view",{staticClass:"top_btn"},[i("v-uni-text",{staticClass:"btn",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.goToPage(n)}}},[t._v(t._s(n.buttonText))])],1)],1)]}))],2),i("v-uni-view",{staticClass:"content_maina"},[i("v-uni-view",{staticClass:"content_text"},[t._v("职业病诊断管理")]),t._l(t.diseaseDiagnosisData,(function(n,e){return[i("v-uni-view",{key:e+"_0",staticClass:"content_list"},[i("v-uni-view",{staticClass:"right-content"},[i("v-uni-view",{staticClass:"top_img"},[i("v-uni-image",{staticClass:"content_img",attrs:{src:n.image,alt:""}})],1),i("v-uni-view",{staticClass:"top"},[i("v-uni-view",{staticClass:"top_title"},[t._v(t._s(n.title))]),i("v-uni-view",{staticClass:"top_list"},[t._v(t._s(n.description))])],1)],1),i("v-uni-view",{staticClass:"top_btn"},[i("v-uni-text",{staticClass:"btn",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.goToPage(n)}}},[t._v(t._s(n.buttonText))])],1)],1)]}))],2)],1)],1)],1)},o=[]},"06c7":function(t,n,i){var e=i("f22b");e.__esModule&&(e=e.default),"string"===typeof e&&(e=[[t.i,e,""]]),e.locals&&(t.exports=e.locals);var a=i("967d").default;a("fd6001c4",e,!0,{sourceMap:!1,shadowMode:!1})},"0bdc":function(t,n,i){"use strict";i.r(n);var e=i("05d6"),a=i("0342");for(var o in a)["default"].indexOf(o)<0&&function(t){i.d(n,t,(function(){return a[t]}))}(o);i("e17f");var s=i("828b"),c=Object(s["a"])(a["default"],e["b"],e["c"],!1,null,"0f05513f",null,!1,e["a"],void 0);n["default"]=c.exports},"128a":function(t,n,i){"use strict";i("6a54"),Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e={data:function(){return{healthCheckData:[{image:"../../static/physical.png",title:"体检预约",description:"劳动者根据所在单位设定体检计划预约体检机构进行体检",buttonText:"预约",targetPage:"/pages/institution/tjBooking"},{image:"../../static/notebook.png",title:"预约记录",description:"劳动者可查看预约体检机构预约结果",buttonText:"查看",targetPage:"/pages/institution/tjRecord"},{image:"../../static/line.png",title:"体检结果",description:"劳动者可查看体检检测结果及体检报告",buttonText:"查看",targetPage:"/pages/institution/tjResult"}],diseaseDiagnosisData:[{image:"../../static/apartment.png",title:"诊断机构",description:"劳动者可选择辖区内的诊断机构进行职业病诊断申请",buttonText:"申请",targetPage:"/pages/institution/institution"},{image:"../../static/memo.png",title:"申请记录",description:"劳动者可查看历次职业病诊断申请记录",buttonText:"查看",targetPage:"/pages/institution/zdResult"},{image:"../../static/notebook.png",title:"疑似职业病上报查询",description:"劳动者可查看疑似职业病上报记录",buttonText:"查看",targetPage:"/pages/institution/ysReport"}]}},methods:{back:function(){uni.navigateBack()},goToPage:function(t){uni.navigateTo({url:"".concat(t.targetPage)})}},onLoad:function(){}};n.default=e},e17f:function(t,n,i){"use strict";var e=i("06c7"),a=i.n(e);a.a},f22b:function(t,n,i){var e=i("c86c");n=e(!1),n.push([t.i,".content[data-v-0f05513f]{box-sizing:border-box;width:100%;height:100vh;background-color:#f6f6f6;display:flex;flex-direction:column;align-items:center}.content .content_main[data-v-0f05513f]{width:%?690?%;height:100%}.content .content_main .content_maina .content_text[data-v-0f05513f]{margin-top:%?15?%;color:#3d3d3d;font-weight:700}.content .content_main .content_maina .content_list[data-v-0f05513f]{width:100%;box-sizing:border-box;height:%?160?%;background:#fff;display:flex;justify-content:space-between;align-items:center;margin-top:%?15?%;padding:0 %?24?%}.content .content_main .content_maina .content_list .right-content[data-v-0f05513f]{display:flex;align-items:center}.content .content_main .content_maina .content_list .right-content .content_img[data-v-0f05513f]{width:%?112?%;height:%?112?%}.content .content_main .content_maina .content_list .right-content .top[data-v-0f05513f]{margin-left:%?24?%}.content .content_main .content_maina .content_list .right-content .top .top_title[data-v-0f05513f]{font-weight:bolder;font-size:%?32?%;margin-bottom:%?12?%;color:#000}.content .content_main .content_maina .content_list .right-content .top .top_list[data-v-0f05513f]{color:#666;font-size:%?24?%}.content .content_main .content_maina .content_list .top_btn[data-v-0f05513f]{display:flex;margin-left:12px}.content .content_main .content_maina .content_list .top_btn .btn[data-v-0f05513f]{width:50px;height:28px;line-height:28px;text-align:center;font-size:14px;background:#f0f9eb;border-radius:20px;flex-shrink:0;border:%?2?% solid #b3e09c;color:#67c23a}.content .content_main .content_maina[data-v-0f05513f]:nth-child(2){margin-top:15px}",""]),t.exports=n}}]);