(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_train-pages-training-test"],{"16ef":function(t,e,n){"use strict";var i=n("e691"),a=n.n(i);a.a},"2d49":function(t,e,n){"use strict";n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("0518")),s={getTopicsRandom:function(t){return(0,a.default)({url:"manage/testPaper/getTopicsRandom",method:"post",data:t})},addTestRecord:function(t){return(0,a.default)({url:"manage/adminTraining/addTestRecord",method:"post",data:t})},getQuestionnaires:function(t){return(0,a.default)({url:"manage/testPaper/questionnaires",method:"get",data:t})},getTestPaperDetail:function(t){return(0,a.default)({url:"manage/testPaper/getDetail",method:"get",data:t})},addData:function(t){return(0,a.default)({url:"manage/testPaper/answer",method:"post",data:t})},getDeatil:function(t){return(0,a.default)({url:"manage/testPaper/answer/detail",method:"get",data:t})}};e.default=s},4617:function(t,e,n){"use strict";n.r(e);var i=n("d7cb"),a=n.n(i);for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);e["default"]=a.a},"50b5":function(t,e,n){var i=n("c86c");e=i(!1),e.push([t.i,'uni-button[size="mini"][data-v-3866fe0a]{line-height:1.8;margin-top:4px}.grace-list-body-desc[data-v-3866fe0a]{margin-top:10px}.grace-body[data-v-3866fe0a]{\n  /* width: auto; */width:100%;box-sizing:border-box}uni-view[data-v-3866fe0a]{line-height:%?46?%}\n/* 考试基本信息 */.des[data-v-3866fe0a]{display:flex;margin-bottom:%?8?%}.des > uni-view[data-v-3866fe0a]{flex:1;text-align:center}.des .value[data-v-3866fe0a]{font-size:%?40?%;line-height:%?80?%}\n/* 题目 */.points[data-v-3866fe0a]{margin-left:%?20?%;font-size:.9em}.img[data-v-3866fe0a]{margin-bottom:12px}.img uni-image[data-v-3866fe0a]{border:1px solid #ebeef5;padding:5px;width:100px;height:100px;border-radius:2px}.topic[data-v-3866fe0a]{padding:%?20?% 0}.topic .content uni-label[data-v-3866fe0a]{display:block;margin:%?10?% 0;width:100%}.testPaper[data-v-3866fe0a]{overflow:scroll}',""]),t.exports=e},"5ee1":function(t,e,n){"use strict";n.r(e);var i=n("7292"),a=n("4617");for(var s in a)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(s);n("16ef");var r=n("828b"),o=Object(r["a"])(a["default"],i["b"],i["c"],!1,null,"3866fe0a",null,!1,i["a"],void 0);e["default"]=o.exports},7292:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return i}));var i={gracePage:n("1367").default},a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[n("my-header",{attrs:{slot:"gHeader",title:t.questionBankName},slot:"gHeader"}),n("v-uni-view",{staticClass:"grace-body",attrs:{slot:"gBody",id:"gBody"},slot:"gBody"},[n("v-uni-view",{staticClass:"grace-margin-top"},[n("v-uni-scroll-view",{staticClass:"grace-list grace-margin-top"},[t.examination.Judgment?n("v-uni-view",{staticClass:"des"},[n("v-uni-view",[n("v-uni-text",{staticClass:"grey"},[t._v("题目数量")]),n("v-uni-view",{staticClass:"value brown"},[t._v(t._s(t.totleTopic))])],1),n("v-uni-view",[n("v-uni-text",{staticClass:"grey"},[t._v("考试时长")]),t.examination.limitTime?n("v-uni-view",{staticClass:"value brown"},[t._v(t._s(t.showTime))]):n("v-uni-view",{staticClass:"value brown"},[t._v("不限制")])],1),n("v-uni-view",[n("v-uni-text",{staticClass:"grey"},[t._v("考试总分")]),n("v-uni-view",{staticClass:"value brown"},[t._v(t._s(t.totleScore))])],1),n("v-uni-view",[n("v-uni-text",{staticClass:"grey"},[t._v("通过分数")]),n("v-uni-view",{staticClass:"value brown"},[t._v(t._s(t.passingGrate))])],1)],1):t._e(),n("v-uni-view",{staticClass:"testPaper",style:{height:t.mainHeight+"px"}},[t._l(t.testPaper,(function(e,i){return n("v-uni-view",{key:e.id,staticClass:"topic",class:i!=t.testPaper.length-1?"grace-border-b":"",attrs:{id:i}},[n("v-uni-view",{staticClass:"topicType"},[t._v(t._s(i+1)+"、"+t._s(t._f("topicType")(e.topicType))),n("span",{staticClass:"grey points"},[t._v("分值："+t._s(t.topicScores[e.topicType-1])+"分")])]),n("v-uni-view",[t._v(t._s(e.steam||""))]),n("v-uni-view",{staticClass:"img"},t._l(e.steamPic,(function(t){return n("v-uni-image",{key:t._id,attrs:{src:t.staticSrc,"preview-src-list":e.steamPic.map((function(t){return t.staticSrc}))}})})),1),n("v-uni-view",{staticClass:"content"},[2==e.topicType?n("v-uni-checkbox-group",{staticClass:"grace-wrap",on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.checkboxChange(i,e)}}},t._l(e.options,(function(e,i){return n("v-uni-label",{key:e._id,staticClass:"grace-check-item"},[n("v-uni-checkbox",{attrs:{value:i+""}}),t._v(t._s(t.A_J[i])+"\n                  "+t._s(e.optionText))],1)})),1):n("v-uni-radio-group",{on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.radioChange(i,e)}}},t._l(e.options,(function(e,i){return n("v-uni-label",{key:e._id,staticClass:"grace-check-item-v"},[n("v-uni-radio",{attrs:{value:i+""}}),t._v(t._s(t.A_J[i])+"\n                  "+t._s(e.optionText))],1)})),1)],1)],1)})),n("v-uni-view",{staticClass:"grace-line-title"},[n("v-uni-view",{staticClass:"grace-line-title-line"}),n("v-uni-text",{staticClass:"grace-line-title-text grey grace-text-small"},[t._v("到底了")]),n("v-uni-view",{staticClass:"grace-line-title-line"})],1)],2),n("v-uni-view",{staticStyle:{padding:"30rpx 0"}},[n("p",{staticClass:"grey2"},[t._v("共 "+t._s(t.totleTopic)+" 题，剩余\n            "+t._s(t.totleTopic-t.completedNum)+" 题未完成")]),n("v-uni-button",{staticClass:"grace-button grace-margin-top",staticStyle:{"line-height":"80rpx"},attrs:{type:"success"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submit.apply(void 0,arguments)}}},[t._v("交 卷")])],1)],1)],1)],1)],1)},s=[]},"9c50":function(t,e,n){"use strict";(function(t){n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("0518")),s={getHotCourses:function(t){return(0,a.default)({url:"manage/training/courses/getHotCourses",method:"get",param:t})},getClassification:function(t){return(0,a.default)({url:"manage/training/courses/getClassification",method:"get",data:t})},getCourseOne:function(t){return(0,a.default)({url:"manage/training/courses/getCourseOne",method:"get",data:t})},getVideoUrl:function(e){return t.log(e),(0,a.default)({url:"manage/training/courses/getVideoUrl",method:"get",data:e})},updateCourseProgress:function(t){return(0,a.default)({url:"manage/training/courses/updateCourseProgress",method:"post",data:t})},likeCourse:function(t){return(0,a.default)({url:"manage/training/courses/likeCourse",method:"post",data:t})},searchCourse:function(t){return(0,a.default)({url:"manage/training/courses/searchCourse",method:"get",data:t})},getCourseByClass:function(t){return(0,a.default)({url:"manage/training/courses/getCourseByClass",method:"get",data:t})},creatComment:function(t){return(0,a.default)({url:"manage/training/courses/creatComment",method:"post",data:t})},getComments:function(t){return(0,a.default)({url:"manage/training/courses/getComments",method:"get",data:t})},createCourseReply:function(t){return(0,a.default)({url:"manage/training/courses/createReply",method:"post",data:t})},likeCourseComment:function(t){return(0,a.default)({url:"manage/training/courses/likeComment",method:"post",data:t})},getMycourses:function(t){return(0,a.default)({url:"manage/training/courses/getMyclourses",method:"post",data:t})},createBulletScreenComment:function(t){return(0,a.default)({url:"manage/training/courses/createBulletScreenComment",method:"post",data:t})},getBulletScreenComment:function(t){return(0,a.default)({url:"manage/training/courses/getBulletScreenComment",method:"get",data:t})},adminTrainingList:function(t){return(0,a.default)({url:"manage/adminTraining/list",method:"post",data:t})},personalTrainingList:function(t){return(0,a.default)({url:"manage/adminTraining/personalTrainingList",method:"post",data:t})},createPersonalTraining:function(t){return(0,a.default)({url:"manage/adminTraining/createPersonalTraining",method:"post",data:t})},updatePersonalTraining:function(t){return(0,a.default)({url:"manage/adminTraining/updatePersonalTraining",method:"post",data:t})},delPersonalTraining:function(t){return(0,a.default)({url:"manage/adminTraining/delPersonalTraining",method:"post",data:t})},getPersonalTraining:function(t){return(0,a.default)({url:"manage/adminTraining/getPersonalTraining",method:"post",data:t})},getAdminTrainingDetail:function(t){return(0,a.default)({url:"manage/adminTraining/getDetail",method:"post",data:t})},updateCompleteState:function(t){return(0,a.default)({url:"manage/training/courses/updateCompleteState",method:"get",data:t})},getPauseTime:function(t){return(0,a.default)({url:"manage/training/courses/getPauseTime",method:"get",data:t})},employeesTrainingList:function(t){return(0,a.default)({url:"manage/employeeTraining/list",method:"post",data:t})},getSign:function(t){return(0,a.default)({url:"app/user/getSign",method:"post",data:t})},getFacePicture:function(t){return(0,a.default)({url:"app/user/getFacePicture",method:"post",data:t})}},r=s;e.default=r}).call(this,n("ba7c")["default"])},d7cb:function(t,e,n){"use strict";(function(t){n("6a54");var i=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("3471")),s=i(n("b7c7")),r=i(n("2634")),o=i(n("2fdc")),u=i(n("9b1b"));n("e966"),n("c223"),n("9327"),n("fd3c"),n("d4b5"),n("4100"),n("bf0f"),n("2797"),n("64aa"),n("4626"),n("5ac7"),n("aa9c"),n("8f71"),n("f7a5");var c=n("8f59"),l=i(n("9c50")),d=i(n("2d49")),m=i(n("cc2e")),p=i(n("7703")),g={data:function(){return{singleTopics:[],multipleTopics:[],judgmentTopics:[],topics:[],showTestResult:!1,totleTopic:"",totleScore:"",testPaper:[],A_J:["A","B","C","D","E","F","G","H","I","J"],completedTopic:[],completedIndex:[],completedNum:0,testResult:[],resultStatistics:{},startTime:new Date,questionBankID:"",questionBankName:"",passingGrate:0,examination:{},remainingTime:0,showTime:"",timer:null,personalTrainingId:"",courseId:"",topicScores:[],topicNum:[],mainHeight:1100,bigTest:!1,originTraining:null,trainingDetail:null,coursesList:[],remainingNum:"",recognition:!1,option:"",hasImage:!1,companyId:""}},onLoad:function(e){this.option=e,t.log(e,"开始之前需要调用摄像头拍照，进行验证和培训时上传的头像是否是同一个人"),this.personalTrainingId=e.personalTrainingId||"",this.questionBankName=e.name||"",this.questionBankID=e.id||"",this.courseId=e.courseID||"",this.bigTest=e.bigTest,this.companyId=e.companyId},onShow:function(){var t=!!this.completedTopic.length;this.completedTopic=[],this.completedIndex=[],this.completedNum=0,this.startTime=new Date,this.timer&&clearInterval(this.timer),t&&this.makeTestPaper(),uni.pageScrollTo({scrollTop:0,duration:10})},onUnload:function(){clearInterval(this.timer)},watch:{remainingTime:function(t){var e=m.default.duration(parseInt(t),"second");this.showTime="".concat(e.hours()>0?e.hours():"").concat(e.hours()>0?":":"","\n                    ").concat(e.minutes()>0?e.minutes():"").concat(e.minutes()>0?":":"","\n                    ").concat(e.seconds())},$route:function(t,e){"/pages_train/pages/training/test"===t.path&&this.phoneVerify()},searchResBack:function(t){this.phoneVerify()}},beforeDestroy:function(){this.timer&&clearInterval(this.timer)},mounted:function(){this.mainHeight=(this.windowHeight-500)/2,this.personalTrainingId?this.getPersonalTrainingDetail():uni.showToast({title:"没有拿到id",icon:"none"}),this.questionBankName=this.questionBankName.endsWith("考试")||this.questionBankName.endsWith("试题")?"《".concat(this.questionBankName,"》"):"《".concat(this.questionBankName,"》考试")},computed:(0,u.default)({},(0,c.mapGetters)({userInfo:"userInfo",windowHeight:"windowHeight",searchResBack:"searchResBack"})),methods:{phoneVerify:function(){var e=this;try{uni.chooseMedia({count:1,sizeType:["original"],mediaType:["image"],sourceType:["camera"],camera:"front",success:function(n){var i=n.tempFiles[0].tempFilePath;this.hasImage=!0,e.option.companyId instanceof Array&&(e.option.companyId=e.option.companyId[0]),uni.uploadFile({url:p.default.apiServer+"app/user/ExmaVerifyImage",filePath:i,name:"file",formData:{EnterpriseID:e.option.companyId,imageUserId:e.option._id,personalTrainingId:e.option.personalTrainingId},fail:function(e){t.log("上传文件错误",e)},success:function(){var n=(0,o.default)((0,r.default)().mark((function n(i){var a;return(0,r.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:t.log(i,"uploadFileRes嗡嗡嗡嗡嗡嗡"),a=JSON.parse(i.data).data,200===i.statusCode&&(a?uni.showToast({title:"验证成功，开始考试",duration:800}):uni.showModal({confirmText:"确认",title:"验证失败",content:"重新拍照，再次进行验证",confirmColor:"#3B8BFF",cancelColor:"#222222",success:function(t){t.confirm?e.phoneVerify():t.cancel&&uni.navigateBack()}}));case 3:case"end":return n.stop()}}),n)})));return function(t){return n.apply(this,arguments)}}()})},fail:function(e){t.log(e)},complete:function(e){t.log(e,"最后一步")}})}catch(n){t.log(n,"调用失败了吗")}},getPersonalTrainingDetail:function(){var t=this;l.default.getPersonalTraining({_id:this.personalTrainingId}).then((function(e){if(200==e.status&&e.data){t.coursesList=e.data.coursesList,t.trainingDetail=e.data.detail,t.originTraining=t.trainingDetail.adminTrainingId||t.trainingDetail.employeesTrainingPlanId,t.bigTest&&(t.questionBankName=t.originTraining.name||"");var n=t.examination=t.originTraining.examination;if(t.examination.timesLimit&&(t.remainingNum=t.examination.timesLimit-t.trainingDetail.bigTestList.length,0==t.remainingNum))return void t.clearPersonalTraining();t.totleTopic=n.singleChoice.num+n.multipleChoice.num+n.Judgment.num,t.topicScores=[t.examination.singleChoice.scores,t.examination.multipleChoice.scores,t.examination.Judgment.scores],t.makeTestPaper()}else uni.showToast({title:"培训详情未获取到",icon:"none"})}))},clearPersonalTraining:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.examination.timesLimit){e.next=2;break}return e.abrupt("return");case 2:uni.showModal({title:"! 提示",content:"很遗憾，机会用完，考试未通过，请重新开始培训。",showCancel:!1,success:function(e){if(e.confirm)if(t.bigTest)l.default.updatePersonalTraining({_id:t.personalTrainingId,status:!1}).then((function(e){200==e.status&&uni.navigateTo({url:2==t.trainingDetail.trainingType?"/pages_train/pages/training/myCourses":"/pages_train/pages/training/myTraining"})}));else{var n=t.trainingDetail.courses,i=n.map((function(e,n){var i=JSON.parse(JSON.stringify(e));return e.coursesId==t.courseId&&(i={chapterPosition:t.coursesList[n]&&t.coursesList[n].sort[0]?t.coursesList[n].sort[0].ID:e.chapterPosition,completeState:!1,coursePlayProgress:e.coursePlayProgress.map((function(t){return{chapterID:t.chapterID,classHours:t.classHours,completeState:!1,duration:t.duration,totalTime:0,videoProgress:0,_id:t._id}})),courseType:e.courseType,coursesId:e.coursesId,like:e.like,testList:[],testStatus:!1,_id:e._id}),i}));l.default.updatePersonalTraining({_id:t.personalTrainingId,courses:i}).then((function(e){200==e.status&&uni.navigateTo({url:"/pages/training/detail?personalTrainingId=".concat(t.personalTrainingId)})}))}}});case 3:case"end":return e.stop()}}),e)})))()},submit:function(){var t=this;this.totleTopic==this.completedNum?this.makeTestResult():uni.showModal({content:"本试卷共".concat(this.totleTopic,"道题，你已答题").concat(this.completedNum,"道，是否确认提交?"),success:function(e){e.confirm?(t.makeTestResult(),t.timer&&clearInterval(t.timer)):e.cancel}})},makeTestResult:function(){var t=this,e=0;this.testResult=[],this.testPaper.forEach((function(n,i){var a=0,s=!1,r=t.completedTopic[i].map((function(t){return Number(t)})),o=r.sort((function(t,e){return t-e}));o.length&&(o.join(";")==n.answer.join(";")?(a=t.topicScores[n.topicType-1],s=!0):o.every((function(t){return n.answer.includes(t)}))&&(a=+(t.topicScores[1]/4).toFixed(1))),t.testResult.push({topicType:n.topicType,answer:n.answer,myAnswer:o,score:a,topicId:n._id,correctAnswer:s}),e+=a})),this.resultStatistics={scoreLine:this.passingGrate,totleScore:this.totleScore,submitTime:new Date,spendTime:(new Date).getTime()-this.startTime.getTime(),topicScores:this.topicScores,topicNum:this.topicNum,actualScore:parseInt(e)},this.addTestRecord(),this.$store.commit("setCurTest",{testResult:this.testResult,resultStatistics:this.resultStatistics,testPaper:this.testPaper}),uni.navigateTo({url:"/pages_train/pages/training/testResult?personalTrainingId=".concat(this.personalTrainingId,"&userinfo=").concat(this.userInfo,"&companyId=").concat(this.companyId)})},addTestRecord:function(){""!==this.remainingNum&&this.remainingNum--,this.timer&&(clearInterval(this.timer),this.timer=null);var t=this.courseId;this.bigTest&&(t=""),d.default.addTestRecord({personalTrainingId:this.personalTrainingId||"",courseId:t,testResult:this.testResult,resultStatistics:this.resultStatistics}).then((function(t){})),this.examination.timesLimit&&this.remainingNum<=0&&this.resultStatistics.actualScore<this.resultStatistics.scoreLine&&this.clearPersonalTraining()},prompt:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(t.remainingNum<=0)){e.next=3;break}return t.clearPersonalTraining(),e.abrupt("return");case 3:"本次考试满分为 ".concat(t.totleScore," 分，").concat(t.passingGrate," 分及格，您共有 ").concat(t.examination.timesLimit," 次考试机会，还剩余 ").concat(t.remainingNum," 次机会，如均未通过，需要重新学习和考试，请谨慎答题。");case 4:case"end":return e.stop()}}),e)})))()},makeTestPaper:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){var n,i,o,u,c,l,m,p,g,f,h,v,T,w;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!=t.topics.length){e.next=23;break}if(!t.bigTest){e.next=15;break}n=[],t.coursesList.forEach((function(t){n.includes(t.questionBank)||n.push(t.questionBank)})),i=0;case 5:if(!(i<n.length)){e.next=13;break}return e.next=8,d.default.getTopicsRandom({questionBankID:n[i]});case 8:u=e.sent,u&&u.data&&(o=t.topics).push.apply(o,(0,s.default)(u.data));case 10:i++,e.next=5;break;case 13:e.next=20;break;case 15:if(!t.questionBankID){e.next=20;break}return e.next=18,d.default.getTopicsRandom({questionBankID:t.questionBankID});case 18:c=e.sent,t.topics=c.data||[];case 20:t.singleTopics=t.topics.filter((function(t){return 1==t.topicType})),t.multipleTopics=t.topics.filter((function(t){return 2==t.topicType})),t.judgmentTopics=t.topics.filter((function(t){return 3==t.topicType}));case 23:if(0!=t.topics.length){e.next=27;break}return t.timer&&clearInterval(t.timer),uni.showModal({title:"！提示",content:"题库中未上传试题，请联系客服。",success:function(t){t.confirm&&uni.navigateBack({})}}),e.abrupt("return");case 27:l=t.singleTopics.sort((function(){return Math.random()-.5})),m=t.judgmentTopics.sort((function(){return Math.random()-.5})),p=t.multipleTopics.sort((function(){return Math.random()-.5})),t.topics.length<=t.totleTopic?(t.testPaper=l.concat(m).concat(p),t.topicNum=[l.length,p.length,m.length]):(g=l.length>t.examination.singleChoice.num?l.slice(0,t.examination.singleChoice.num):l,f=m.length>t.examination.Judgment.num?m.slice(0,t.examination.Judgment.num):m,h=p.length>t.examination.multipleChoice.num?p.slice(0,t.examination.multipleChoice.num):p,t.testPaper=g.concat(f).concat(h),t.topicNum=[g.length,h.length,f.length]),t.totleTopic=t.testPaper.length,v=0,t.topicNum.forEach((function(e,n){v+=e*t.topicScores[n]})),t.totleScore=v,t.passingGrate=parseInt(Math.round(t.totleScore*t.examination.passingGrate/100)),T=(0,a.default)(t.testPaper);try{for(T.s();!(w=T.n()).done;)w.value,t.completedTopic.push([]),t.completedIndex.push(0)}catch(r){T.e(r)}finally{T.f()}if(!t.examination.timesLimit){e.next=43;break}return e.next=41,t.prompt();case 41:e.next=44;break;case 43:t.examination.limitTime&&t.countdown();case 44:case"end":return e.stop()}}),e)})))()},countdown:function(){var t=this;if(this.examination.limitTime){var e=60*this.examination.limitTime;this.timer&&clearInterval(this.timer),this.timer=setInterval((function(){t.remainingTime=e--,0==t.remainingTime&&(clearInterval(t.timer),t.makeTestResult())}),1e3)}},radioChange:function(t,e){var n=this;this.completedTopic[t][0]=e.detail.value,this.completedIndex=this.completedTopic.map((function(t){return t.length})),this.completedNum=0,this.completedIndex.forEach((function(t){t&&n.completedNum++}))},checkboxChange:function(t,e){var n=this;this.completedTopic[t]=e.detail.value,this.completedIndex=this.completedTopic.map((function(t){return t.length})),this.completedNum=0,this.completedIndex.forEach((function(t){t&&n.completedNum++}))}},filters:{topicType:function(t){switch(+t){case 1:return"单选题";case 2:return"多选题";case 3:return"判断题";case 4:return"填空题";case 5:return"问答题"}}}};e.default=g}).call(this,n("ba7c")["default"])},e691:function(t,e,n){var i=n("50b5");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("967d").default;a("d038f822",i,!0,{sourceMap:!1,shadowMode:!1})}}]);