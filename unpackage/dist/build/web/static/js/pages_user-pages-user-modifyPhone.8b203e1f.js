(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-user-modifyPhone"],{"1a8c":function(e,t,r){"use strict";r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return i})),r.d(t,"a",(function(){return n}));var n={gracePage:r("1367").default},a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[r("my-header",{attrs:{slot:"gHeader",title:"修改手机号"},slot:"gHeader"}),r("v-uni-view",{staticClass:"grace-body",attrs:{slot:"gBody"},slot:"gBody"},[r("v-uni-view",{staticClass:"marginTop"},[r("v-uni-view",{staticClass:"login-title margin-top"},[e._v("请注意：修改手机号将同步修改用户名！")])],1),r("v-uni-form",{staticClass:"grace-form",staticStyle:{"margin-top":"80rpx"},on:{submit:function(t){arguments[0]=t=e.$handleEvent(t),e.loginNow.apply(void 0,arguments)}}},[r("v-uni-view",{staticClass:"grace-form-item grace-border-b"},[r("v-uni-view",{staticClass:"grace-pnper"},[r("v-uni-picker",{staticStyle:{"text-align":"left"},attrs:{value:e.pnpre,range:e.pnpres,name:"pn_pre"},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.changePre.apply(void 0,arguments)}}},[r("v-uni-text",{staticClass:"grace-text"},[e._v("+"+e._s(e.pnpres[e.pnpre]))]),r("v-uni-text",{staticClass:"grace-text grace-icons icon-arrow-down",staticStyle:{"margin-left":"10rpx"}})],1)],1),r("v-uni-view",{staticClass:"grace-form-body"},[r("v-uni-input",{staticClass:"grace-form-input",attrs:{type:"number",name:"pn",placeholder:"请输入新手机号码"},model:{value:e.phone,callback:function(t){e.phone="string"===typeof t?t.trim():t},expression:"phone"}})],1)],1),r("v-uni-view",{staticClass:"grace-form-item"},[r("v-uni-text",{staticClass:"grace-form-label"},[e._v("短信验证码")]),r("v-uni-view",{staticClass:"grace-form-body"},[r("v-uni-input",{staticClass:"grace-form-input",attrs:{type:"number",name:"yzm",placeholder:"请输入验证码"},model:{value:e.code,callback:function(t){e.code="string"===typeof t?t.trim():t},expression:"code"}})],1),r("v-uni-view",{staticClass:"login-sendmsg-btn grace-bg-blue grace-border-radius",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getVCode.apply(void 0,arguments)}}},[e._v(e._s(e.vcodeBtnName))])],1),r("v-uni-view",{staticClass:"grace-margin-top"},[r("v-uni-button",{staticClass:"grace-button grace-border-radius grace-gtbg-blue",attrs:{"form-type":"submit",type:"primary"}},[e._v("提交")])],1)],1)],1)],1)},i=[]},6740:function(e,t,r){"use strict";r("6a54");var n=r("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,r("5c47"),r("0506");var a=n(r("9b1b")),i=n(r("570a")),s=r("8f59"),o=r("d924"),c={data:function(){return{pnpre:0,pnpres:["86","01","11","26","520"],vcodeBtnName:"获取验证码",countNum:120,countDownTimer:null,phone:"",code:""}},computed:(0,a.default)({},(0,s.mapGetters)({hasLogin:"hasLogin",userInfo:"userInfo"})),beforeDestroy:function(){clearInterval(this.countDownTimer)},methods:{changePhone:function(){var e=this;i.default.updateUserInfo({phoneNum:this.phone,countryCode:this.pnpres[this.pnpre],messageCode:this.code}).then((function(t){200==t.status?(e.$store.commit("login",(0,a.default)((0,a.default)({},e.userInfo),{},{phoneNum:e.phone,userName:e.phone})),uni.showToast({title:"手机号码修改成功",icon:"success"}),setTimeout((function(){uni.navigateBack({})}),1500)):uni.showToast({title:t.message,icon:"none"})}))},changePre:function(e){this.pnpre=e.detail.value},loginNow:function(e){var t=e.detail.value,r=o.check(t,[{name:"pn",checkType:"phone",errorMsg:"请填写正确的手机号"},{name:"yzm",checkType:"string",checkRule:"4,6",errorMsg:"请正确填写短信验证码"}]);r?this.changePhone():uni.showToast({title:o.error,icon:"none"})},getVCode:function(){if(!/^[1][0-9]{10}$/.test(this.phone))return uni.showToast({title:"请正确填写手机号码",icon:"none"}),!1;"获取验证码"!=this.vcodeBtnName&&"重新发送"!=this.vcodeBtnName||(this.vcodeBtnName="发送中...",this.sendCode())},sendCode:function(){var e=this;i.default.sendVerificationCode({phoneNum:this.phone,countryCode:this.pnpres[this.pnpre],messageType:"7",_id:this.userInfo._id}).then((function(t){200==t.status?(uni.showToast({title:"短信已发送，请注意查收",icon:"success"}),e.countNum=120,e.countDownTimer=setInterval(function(){this.countDown()}.bind(e),1e3)):uni.showToast({title:t.message||"验证码发送失败",icon:"none"})}))},countDown:function(){if(this.countNum<1)return clearInterval(this.countDownTimer),void(this.vcodeBtnName="重新发送");this.countNum--,this.vcodeBtnName=this.countNum+"秒重发"}}};t.default=c},"6dad":function(e,t,r){"use strict";r.r(t);var n=r("1a8c"),a=r("f795");for(var i in a)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return a[e]}))}(i);r("e284");var s=r("828b"),o=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"09029a45",null,!1,n["a"],void 0);t["default"]=o.exports},c60d:function(e,t,r){var n=r("d759");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var a=r("967d").default;a("126c58a8",n,!0,{sourceMap:!1,shadowMode:!1})},d759:function(e,t,r){var n=r("c86c");t=n(!1),t.push([e.i,".login-title[data-v-09029a45]{text-align:center;font-size:%?34?%;font-weight:500;color:#f37b1d}.marginTop[data-v-09029a45]{margin-top:%?100?%;text-align:center}.logo[data-v-09029a45]{width:%?250?%;height:%?68?%}.login-sendmsg-btn[data-v-09029a45]{height:%?60?%;width:%?200?%;flex-shrink:0;margin-left:%?30?%;text-align:center;background-color:#3688ff;line-height:%?60?%;font-size:%?26?%}.grace-pnper[data-v-09029a45]{width:%?168?%;flex-shrink:0}.grace-form-label[data-v-09029a45]{width:%?168?%}.grace-form-input[data-v-09029a45]{text-align:left}.grace-form-item[data-v-09029a45]{padding:%?10?% 0}.grace-login-three-items[data-v-09029a45]{display:block;width:%?88?%;height:%?88?%;line-height:%?88?%;font-size:%?60?%;color:#3688ff;text-align:center;margin:%?10?%}",""]),e.exports=t},d924:function(e,t,r){r("d4b5"),r("5c47"),r("a1c1"),r("23f4"),r("7d2f"),r("9c4e"),r("ab80"),r("0506"),r("64aa"),r("5ef2"),e.exports={error:"",check:function(e,t){e=JSON.stringify(e);for(var r=JSON.parse(e),n=0;n<t.length;n++){if(!t[n].checkType)return!0;if(!t[n].name)return!0;if(!t[n].errorMsg)return!0;if(!r[t[n].name]||""==r[t[n].name])return this.error=t[n].errorMsg,!1;switch("string"==typeof r[t[n].name]&&(r[t[n].name]=r[t[n].name].replace(/\s/g,"")),t[n].checkType){case"string":var a=new RegExp("^.{"+t[n].checkRule+"}$");if(!a.test(r[t[n].name]))return this.error=t[n].errorMsg,!1;break;case"int":var i=t[n].checkRule.split(",");t.length<2?(i[0]=Number(i[0])-1,i[1]=""):(i[0]=Number(i[0])-1,i[1]=Number(i[1])-1);a=new RegExp("^(-[1-9]|[1-9])[0-9]{"+i[0]+","+i[1]+"}$");if(!a.test(r[t[n].name]))return this.error=t[n].errorMsg,!1;break;case"between":if(!this.isNumber(r[t[n].name]))return this.error=t[n].errorMsg,!1;var s=t[n].checkRule.split(",");if(s[0]=Number(s[0]),s[1]=Number(s[1]),r[t[n].name]>s[1]||r[t[n].name]<s[0])return this.error=t[n].errorMsg,!1;break;case"betweenD":a=/^-?\d+$/;if(!a.test(r[t[n].name]))return this.error=t[n].errorMsg,!1;s=t[n].checkRule.split(",");if(s[0]=Number(s[0]),s[1]=Number(s[1]),r[t[n].name]>s[1]||r[t[n].name]<s[0])return this.error=t[n].errorMsg,!1;break;case"betweenF":a=/^-?[0-9][0-9]?.+[0-9]+$/;if(!a.test(r[t[n].name]))return this.error=t[n].errorMsg,!1;s=t[n].checkRule.split(",");if(s[0]=Number(s[0]),s[1]=Number(s[1]),r[t[n].name]>s[1]||r[t[n].name]<s[0])return this.error=t[n].errorMsg,!1;break;case"same":if(r[t[n].name]!=t[n].checkRule)return this.error=t[n].errorMsg,!1;break;case"notsame":if(r[t[n].name]==t[n].checkRule)return this.error=t[n].errorMsg,!1;break;case"email":a=/^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;if(!a.test(r[t[n].name]))return this.error=t[n].errorMsg,!1;break;case"phoneno":a=/^1[0-9]{10,10}$/;if(!a.test(r[t[n].name]))return this.error=t[n].errorMsg,!1;break;case"zipcode":a=/^[0-9]{6}$/;if(!a.test(r[t[n].name]))return this.error=t[n].errorMsg,!1;break;case"reg":a=new RegExp(t[n].checkRule);if(!a.test(r[t[n].name]))return this.error=t[n].errorMsg,!1;break;case"in":if(-1==t[n].checkRule.indexOf(r[t[n].name]))return this.error=t[n].errorMsg,!1;break;case"notnull":if(null==r[t[n].name]||r[t[n].name].length<1)return this.error=t[n].errorMsg,!1;break;case"samewith":if(r[t[n].name]!=r[t[n].checkRule])return this.error=t[n].errorMsg,!1;break;case"numbers":a=new RegExp("^[0-9]{"+t[n].checkRule+"}$");if(!a.test(r[t[n].name]))return this.error=t[n].errorMsg,!1;break}}return!0},isNumber:function(e){return e=Number(e),NaN!=e}}},e284:function(e,t,r){"use strict";var n=r("c60d"),a=r.n(n);a.a},f795:function(e,t,r){"use strict";r.r(t);var n=r("6740"),a=r.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){r.d(t,e,(function(){return n[e]}))}(i);t["default"]=a.a}}]);