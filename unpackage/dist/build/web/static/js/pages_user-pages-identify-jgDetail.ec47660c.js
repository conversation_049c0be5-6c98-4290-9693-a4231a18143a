(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-identify-jgDetail"],{"2a07":function(i,e,n){"use strict";n.r(e);var o=n("4f9bc"),t=n("569b5");for(var l in t)["default"].indexOf(l)<0&&function(i){n.d(e,i,(function(){return t[i]}))}(l);n("4dcc");var c=n("828b"),u=Object(c["a"])(t["default"],o["b"],o["c"],!1,null,"14fd6023",null,!1,o["a"],void 0);e["default"]=u.exports},"2bf0":function(i,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return t})),n.d(e,"a",(function(){}));var o=function(){var i=this,e=i.$createElement,n=i._self._c||e;return n("v-uni-view",{staticClass:"u-icon",class:["u-icon--"+i.labelPos],on:{click:function(e){arguments[0]=e=i.$handleEvent(e),i.clickHandler.apply(void 0,arguments)}}},[i.isImg?n("v-uni-image",{staticClass:"u-icon__img",style:[i.imgStyle,i.$u.addStyle(i.customStyle)],attrs:{src:i.name,mode:i.imgMode}}):n("v-uni-text",{staticClass:"u-icon__icon",class:i.uClasses,style:[i.iconStyle,i.$u.addStyle(i.customStyle)],attrs:{"hover-class":i.hoverClass}},[i._v(i._s(i.icon))]),""!==i.label?n("v-uni-text",{staticClass:"u-icon__label",style:{color:i.labelColor,fontSize:i.$u.addUnit(i.labelSize),marginLeft:"right"==i.labelPos?i.$u.addUnit(i.space):0,marginTop:"bottom"==i.labelPos?i.$u.addUnit(i.space):0,marginRight:"left"==i.labelPos?i.$u.addUnit(i.space):0,marginBottom:"top"==i.labelPos?i.$u.addUnit(i.space):0}},[i._v(i._s(i.label))]):i._e()],1)},t=[]},"33a2":function(i,e,n){var o=n("c86c");e=o(!1),e.push([i.i,'uni-view[data-v-59765974], uni-scroll-view[data-v-59765974], uni-swiper-item[data-v-59765974]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}@font-face{font-family:uicon-iconfont;src:url(https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf) format("truetype")}.u-icon[data-v-59765974]{display:flex;align-items:center}.u-icon--left[data-v-59765974]{flex-direction:row-reverse;align-items:center}.u-icon--right[data-v-59765974]{flex-direction:row;align-items:center}.u-icon--top[data-v-59765974]{flex-direction:column-reverse;justify-content:center}.u-icon--bottom[data-v-59765974]{flex-direction:column;justify-content:center}.u-icon__icon[data-v-59765974]{font-family:uicon-iconfont;position:relative;display:flex;flex-direction:row;align-items:center}.u-icon__icon--primary[data-v-59765974]{color:#3c9cff}.u-icon__icon--success[data-v-59765974]{color:#5ac725}.u-icon__icon--error[data-v-59765974]{color:#f56c6c}.u-icon__icon--warning[data-v-59765974]{color:#f9ae3d}.u-icon__icon--info[data-v-59765974]{color:#909399}.u-icon__img[data-v-59765974]{height:auto;will-change:transform}.u-icon__label[data-v-59765974]{line-height:1}',""]),i.exports=e},4194:function(i,e,n){var o=n("33a2");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[i.i,o,""]]),o.locals&&(i.exports=o.locals);var t=n("967d").default;t("39d62e96",o,!0,{sourceMap:!1,shadowMode:!1})},"4dcc":function(i,e,n){"use strict";var o=n("d2b4"),t=n.n(o);t.a},"4f9bc":function(i,e,n){"use strict";n.d(e,"b",(function(){return t})),n.d(e,"c",(function(){return l})),n.d(e,"a",(function(){return o}));var o={uniNavBar:n("27af").default,uIcon:n("aa10").default},t=function(){var i=this,e=i.$createElement,o=i._self._c||e;return o("v-uni-view",{staticClass:"institution"},[o("uni-nav-bar",{attrs:{leftWidth:"180rpx",fixed:!0,"background-color":"#007AFF",border:"{false}"},on:{clickLeft:function(e){arguments[0]=e=i.$handleEvent(e),i.back.apply(void 0,arguments)}}},[o("template",{attrs:{slot:"left"},slot:"left"},[o("v-uni-view",{staticClass:"nav-left"},[o("v-uni-image",{attrs:{src:n("00a9"),mode:""}}),i._v("机构详情")],1)],1)],2),o("v-uni-view",{staticClass:"section-body"},[o("v-uni-view",{staticClass:"jg-detail"},[o("v-uni-view",{staticClass:"row"},[o("v-uni-view",{staticClass:"label"},[i._v("机构名称：")]),o("v-uni-view",{staticClass:"desc"},[i._v(i._s(i.institutionDetail.institutionName))])],1),o("v-uni-view",{staticClass:"row"},[o("v-uni-view",{staticClass:"label"},[i._v("联系人：")]),o("v-uni-view",{staticClass:"desc"},[i._v(i._s(i.institutionDetail.contactPerson))])],1),o("v-uni-view",{staticClass:"row phone"},[o("v-uni-view",{staticClass:"label"},[i._v("联系电话：")]),o("v-uni-view",{staticClass:"desc"},[i._v(i._s(i.institutionDetail.contactPhone))]),o("u-icon",{attrs:{name:"phone-fill",color:"#409EFF",size:"20"}})],1),o("v-uni-view",{staticClass:"row address"},[o("v-uni-view",{staticClass:"label"},[i._v("地址：")]),o("v-uni-view",{staticClass:"desc"},[i._v(i._s(i.institutionDetail.address)+"4号")]),o("u-icon",{attrs:{name:"map-fill",color:"#409EFF",size:"20"}})],1)],1)],1)],1)},l=[]},"569b5":function(i,e,n){"use strict";n.r(e);var o=n("8f5d"),t=n.n(o);for(var l in o)["default"].indexOf(l)<0&&function(i){n.d(e,i,(function(){return o[i]}))}(l);e["default"]=t.a},"60db":function(i,e,n){i.exports=n.p+"assets/uni.75745d34.ttf"},"67fa":function(i,e,n){"use strict";n.r(e);var o=n("df08"),t=n("8839");for(var l in t)["default"].indexOf(l)<0&&function(i){n.d(e,i,(function(){return t[i]}))}(l);n("69f7");var c=n("828b"),u=Object(c["a"])(t["default"],o["b"],o["c"],!1,null,"5c0264f4",null,!1,o["a"],void 0);e["default"]=u.exports},"69f7":function(i,e,n){"use strict";var o=n("cf5b"),t=n.n(o);t.a},8839:function(i,e,n){"use strict";n.r(e);var o=n("f10a"),t=n.n(o);for(var l in o)["default"].indexOf(l)<0&&function(i){n.d(e,i,(function(){return o[i]}))}(l);e["default"]=t.a},"88d0":function(i,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("aa9c"),n("4626"),n("5ac7"),n("5ef2");var t=o(n("9abe")),l=o(n("ad57")),c={name:"u-icon",data:function(){return{}},mixins:[uni.$u.mpMixin,uni.$u.mixin,l.default],computed:{uClasses:function(){var i=[];return i.push(this.customPrefix+"-"+this.name),this.color&&uni.$u.config.type.includes(this.color)&&i.push("u-icon__icon--"+this.color),i},iconStyle:function(){var i={};return i={fontSize:uni.$u.addUnit(this.size),lineHeight:uni.$u.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:uni.$u.addUnit(this.top)},this.color&&!uni.$u.config.type.includes(this.color)&&(i.color=this.color),i},isImg:function(){return-1!==this.name.indexOf("/")},imgStyle:function(){var i={};return i.width=this.width?uni.$u.addUnit(this.width):uni.$u.addUnit(this.size),i.height=this.height?uni.$u.addUnit(this.height):uni.$u.addUnit(this.size),i},icon:function(){return t.default["uicon-"+this.name]||this.name}},methods:{clickHandler:function(i){this.$emit("click",this.index),this.stop&&this.preventEvent(i)}}};e.default=c},"8f5d":function(i,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t=o(n("2634")),l=o(n("2fdc")),c=o(n("f7ce")),u={data:function(){return{id:"",institutionDetail:{}}},created:function(i){this.getInsDetail()},onLoad:function(i){this.id=i},methods:{getInsDetail:function(){var i=this;return(0,l.default)((0,t.default)().mark((function e(){var n;return(0,t.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.default.getInstitutionDetail(i.id);case 2:n=e.sent,i.institutionDetail=n.data;case 4:case"end":return e.stop()}}),e)})))()},back:function(){uni.navigateBack()}}};e.default=u},"9abe":function(i,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={"uicon-level":"","uicon-column-line":"","uicon-checkbox-mark":"","uicon-folder":"","uicon-movie":"","uicon-star-fill":"","uicon-star":"","uicon-phone-fill":"","uicon-phone":"","uicon-apple-fill":"","uicon-chrome-circle-fill":"","uicon-backspace":"","uicon-attach":"","uicon-cut":"","uicon-empty-car":"","uicon-empty-coupon":"","uicon-empty-address":"","uicon-empty-favor":"","uicon-empty-permission":"","uicon-empty-news":"","uicon-empty-search":"","uicon-github-circle-fill":"","uicon-rmb":"","uicon-person-delete-fill":"","uicon-reload":"","uicon-order":"","uicon-server-man":"","uicon-search":"","uicon-fingerprint":"","uicon-more-dot-fill":"","uicon-scan":"","uicon-share-square":"","uicon-map":"","uicon-map-fill":"","uicon-tags":"","uicon-tags-fill":"","uicon-bookmark-fill":"","uicon-bookmark":"","uicon-eye":"","uicon-eye-fill":"","uicon-mic":"","uicon-mic-off":"","uicon-calendar":"","uicon-calendar-fill":"","uicon-trash":"","uicon-trash-fill":"","uicon-play-left":"","uicon-play-right":"","uicon-minus":"","uicon-plus":"","uicon-info":"","uicon-info-circle":"","uicon-info-circle-fill":"","uicon-question":"","uicon-error":"","uicon-close":"","uicon-checkmark":"","uicon-android-circle-fill":"","uicon-android-fill":"","uicon-ie":"","uicon-IE-circle-fill":"","uicon-google":"","uicon-google-circle-fill":"","uicon-setting-fill":"","uicon-setting":"","uicon-minus-square-fill":"","uicon-plus-square-fill":"","uicon-heart":"","uicon-heart-fill":"","uicon-camera":"","uicon-camera-fill":"","uicon-more-circle":"","uicon-more-circle-fill":"","uicon-chat":"","uicon-chat-fill":"","uicon-bag-fill":"","uicon-bag":"","uicon-error-circle-fill":"","uicon-error-circle":"","uicon-close-circle":"","uicon-close-circle-fill":"","uicon-checkmark-circle":"","uicon-checkmark-circle-fill":"","uicon-question-circle-fill":"","uicon-question-circle":"","uicon-share":"","uicon-share-fill":"","uicon-shopping-cart":"","uicon-shopping-cart-fill":"","uicon-bell":"","uicon-bell-fill":"","uicon-list":"","uicon-list-dot":"","uicon-zhihu":"","uicon-zhihu-circle-fill":"","uicon-zhifubao":"","uicon-zhifubao-circle-fill":"","uicon-weixin-circle-fill":"","uicon-weixin-fill":"","uicon-twitter-circle-fill":"","uicon-twitter":"","uicon-taobao-circle-fill":"","uicon-taobao":"","uicon-weibo-circle-fill":"","uicon-weibo":"","uicon-qq-fill":"","uicon-qq-circle-fill":"","uicon-moments-circel-fill":"","uicon-moments":"","uicon-qzone":"","uicon-qzone-circle-fill":"","uicon-baidu-circle-fill":"","uicon-baidu":"","uicon-facebook-circle-fill":"","uicon-facebook":"","uicon-car":"","uicon-car-fill":"","uicon-warning-fill":"","uicon-warning":"","uicon-clock-fill":"","uicon-clock":"","uicon-edit-pen":"","uicon-edit-pen-fill":"","uicon-email":"","uicon-email-fill":"","uicon-minus-circle":"","uicon-minus-circle-fill":"","uicon-plus-circle":"","uicon-plus-circle-fill":"","uicon-file-text":"","uicon-file-text-fill":"","uicon-pushpin":"","uicon-pushpin-fill":"","uicon-grid":"","uicon-grid-fill":"","uicon-play-circle":"","uicon-play-circle-fill":"","uicon-pause-circle-fill":"","uicon-pause":"","uicon-pause-circle":"","uicon-eye-off":"","uicon-eye-off-outline":"","uicon-gift-fill":"","uicon-gift":"","uicon-rmb-circle-fill":"","uicon-rmb-circle":"","uicon-kefu-ermai":"","uicon-server-fill":"","uicon-coupon-fill":"","uicon-coupon":"","uicon-integral":"","uicon-integral-fill":"","uicon-home-fill":"","uicon-home":"","uicon-hourglass-half-fill":"","uicon-hourglass":"","uicon-account":"","uicon-plus-people-fill":"","uicon-minus-people-fill":"","uicon-account-fill":"","uicon-thumb-down-fill":"","uicon-thumb-down":"","uicon-thumb-up":"","uicon-thumb-up-fill":"","uicon-lock-fill":"","uicon-lock-open":"","uicon-lock-opened-fill":"","uicon-lock":"","uicon-red-packet-fill":"","uicon-photo-fill":"","uicon-photo":"","uicon-volume-off-fill":"","uicon-volume-off":"","uicon-volume-fill":"","uicon-volume":"","uicon-red-packet":"","uicon-download":"","uicon-arrow-up-fill":"","uicon-arrow-down-fill":"","uicon-play-left-fill":"","uicon-play-right-fill":"","uicon-rewind-left-fill":"","uicon-rewind-right-fill":"","uicon-arrow-downward":"","uicon-arrow-leftward":"","uicon-arrow-rightward":"","uicon-arrow-upward":"","uicon-arrow-down":"","uicon-arrow-right":"","uicon-arrow-left":"","uicon-arrow-up":"","uicon-skip-back-left":"","uicon-skip-forward-right":"","uicon-rewind-right":"","uicon-rewind-left":"","uicon-arrow-right-double":"","uicon-arrow-left-double":"","uicon-wifi-off":"","uicon-wifi":"","uicon-empty-data":"","uicon-empty-history":"","uicon-empty-list":"","uicon-empty-page":"","uicon-empty-order":"","uicon-man":"","uicon-woman":"","uicon-man-add":"","uicon-man-add-fill":"","uicon-man-delete":"","uicon-man-delete-fill":"","uicon-zh":"","uicon-en":""}},a922:function(i,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={pulldown:"",refreshempty:"",back:"",forward:"",more:"","more-filled":"",scan:"",qq:"",weibo:"",weixin:"",pengyouquan:"",loop:"",refresh:"","refresh-filled":"",arrowthindown:"",arrowthinleft:"",arrowthinright:"",arrowthinup:"","undo-filled":"",undo:"",redo:"","redo-filled":"",bars:"",chatboxes:"",camera:"","chatboxes-filled":"","camera-filled":"","cart-filled":"",cart:"","checkbox-filled":"",checkbox:"",arrowleft:"",arrowdown:"",arrowright:"","smallcircle-filled":"",arrowup:"",circle:"","eye-filled":"","eye-slash-filled":"","eye-slash":"",eye:"","flag-filled":"",flag:"","gear-filled":"",reload:"",gear:"","hand-thumbsdown-filled":"","hand-thumbsdown":"","hand-thumbsup-filled":"","heart-filled":"","hand-thumbsup":"",heart:"",home:"",info:"","home-filled":"","info-filled":"","circle-filled":"","chat-filled":"",chat:"","mail-open-filled":"","email-filled":"","mail-open":"",email:"",checkmarkempty:"",list:"","locked-filled":"",locked:"","map-filled":"","map-pin":"","map-pin-ellipse":"",map:"","minus-filled":"","mic-filled":"",minus:"",micoff:"",mic:"",clear:"",smallcircle:"",close:"",closeempty:"",paperclip:"",paperplane:"","paperplane-filled":"","person-filled":"","contact-filled":"",person:"",contact:"","images-filled":"",phone:"",images:"",image:"","image-filled":"","location-filled":"",location:"","plus-filled":"",plus:"",plusempty:"","help-filled":"",help:"","navigate-filled":"",navigate:"","mic-slash-filled":"",search:"",settings:"",sound:"","sound-filled":"","spinner-cycle":"","download-filled":"","personadd-filled":"","videocam-filled":"",personadd:"",upload:"","upload-filled":"",starhalf:"","star-filled":"",star:"",trash:"","phone-filled":"",compose:"",videocam:"","trash-filled":"",download:"","chatbubble-filled":"",chatbubble:"","cloud-download":"","cloud-upload-filled":"","cloud-upload":"","cloud-download-filled":"",headphones:"",shop:""}},aa10:function(i,e,n){"use strict";n.r(e);var o=n("2bf0"),t=n("d5f4");for(var l in t)["default"].indexOf(l)<0&&function(i){n.d(e,i,(function(){return t[i]}))}(l);n("e026");var c=n("828b"),u=Object(c["a"])(t["default"],o["b"],o["c"],!1,null,"59765974",null,!1,o["a"],void 0);e["default"]=u.exports},ad57:function(i,e,n){"use strict";n("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var o={props:{name:{type:String,default:uni.$u.props.icon.name},color:{type:String,default:uni.$u.props.icon.color},size:{type:[String,Number],default:uni.$u.props.icon.size},bold:{type:Boolean,default:uni.$u.props.icon.bold},index:{type:[String,Number],default:uni.$u.props.icon.index},hoverClass:{type:String,default:uni.$u.props.icon.hoverClass},customPrefix:{type:String,default:uni.$u.props.icon.customPrefix},label:{type:[String,Number],default:uni.$u.props.icon.label},labelPos:{type:String,default:uni.$u.props.icon.labelPos},labelSize:{type:[String,Number],default:uni.$u.props.icon.labelSize},labelColor:{type:String,default:uni.$u.props.icon.labelColor},space:{type:[String,Number],default:uni.$u.props.icon.space},imgMode:{type:String,default:uni.$u.props.icon.imgMode},width:{type:[String,Number],default:uni.$u.props.icon.width},height:{type:[String,Number],default:uni.$u.props.icon.height},top:{type:[String,Number],default:uni.$u.props.icon.top},stop:{type:Boolean,default:uni.$u.props.icon.stop}}};e.default=o},cf5b:function(i,e,n){var o=n("f103");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[i.i,o,""]]),o.locals&&(i.exports=o.locals);var t=n("967d").default;t("c24910a6",o,!0,{sourceMap:!1,shadowMode:!1})},d2b4:function(i,e,n){var o=n("ed43");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[i.i,o,""]]),o.locals&&(i.exports=o.locals);var t=n("967d").default;t("f55e6294",o,!0,{sourceMap:!1,shadowMode:!1})},d5f4:function(i,e,n){"use strict";n.r(e);var o=n("88d0"),t=n.n(o);for(var l in o)["default"].indexOf(l)<0&&function(i){n.d(e,i,(function(){return o[i]}))}(l);e["default"]=t.a},df08:function(i,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return t})),n.d(e,"a",(function(){}));var o=function(){var i=this,e=i.$createElement,n=i._self._c||e;return n("v-uni-text",{staticClass:"uni-icons",class:[i.customIcons,i.customIcons?i.type:""],style:{color:i.color,"font-size":i.size+"px"},on:{click:function(e){arguments[0]=e=i.$handleEvent(e),i._onClick.apply(void 0,arguments)}}},[i._v(i._s(i.icons[i.type]))])},t=[]},e026:function(i,e,n){"use strict";var o=n("4194"),t=n.n(o);t.a},ed43:function(i,e,n){var o=n("c86c");e=o(!1),e.push([i.i,".institution[data-v-14fd6023]{width:100%;min-height:100vh;background-color:#f6f6f6;padding:0 %?30?%;box-sizing:border-box}.nav-left[data-v-14fd6023]{display:flex;align-items:center;width:auto;color:#fff;white-space:nowrap}.nav-left uni-image[data-v-14fd6023]{width:%?40?%;height:%?40?%}.section-body[data-v-14fd6023]{padding-top:%?30?%}.section-body .jg-detail .row[data-v-14fd6023]{width:100%;border-bottom:1px solid hsla(0,0%,62%,.32941176470588235);margin-bottom:%?30?%;padding-bottom:%?20?%;position:relative;display:flex;flex-direction:column}.section-body .jg-detail .row .label[data-v-14fd6023]{font-family:Source Han Sans;font-size:16px;color:#333;padding-bottom:%?14?%}.section-body .jg-detail .row .desc[data-v-14fd6023]{font-family:Source Han Sans;font-size:16px;font-weight:350;color:#666}.section-body .jg-detail .row .u-icon[data-v-14fd6023]{position:absolute;right:0;bottom:%?14?%}",""]),i.exports=e},f103:function(i,e,n){var o=n("c86c"),t=n("2ec5"),l=n("60db");e=o(!1);var c=t(l);e.push([i.i,"@font-face{font-family:uniicons;src:url("+c+') format("truetype")}.uni-icons[data-v-5c0264f4]{font-family:uniicons;text-decoration:none;text-align:center}',""]),i.exports=e},f10a:function(i,e,n){"use strict";n("6a54");var o=n("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("64aa");var t=o(n("a922")),l={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16},customIcons:{type:String,default:""}},data:function(){return{icons:t.default}},methods:{_onClick:function(){this.$emit("click")}}};e.default=l}}]);