(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-eHealthRecord-warning"],{"01dd":function(e,t,a){"use strict";a.r(t);var i=a("b662"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},"0654":function(e,t,a){"use strict";var i=a("d029"),n=a.n(i);n.a},2026:function(e,t,a){"use strict";a.r(t);var i=a("7cec"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},"5fa3":function(e,t,a){"use strict";var i=a("8931"),n=a.n(i);n.a},"7cec":function(e,t,a){"use strict";(function(e){a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("8f71"),a("bf0f"),a("fd3c"),a("2797"),a("e838"),a("aa9c"),a("c223");var n=i(a("2634")),r=i(a("9b1b")),c=i(a("2fdc")),s=i(a("e81c")),o=i(a("570a")),l=i(a("cc2e")),d={data:function(){return{tabs:["体检提醒","异常提醒"],currentIndex:0,reportList:[],warningList:[],appointList:[],examTypeOption:[{value:"",label:"全部"},{value:"1",label:"岗前"},{value:"2",label:"在岗"},{value:"0",label:"离岗"}],examTypeLabel:"全部",examTypeIndex:0}},created:function(){this.examTypeLabel=this.examTypeOption[this.examTypeIndex].label,this.getAppointList()},watch:{currentIndex:function(e){0===e?this.getAppointList():1===e&&this.getReportList()}},methods:{getAppointList:function(){var e=this;return(0,c.default)((0,n.default)().mark((function t(){var a,i,c,s;return(0,n.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,o.default.getHcAppointmentList();case 2:a=t.sent,200===a.status&&(i=(0,l.default)(),c=a.data.filter((function(e){return 0==e.reservationStatu||3==e.reservationStatu||2==e.reservationStatu&&!e.registerStatus})),s=e.examTypeOption[e.examTypeIndex].value,""!==s&&(c=c.filter((function(e){return String(e.examType)===s}))),e.appointList=c.map((function(e){var t="";return t=e.reservationDate?0===e.reservationStatu?"请预约体检日期":3===e.reservationStatu?"体检预约已被拒绝，请重新预约":(0,l.default)(e.reservationDate).isBefore(i,"day")?"预约日期已过，请重新预约":(0,l.default)(e.reservationDate).isSame(i,"day")?"体检日期已到期，请及时前往":i.isBefore((0,l.default)(e.reservationDate).add(7,"days"),"day")?"体检日期临近，请记得准时前往":"体检已预约，请记得准时前往":"请预约体检日期",(0,r.default)((0,r.default)({},e),{},{desc:t,reservationDate:e.reservationDate&&(0,l.default)(e.reservationDate).format("YYYY-MM-DD")})})));case 4:case"end":return t.stop()}}),t)})))()},examTypeChange:function(e){this.examTypeIndex=e.detail.value,this.examTypeLabel=this.examTypeOption[this.examTypeIndex].label,this.getAppointList()},getReportList:function(){var t=this;return(0,c.default)((0,n.default)().mark((function a(){var i;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,s.default.reportList();case 3:if(i=a.sent,i.data&&0!==i.data.length){a.next=7;break}return uni.showToast({title:"暂无体检记录",icon:"none",duration:2e3}),a.abrupt("return");case 7:t.reportList=i.data,t.reportList.forEach((function(e){t.processWarningItems(e)})),a.next=15;break;case 11:a.prev=11,a.t0=a["catch"](0),e.error("获取体检记录失败:",a.t0),uni.showToast({title:"获取体检记录失败",icon:"none",duration:2e3});case 15:case"end":return a.stop()}}),a,null,[[0,11]])})))()},processWarningItems:function(e){var t,a=[];this.warningList=[];var i=new Date(e.registerTime).toLocaleDateString();e.checkDepartments.forEach((function(e){e.checkProjects.forEach((function(e){e.checkItems.forEach((function(e){var t=parseFloat(e.result),n=parseFloat(e.itemId.standardValueMin),r=parseFloat(e.itemId.standardValueMax);(t<n||t>r)&&a.push({projectName:e.itemId.projectName,result:e.result,unit:e.itemId.msrunt,referenceRange:"".concat(n," - ").concat(r),warningType:e.conclusion||"".concat(e.itemId.projectName).concat(t<n?"偏低":"偏高"),checkTime:i})}))}))})),(t=this.warningList).push.apply(t,a)},navChange:function(e){this.currentIndex=e}}};t.default=d}).call(this,a("ba7c")["default"])},"7f81":function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){}));var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("v-uni-scroll-view",{class:["grace-nav-bar",e.isCenter?"grace-nav-center":""],attrs:{"scroll-with-animation":e.scorllAnimation,"scroll-x":!0,"show-scrollbar":!1,"scroll-into-view":"tab-"+e.currentIndex+e.autoLeft}},e._l(e.items,(function(t,i){return a("v-uni-view",{key:i,staticClass:"nav-item",style:{width:e.size<1?"auto":e.size+"rpx",marginRight:e.margin+"rpx",padding:"0rpx "+e.padding},attrs:{id:"tab-"+i,"data-index":i},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.navchang.apply(void 0,arguments)}}},[a("v-uni-view",{class:["nav-item-title",e.currentIndex==i?"nav-active":""],style:{color:e.currentIndex==i?e.activeColor:e.color,textAlign:e.textAlign,lineHeight:e.lineHeight,fontSize:e.currentIndex==i?e.activeFontSize:e.fontSize,fontWeight:e.currentIndex==i?e.activeFontWeight:""}},[e._v(e._s(t))]),a("v-uni-view",{staticClass:"nav-active-line-wrap",style:{justifyContent:e.activeDirection}},[e.currentIndex==i?a("v-uni-view",{staticClass:"nav-active-line",class:[e.currentIndex==i&&e.animatie?"grace-nav-scale":""],style:{background:e.activeLineBg,width:e.activeLineWidth,height:e.activeLineHeight,borderRadius:e.activeLineRadius}}):e._e()],1)],1)})),1)},n=[]},8931:function(e,t,a){var i=a("b751");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("07c2a75e",i,!0,{sourceMap:!1,shadowMode:!1})},a979:function(e,t,a){"use strict";a.r(t);var i=a("efbf"),n=a("2026");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("5fa3");var c=a("828b"),s=Object(c["a"])(n["default"],i["b"],i["c"],!1,null,"bee94208",null,!1,i["a"],void 0);t["default"]=s.exports},b662:function(e,t,a){"use strict";a("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("64aa");var i={props:{isCenter:{type:Boolean,default:!1},currentIndex:{type:Number,default:0},size:{type:Number,default:120},fontSize:{type:String,default:"28rpx"},activeFontSize:{type:String,default:"28rpx"},items:{type:Array,default:function(){return[]}},activeLineBg:{type:String,default:"linear-gradient(to right, #66BFFF,#3388FF)"},color:{type:String,default:"#333333"},activeColor:{type:String,default:"#333333"},activeLineHeight:{type:String,default:"6rpx"},activeLineWidth:{type:String,default:"36rpx"},activeLineRadius:{type:String,default:"0rpx"},activeDirection:{type:String,default:""},activeFontWeight:{type:Number,default:700},margin:{type:Number,default:0},textAlign:{type:String,default:""},lineHeight:{type:String,default:"50rpx"},padding:{type:String,default:"0rpx"},animatie:{type:Boolean,default:!0},autoLeft:{type:String,default:""},scorllAnimation:{type:Boolean,default:!1}},methods:{navchang:function(e){this.$emit("change",Number(e.currentTarget.dataset.index))}}};t.default=i},b751:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'.health-check-item[data-v-bee94208]{background-color:#fff;border-radius:%?12?%;padding:%?30?%;margin-bottom:%?20?%;box-shadow:0 2px 6px rgba(0,0,0,.04)}.health-check-item[data-v-bee94208]:first-child{margin-top:%?20?%}.health-check-item .item-header[data-v-bee94208]{display:flex;justify-content:space-between;align-items:center;padding-bottom:%?20?%;border-bottom:1px solid #f0f0f0}.health-check-item .item-header .date[data-v-bee94208]{font-size:14px;color:#666}.health-check-item .item-header .org-name[data-v-bee94208]{display:flex;align-items:center;font-size:16px;color:#333;font-weight:500}.health-check-item .item-header .org-name uni-text[data-v-bee94208]{margin-left:%?10?%}.health-check-item .item-content[data-v-bee94208]{padding:%?20?% 0;border-bottom:1px solid #f0f0f0}.health-check-item .check-info[data-v-bee94208]{margin-top:%?20?%}.health-check-item .check-info .info-item[data-v-bee94208]{margin-bottom:%?16?%;font-size:14px;color:#666}.health-check-item .check-info .info-item .label[data-v-bee94208]{color:#999}.health-check-item .check-info .info-item .value-group[data-v-bee94208]{display:flex;align-items:center;gap:%?4?%}.health-check-item .check-info .info-item .value-group .value.warning[data-v-bee94208]{color:#f5222d;font-weight:500}.health-check-item .check-info .info-item .value-group .unit[data-v-bee94208]{color:#999;font-size:%?24?%}.container[data-v-bee94208]{display:flex;flex-direction:column;height:100%;background-color:#f5f5f5}.empty-state[data-v-bee94208]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:%?60?% 0}.empty-state .empty-image[data-v-bee94208]{width:%?200?%;height:%?200?%;margin-bottom:%?30?%}.empty-state .empty-text[data-v-bee94208]{font-size:%?32?%;color:#333;margin-bottom:%?16?%}.empty-state .empty-subtext[data-v-bee94208]{font-size:%?28?%;color:#999}.empty[data-v-bee94208]{text-align:center;color:#999;font-size:14px;margin-top:20px}.card-section .card[data-v-bee94208]{width:100%;border-radius:5px;background:#fff;margin-top:12px;padding:14px;box-sizing:border-box;box-shadow:2px 2px 10px 2px rgba(0,0,0,.1)}.card-section .card .title[data-v-bee94208]{font-family:PingFangSC;font-size:14px;color:#555;display:flex;align-items:center;margin-left:-14px;margin-bottom:15px}.card-section .card .title uni-text[data-v-bee94208]{display:inline-block;width:6px;height:20px;border-radius:3px 3px 0 3px;background:#fe3e3e;margin-right:12px}.card-section .card .name[data-v-bee94208]{font-size:14px;margin-bottom:6px}.card-section .card .name .label[data-v-bee94208]{margin-right:24px;color:#000}.card-section .card .name .des[data-v-bee94208]{color:#555}.card-section .card .operaction[data-v-bee94208]{display:flex;align-items:center;justify-content:flex-end;margin-top:24px}.card-section .card .operaction uni-view[data-v-bee94208]{text-align:center;line-height:33px;width:81px;height:33px;border-radius:3px}.card-section .card .operaction .cancel-btn[data-v-bee94208]{color:#3e73fe;border:1px solid #3e73fe;text-align:center;line-height:33px}.card-section .card .operaction .edit-btn[data-v-bee94208]{background:#3e73fe;border:1px solid #3e73fe;color:#fff;margin-left:12px}.infoCard[data-v-bee94208]{padding:15px;border-radius:10px;margin-top:12.5px;position:relative}.infoCard .cardTitle[data-v-bee94208]{margin-bottom:16px}.infoCard .cardTitle .titleText[data-v-bee94208]{font-weight:600;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555;display:flex;justify-content:space-between}.infoCard .cardTitle .titlePoint[data-v-bee94208]{position:absolute;left:0;top:13px;width:6px;height:20px;border-radius:3px 3px 0 3px;opacity:1;background:#3e73fe}.infoCard .cardItem[data-v-bee94208]{display:flex;margin-bottom:12px;justify-content:space-between;font-size:%?24?%;font-weight:400;line-height:%?28?%;letter-spacing:0;color:#555}.infoCard .cardItem[data-v-bee94208]:last-child{margin-bottom:0}uni-picker .uni-input[data-v-bee94208]{color:#a8abb2}uni-picker .uni-input[data-v-bee94208]::after{content:">";display:inline-block;-webkit-transform:rotate(90deg) scaleY(1.5) translateY(-.25em);transform:rotate(90deg) scaleY(1.5) translateY(-.25em);margin-right:1em}',""]),e.exports=t},d029:function(e,t,a){var i=a("d999");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("092ecad1",i,!0,{sourceMap:!1,shadowMode:!1})},d999:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,"\n.grace-nav-bar[data-v-2903520c]{width:100%;display:flex;white-space:nowrap}\n.nav-item[data-v-2903520c]{width:%?100?%;display:inline-flex;flex-direction:column}.nav-item-title[data-v-2903520c]{width:100%;color:#333}.nav-active-line-wrap[data-v-2903520c]{display:flex}.nav-active-line[data-v-2903520c]{margin-top:%?5?%}.grace-nav-center[data-v-2903520c]{justify-content:center;text-align:center}@-webkit-keyframes grace-nav-scale-data-v-2903520c{0%{-webkit-transform:scale(.1);transform:scale(.1)}100%{-webkit-transform:scale(1);transform:scale(1)}}@keyframes grace-nav-scale-data-v-2903520c{0%{-webkit-transform:scale(.1);transform:scale(.1)}100%{-webkit-transform:scale(1);transform:scale(1)}}.grace-nav-scale[data-v-2903520c]{-webkit-animation:grace-nav-scale-data-v-2903520c .3s forwards;animation:grace-nav-scale-data-v-2903520c .3s forwards}",""]),e.exports=t},e81c:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("0518")),r={getCheckHealthList:function(e){return(0,n.default)({url:"manage/btHealthCheck/checkOrgList",method:"post",data:e})},getOneDetail:function(e){return(0,n.default)({url:"manage/btHealthCheck/getOneDetail",method:"get",data:{id:e.id}})},getappointmentRecord:function(e){return(0,n.default)({url:"manage/btHealthCheck/appointmentRecord",method:"get",data:e})},getappointment:function(e){return(0,n.default)({url:"manage/btHealthCheck/appointment",method:"post",data:e})},cancelAppointment:function(e){return(0,n.default)({url:"manage/btHealthCheck/cancelAppointment",method:"post",data:e})},updateAppointment:function(e){return(0,n.default)({url:"manage/btHealthCheck/updateAppointment",method:"post",data:e})},checkReport:function(e){return(0,n.default)({url:"manage/btHealthCheck/checkReport",method:"get",data:e})},reportList:function(e){return(0,n.default)({url:"manage/btHealthCheck/reportList",method:"get",data:e})},cancelReservation:function(e){return(0,n.default)({url:"manage/btHealthCheck/cancelReservation",method:"get",data:e})},getHCReportAuthList:function(e){return(0,n.default)({url:"manage/btHealthCheck/getHCReportAuthList",method:"get",data:e})},updateHCReportAuthStatus:function(e){return(0,n.default)({url:"manage/btHealthCheck/updateHCReportAuthStatus",method:"post",data:e})}};t.default=r},efbf:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i}));var i={gracePage:a("1367").default,graceNavBar:a("f664").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[a("my-header",{attrs:{slot:"gHeader",title:"异常提醒"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body container",attrs:{slot:"gBody"},slot:"gBody"},[a("graceNavBar",{attrs:{items:e.tabs,currentIndex:e.currentIndex,textAlign:"center",isCenter:!0,size:160,lineHeight:"70rpx",activeColor:"#3688FF",padding:"30rpx",activeLineWidth:"100%"},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.navChange.apply(void 0,arguments)}}}),0===e.currentIndex?a("v-uni-view",{staticClass:"infoCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[e._v("筛选条件")])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticStyle:{"margin-right":"1em"}},[e._v("检查类型")]),a("v-uni-picker",{attrs:{mode:"selector","range-key":"label",value:e.examTypeIndex,range:e.examTypeOption},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.examTypeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[e._v(e._s(e.examTypeLabel))])],1)],1)],1):e._e(),0===e.currentIndex?a("v-uni-view",{staticClass:"card-section"},[0===e.appointList.length?a("v-uni-view",[a("v-uni-view",{staticClass:"empty"},[e._v("暂无数据")])],1):e._e(),e._l(e.appointList,(function(t){return a("v-uni-view",{staticClass:"card"},[a("v-uni-view",{staticClass:"title"},[a("v-uni-text"),e._v(e._s(t.desc))],1),a("v-uni-view",{staticClass:"name"},[a("v-uni-text",{staticClass:"label"},[e._v("体检机构：")]),a("v-uni-text",{staticClass:"des"},[e._v(e._s(t.physicalExamOrgName))])],1),a("v-uni-view",{staticClass:"name"},[a("v-uni-text",{staticClass:"label"},[e._v("预约状态：")]),0===t.reservationStatu?a("v-uni-text",{staticClass:"des"},[e._v("待预约")]):e._e(),1===t.reservationStatu?a("v-uni-text",{staticClass:"des"},[e._v("待审核")]):e._e(),2===t.reservationStatu?a("v-uni-text",{staticClass:"des"},[e._v("已通过")]):e._e(),3===t.reservationStatu?a("v-uni-text",{staticClass:"des"},[e._v("已拒绝")]):e._e()],1),a("v-uni-view",{staticClass:"name"},[a("v-uni-text",{staticClass:"label"},[e._v("体检时间：")]),a("v-uni-text",{staticClass:"des"},[e._v(e._s(t.reservationDate||"尚未预约"))])],1),a("v-uni-view",{staticClass:"name"},[a("v-uni-text",{staticClass:"label"},[e._v("体检类型：")]),0===t.examType?a("v-uni-text",{staticClass:"des"},[e._v("离岗")]):e._e(),1===t.examType?a("v-uni-text",{staticClass:"des"},[e._v("岗前")]):e._e(),2===t.examType?a("v-uni-text",{staticClass:"des"},[e._v("在岗")]):e._e()],1)],1)}))],2):e._e(),1===e.currentIndex?a("v-uni-view",[e._l(e.warningList,(function(t,i){return a("v-uni-view",{key:i,staticClass:"health-check-item"},[a("v-uni-view",{staticClass:"item-header"},[a("v-uni-view",{staticClass:"org-name"},[e._v(e._s(t.projectName))]),a("v-uni-view",{staticClass:"date"},[e._v(e._s(t.checkTime))])],1),a("v-uni-view",{staticClass:"item-content"},[a("v-uni-view",{staticClass:"check-info"},[a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[e._v("检查结果：")]),a("v-uni-text",{staticClass:"value"},[e._v(e._s(t.result))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[e._v("单位：")]),a("v-uni-text",{staticClass:"value"},[e._v(e._s(t.unit))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[e._v("参考范围：")]),a("v-uni-text",{staticClass:"value"},[e._v(e._s(t.referenceRange))])],1),a("v-uni-view",{staticClass:"info-item"},[a("v-uni-text",{staticClass:"label"},[e._v("异常描述：")]),a("v-uni-text",{staticClass:"value warning"},[e._v(e._s(t.warningType))])],1)],1)],1)],1)})),0===e.warningList.length?a("v-uni-view",{staticClass:"empty-state"},[a("v-uni-image",{staticClass:"empty-image",attrs:{src:"/static/images/empty.png",mode:"aspectFit"}}),a("v-uni-text",{staticClass:"empty-text"},[e._v("暂无异常数据")]),a("v-uni-text",{staticClass:"empty-subtext"},[e._v("您的体检指标都在正常范围内")])],1):e._e()],2):e._e()],1)],1)},r=[]},f664:function(e,t,a){"use strict";a.r(t);var i=a("7f81"),n=a("01dd");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("0654");var c=a("828b"),s=Object(c["a"])(n["default"],i["b"],i["c"],!1,null,"2903520c",null,!1,i["a"],void 0);t["default"]=s.exports}}]);