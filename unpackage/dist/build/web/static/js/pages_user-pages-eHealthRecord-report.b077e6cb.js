(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-eHealthRecord-report"],{"0447":function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,'.page-body[data-v-68c2f39e]{width:100%;padding:0 15px 15px 15px;box-sizing:border-box;background-color:#f6f6f6}.chartContainer[data-v-68c2f39e]{width:100%;height:100%}.infoCard[data-v-68c2f39e]{padding:15px;border-radius:10px;margin-top:12.5px;position:relative}.infoCard .cardTitle[data-v-68c2f39e]{margin-bottom:16px}.infoCard .cardTitle .titleText[data-v-68c2f39e]{font-weight:600;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555;display:flex;justify-content:space-between}.infoCard .cardTitle .titlePoint[data-v-68c2f39e]{position:absolute;left:0;top:13px;width:6px;height:20px;border-radius:3px 3px 0 3px;opacity:1;background:#3e73fe}.infoCard .cardItem[data-v-68c2f39e]{display:flex;margin-bottom:12px;justify-content:space-between;font-size:%?24?%;font-weight:400;line-height:%?28?%;letter-spacing:0;color:#555}.infoCard .cardItem[data-v-68c2f39e]:last-child{margin-bottom:0}.pieContainer[data-v-68c2f39e]{display:flex;flex-direction:column;align-items:center;height:32vh;width:100%}.container[data-v-68c2f39e]{display:flex;flex-direction:column;align-items:center;height:48vh;width:100%;position:relative}.no-data-tip[data-v-68c2f39e]{position:absolute;top:50%;left:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);color:#999;font-size:%?28?%}uni-picker .uni-input[data-v-68c2f39e]{color:#a8abb2}uni-picker .uni-input[data-v-68c2f39e]::after{content:">";display:inline-block;-webkit-transform:rotate(90deg) scaleY(1.5) translateY(-.25em);transform:rotate(90deg) scaleY(1.5) translateY(-.25em);margin-right:1em}',""]),e.exports=t},"10f5":function(e,t,a){"use strict";(function(e){a("6a54");var i=a("3639").default,n=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("c9b5"),a("bf0f"),a("ab80"),a("c1a3"),a("18f7"),a("de6c"),a("4100"),a("2797"),a("aa9c"),a("e838"),a("08eb"),a("fd3c"),a("0829"),a("aa77"),a("c223");var r=n(a("5de6")),o=n(a("2634")),s=n(a("2fdc")),l=n(a("9b1b")),c=n(a("e81c")),u=n(a("3d1c")),d=i(a("b73f")),p=n(a("cc2e")),h={dataset:{source:[]},tooltip:{trigger:"axis"},xAxis:[{type:"category",axisLine:{lineStyle:{color:"#999"}},boundaryGap:!1}],yAxis:[{type:"value",splitLine:{lineStyle:{type:"dashed",color:"#DCDCDC"}},axisLine:{show:!1,lineStyle:{color:"#A4A4A4"}},axisTick:{show:!1},nameTextStyle:{color:"#A4A4A4"},splitArea:{show:!1}}],series:[{name:"指标",type:"line",encode:{x:"year",y:"result"},lineStyle:{normal:{width:8,color:{type:"linear",colorStops:[{offset:0,color:"#A9F387"},{offset:1,color:"#48D8BF"}],globalCoord:!1},shadowColor:"rgba(72,216,191, 0.3)",shadowBlur:10,shadowOffsetY:20}},itemStyle:{normal:{color:"#fff",borderColor:"#A9F387",borderWidth:10}},label:{normal:{show:!0,position:"top",color:"#A9F387"}},smooth:!0}]},f={tooltip:{trigger:"item",formatter:"{b}: {c} ({d}%)"},legend:{bottom:"5%",left:"center",itemWidth:12,itemHeight:12,textStyle:{fontSize:12}},series:[{name:"检查结论统计",type:"pie",radius:["16%","54%"],center:["50%","45%"],avoidLabelOverlap:!0,itemStyle:{borderRadius:8,borderColor:"#fff",borderWidth:2},label:{show:!0,position:"outside",formatter:"{b}: {c}",fontSize:11,color:"#333",distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:10,smooth:!0},emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},data:[],color:["#3E73FE","#008AFF","#48D8BF","#A9F387","#FF9F40"]}]},m=(0,l.default)((0,l.default)({},f),{},{series:[(0,l.default)((0,l.default)({},f.series[0]),{},{name:"暂无数据",data:[]})]}),v=(0,l.default)((0,l.default)({},h),{},{series:[{name:"暂无数据",type:"line",data:[],label:{show:!1}}],xAxis:[{type:"category",data:[],axisLine:{lineStyle:{color:"#999"}},boundaryGap:!1}],yAxis:[{type:"value",splitLine:{lineStyle:{type:"dashed",color:"#DCDCDC"}},axisLine:{show:!1,lineStyle:{color:"#A4A4A4"}},axisTick:{show:!1},nameTextStyle:{color:"#A4A4A4"},splitArea:{show:!1}}]}),g={data:function(){return{userInfo:{gender:"0",name:"",age:"",department:"",workType:"",workYears:""},ec:{option:{}},pieEc:{option:{}},pickerArray:[],pickerLabel:"",pickerIndex:0,chartData:[],chartInstance:null,checkItems:[],historyData:[],isEmpty:!1,pieIsEmpty:!1,examTypeOption:[{value:"",label:"全部"},{value:"1",label:"岗前"},{value:"2",label:"在岗"},{value:"3",label:"离岗"}],examTypeLabel:"全部",examTypeIndex:0,examTypeLabelForPie:"全部",examTypeIndexForPie:0}},mounted:function(){var t=this;return(0,s.default)((0,o.default)().mark((function a(){var i,n,r;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.getBasicInfo();case 3:return a.next=5,c.default.reportList({examType:t.examTypeOption[t.examTypeIndex].value});case 5:return i=a.sent,a.next=8,c.default.reportList({examType:t.examTypeOption[t.examTypeIndexForPie].value});case 8:n=a.sent,i.data&&0!==i.data.length||(t.isEmpty=!0),t.processCheckItems(i.data),r=t.processPie(n.data),t.pieIsEmpty=!r||0===r.length,t.initChartH5(),t.initPieChartH5(r),t.getData(),a.next=23;break;case 18:a.prev=18,a.t0=a["catch"](0),e.error("数据获取失败:",a.t0),t.isEmpty=!0,t.pieIsEmpty=!0;case 23:case"end":return a.stop()}}),a,null,[[0,18]])})))()},methods:{getBasicInfo:function(){var t=this;return(0,s.default)((0,o.default)().mark((function a(){var i;return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,u.default.getEHealthRecordBaseInfo();case 3:i=a.sent,i.data&&(t.userInfo=(0,l.default)((0,l.default)({},t.userInfo),{},{name:i.data.name||"",gender:i.data.gender||"0",age:i.data.age||"",department:i.data.department||"",workType:i.data.workType||"",workYears:i.data.workYears||""})),a.next=10;break;case 7:a.prev=7,a.t0=a["catch"](0),e.error("获取基本信息失败:",a.t0);case 10:case"end":return a.stop()}}),a,null,[[0,7]])})))()},calculateAge:function(e){var t=new Date(e),a=new Date,i=a.getFullYear()-t.getFullYear(),n=a.getMonth()-t.getMonth();return(n<0||0===n&&a.getDate()<t.getDate())&&i--,i.toString()},processCheckItems:function(e){var t,a=new Map;e.sort((function(e,t){return new Date(e.registerTime)-new Date(t.registerTime)})),e.forEach((function(e){var t=(0,p.default)(e.registerTime).format("YYYY-MM-DD");e.checkDepartments.forEach((function(e){e.checkProjects.forEach((function(e){e.checkItems.forEach((function(e){var i=e.itemId;if(i){var n=i._id;a.has(n)||a.set(n,{label:i.projectName,value:n,unit:i.msrunt||"",history:[]}),a.get(n).history.push({year:t,result:parseFloat(e.result)})}}))}))}))})),this.historyData=Array.from(a.values()),this.checkItems=this.historyData.map((function(e){var t;return{label:e.label,value:e.value,unit:e.unit,result:(null===(t=e.history[e.history.length-1])||void 0===t?void 0:t.result)||""}})),this.pickerArray=this.checkItems.map((function(e){return{value:e.value,label:e.label}})),this.pickerLabel=(null===(t=this.pickerArray[0])||void 0===t?void 0:t.label)||""},processPie:function(e){var t={1:"目前未见异常",2:"复查",3:"疑似职业病",4:"禁忌证",5:"其他疾病或异常"},a={};e.forEach((function(e){e.jobConclusion.forEach((function(e){a[e]=(a[e]||0)+1}))}));var i=Object.entries(a).map((function(e){var a=(0,r.default)(e,2),i=a[0],n=a[1];return{name:t[i]||i,value:n}}));return i},examTypeChangeForPie:function(e){var t=this;return(0,s.default)((0,o.default)().mark((function a(){return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.examTypeIndexForPie=e.detail.value,t.examTypeLabelForPie=t.examTypeOption[t.examTypeIndexForPie].label,a.next=4,t.updateDataForPie();case 4:case"end":return a.stop()}}),a)})))()},updateDataForPie:function(){var e=this;return(0,s.default)((0,o.default)().mark((function t(){var a,i;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,c.default.reportList({examType:e.examTypeOption[e.examTypeIndexForPie].value});case 2:a=t.sent,i=[],a.data&&a.data.length>0?(i=e.processPie(a.data),e.pieIsEmpty=!1):e.pieIsEmpty=!0,e.initPieChartH5(i);case 6:case"end":return t.stop()}}),t)})))()},updateData:function(){var e=this;return(0,s.default)((0,o.default)().mark((function t(){var a;return(0,o.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.clearData(),t.next=3,c.default.reportList({examType:e.examTypeOption[e.examTypeIndex].value});case 3:a=t.sent,a.data&&0!==a.data.length?e.isEmpty=!1:e.isEmpty=!0,e.processCheckItems(a.data);case 6:case"end":return t.stop()}}),t)})))()},getData:function(){if(!this.checkItems.length)return this.isEmpty=!0,h.dataset.source=[],h.series[0].name="暂无数据",void this.chartInstance.setOption(h);var e=this.checkItems[this.pickerIndex],t=this.historyData.find((function(t){return t.value===e.value}));t&&t.history.length>0?(this.isEmpty=!1,t.history.sort((function(e,t){return e.year-t.year})),h.dataset.source=t.history,h.series[0].name="".concat(e.label,"(").concat(e.unit,")"),this.chartInstance.setOption(h)):(this.isEmpty=!0,this.chartInstance.setOption(v))},bindPickerChange:function(e){this.pickerIndex=e.detail.value,this.pickerLabel=this.pickerArray[this.pickerIndex].label,h.series[0].name=this.pickerLabel,this.getData()},examTypeChange:function(e){var t=this;return(0,s.default)((0,o.default)().mark((function a(){return(0,o.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.examTypeIndex=e.detail.value,t.examTypeLabel=t.examTypeOption[t.examTypeIndex].label,a.next=4,t.updateData();case 4:t.getData();case 5:case"end":return a.stop()}}),a)})))()},clearData:function(){this.checkItems=[],this.pickerArray=[],this.pickerLabel="",this.historyData=[],this.isEmpty=!0},initChart:function(e,t,a,i){var n=d.init(e,null,{width:t,height:a,devicePixelRatio:i});return e.setChart(n),n.setOption(this.isEmpty?v:h),n},initPieChart:function(e,t,a,i){var n=d.init(e,null,{width:t,height:a,devicePixelRatio:i});e.setChart(n);var r=this.processPie([]);return n.setOption(r.length>0?(0,l.default)((0,l.default)({},f),{},{series:[(0,l.default)((0,l.default)({},f.series[0]),{},{data:r})]}):m),n},initChartH5:function(){var e=document.getElementById("chartContainer");this.chartInstance=d.init(e),this.chartInstance.setOption(this.isEmpty?v:h)},initPieChartH5:function(e){var t=document.getElementById("pieContainer");this.pieChartInstance||(this.pieChartInstance=d.init(t));var a=e&&e.length>0?(0,l.default)((0,l.default)({},f),{},{series:[(0,l.default)((0,l.default)({},f.series[0]),{},{data:e})]}):m;this.pieChartInstance.setOption(a)}}};t.default=g}).call(this,a("ba7c")["default"])},"38b3":function(e,t,a){"use strict";var i=a("6c10"),n=a.n(i);n.a},"3d1c":function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("0518")),r={getEHealthRecordAuthList:function(e){return(0,n.default)({url:"manage/eHealthRecord/getEHealthRecordAuthList",method:"get",data:e})},handleEHealthRecordAuth:function(e){return(0,n.default)({url:"manage/eHealthRecord/handleEHealthRecordAuth",method:"post",data:e})},getEHealthRecordBaseInfo:function(e){return(0,n.default)({url:"manage/eHealthRecord/getEHealthRecordBaseInfo",method:"get",data:e})},updateEHealthRecordBaseInfo:function(e){return(0,n.default)({url:"manage/eHealthRecord/updateEHealthRecordBaseInfo",method:"post",data:e})},getSupervisionList:function(e){return(0,n.default)({url:"manage/eHealthRecord/getSupervisionList",method:"post",data:e})},postComplaint:function(e){return(0,n.default)({url:"manage/eHealthRecord/postComplaint",method:"post",data:e})},getLaborIsEdit:function(e){return(0,n.default)({url:"manage/eHealthRecord/getLaborIsEdit",method:"get",data:e})},addEmploymentHistory:function(e){return(0,n.default)({url:"manage/eHealthRecord/addEmploymentHistory",method:"post",data:e})},editEmploymentHistory:function(e){return(0,n.default)({url:"manage/eHealthRecord/editEmploymentHistory",method:"post",data:e})},deleteEmploymentHistory:function(e){return(0,n.default)({url:"manage/eHealthRecord/deleteEmploymentHistory",method:"post",data:e})},getEmploymentHistory:function(e){return(0,n.default)({url:"manage/eHealthRecord/getEmploymentHistory",method:"get",data:e})},findDistricts:function(e){return(0,n.default)({url:"app/user/findDistricts",method:"get",data:e})},getProvinces:function(){return this.findDistricts({level:0})},getCitiesByProvince:function(e){return this.findDistricts({level:1,parent_code:e})},getDistrictsByCity:function(e){return this.findDistricts({level:2,parent_code:e})},getTownsByDistrict:function(e){return this.findDistricts({level:3,parent_code:e})},getDiaList:function(e){return(0,n.default)({url:"manage/eHealthRecord/getDiaList",method:"post",data:e})},getIdentificationList:function(e){return(0,n.default)({url:"manage/eHealthRecord/getIdentificationList",method:"post",data:e})},findHarmFactors:function(e){return(0,n.default)({url:"manage/eHealthRecord/findHarmFactors",method:"get",data:e})}};t.default=r},5730:function(e,t,a){"use strict";a.r(t);var i=a("e79d"),n=a("d888");for(var r in n)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(r);a("38b3");var o=a("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,"68c2f39e",null,!1,i["a"],void 0);t["default"]=s.exports},"6c10":function(e,t,a){var i=a("0447");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("047626a4",i,!0,{sourceMap:!1,shadowMode:!1})},d888:function(e,t,a){"use strict";a.r(t);var i=a("10f5"),n=a.n(i);for(var r in i)["default"].indexOf(r)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(r);t["default"]=n.a},e79d:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return i}));var i={gracePage:a("1367").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[a("my-header",{attrs:{slot:"gHeader",title:"职业健康检查统计分析"},slot:"gHeader"}),a("v-uni-view",{staticClass:"page-body",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"infoCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[e._v("基本信息")])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",[a("v-uni-text",[e._v("姓名："+e._s(e.userInfo.name||"暂无"))])],1),a("v-uni-view",[a("v-uni-text",[e._v("年龄："+e._s(e.userInfo.age||"暂无"))])],1),a("v-uni-view",[a("v-uni-text",[e._v("性别："+e._s("0"===e.userInfo.gender?"男":"1"===e.userInfo.gender?"女":"未知"))])],1)],1)],1),a("v-uni-view",{staticClass:"infoCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-view",[e._v("历次体检结果统计")]),a("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between"}})],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticStyle:{"margin-right":"1em"}},[e._v("检查类型")]),a("v-uni-picker",{attrs:{mode:"selector","range-key":"label",value:e.examTypeIndexForPie,range:e.examTypeOption},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.examTypeChangeForPie.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[e._v(e._s(e.examTypeLabelForPie))])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticClass:"pieContainer",staticStyle:{height:"32vh"}},[a("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!e.pieIsEmpty,expression:"!pieIsEmpty"}],staticStyle:{width:"100%",height:"100%"}},[a("v-uni-view",{ref:"pieContainer",staticClass:"chartContainer",attrs:{id:"pieContainer"}})],1),e.pieIsEmpty?a("v-uni-view",{staticClass:"no-data-tip"},[a("v-uni-text",[e._v("暂无相关检查数据")])],1):e._e()],1)],1)],1),a("v-uni-view",{staticClass:"infoCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[e._v("身体健康状况变化趋势")])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticStyle:{"margin-right":"1em"}},[e._v("检查类型")]),a("v-uni-picker",{attrs:{mode:"selector","range-key":"label",value:e.examTypeIndex,range:e.examTypeOption},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.examTypeChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[e._v(e._s(e.examTypeLabel))])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticStyle:{"margin-right":"1em"}},[e._v("检查指标")]),a("v-uni-picker",{attrs:{mode:"selector","range-key":"label",value:e.pickerIndex,range:e.pickerArray},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.bindPickerChange.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"uni-input"},[e._v(e._s(e.pickerLabel||"暂无数据"))])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticClass:"container"},[a("v-uni-view",{staticStyle:{width:"100%",height:"100%"}},[a("v-uni-view",{ref:"chartContainer",staticClass:"chartContainer",attrs:{id:"chartContainer"}})],1),e.isEmpty?a("v-uni-view",{staticClass:"no-data-tip"},[a("v-uni-text",[e._v("暂无相关检查数据")])],1):e._e()],1)],1)],1)],1)],1)},r=[]},e81c:function(e,t,a){"use strict";a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("0518")),r={getCheckHealthList:function(e){return(0,n.default)({url:"manage/btHealthCheck/checkOrgList",method:"post",data:e})},getOneDetail:function(e){return(0,n.default)({url:"manage/btHealthCheck/getOneDetail",method:"get",data:{id:e.id}})},getappointmentRecord:function(e){return(0,n.default)({url:"manage/btHealthCheck/appointmentRecord",method:"get",data:e})},getappointment:function(e){return(0,n.default)({url:"manage/btHealthCheck/appointment",method:"post",data:e})},cancelAppointment:function(e){return(0,n.default)({url:"manage/btHealthCheck/cancelAppointment",method:"post",data:e})},updateAppointment:function(e){return(0,n.default)({url:"manage/btHealthCheck/updateAppointment",method:"post",data:e})},checkReport:function(e){return(0,n.default)({url:"manage/btHealthCheck/checkReport",method:"get",data:e})},reportList:function(e){return(0,n.default)({url:"manage/btHealthCheck/reportList",method:"get",data:e})},cancelReservation:function(e){return(0,n.default)({url:"manage/btHealthCheck/cancelReservation",method:"get",data:e})},getHCReportAuthList:function(e){return(0,n.default)({url:"manage/btHealthCheck/getHCReportAuthList",method:"get",data:e})},updateHCReportAuthStatus:function(e){return(0,n.default)({url:"manage/btHealthCheck/updateHCReportAuthStatus",method:"post",data:e})}};t.default=r}}]);