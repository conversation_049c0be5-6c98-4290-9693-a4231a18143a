(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_remote-pages-remote-list","pages-institution-institution~pages-workInjuryRecognition-add~pages_lifeCycle-pages-Appointment-Appo~215f5d8d"],{"0019":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var a={xeUpload:n("3378").default},r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",[n("v-uni-view",{staticClass:"upload-wrap"},[n("v-uni-view",{directives:[{name:"show",rawName:"v-show",value:!e.disabled,expression:"!disabled"}],staticClass:"btn-click mgb-16 upload-btn",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleUploadClick.apply(void 0,arguments)}}},[n("v-uni-image",{staticClass:"upload-icon",attrs:{src:e.icons.upload,mode:"aspectFill"}}),n("v-uni-text",{staticClass:"upload-text"},[e._v("上传"+e._s(e.title[e.type]))])],1),e._l(e.fileList,(function(t,a){return n("v-uni-view",{key:a,staticClass:"mgb-16 file-wrap"},[n("v-uni-view",{staticClass:"btn-click file-line",on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.handlePreview(t)}}},[n("v-uni-view",{staticClass:"file-info"},[n("v-uni-image",{staticClass:"file-icon",attrs:{src:e.icons[t.fileType||"file"],mode:"aspectFill"}}),n("v-uni-text",{staticClass:"file-name"},[e._v(e._s(t.name||e.title[e.type]))])],1),e.disabled?e._e():n("v-uni-image",{staticClass:"file-icon",attrs:{src:e.icons.close,mode:"aspectFill"},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.handleDeleteFile(a)}}})],1)],1)})),0===e.fileList.length&&e.disabled?n("v-uni-view",{staticClass:"mgb-16 file-wrap"},[n("v-uni-view",{staticClass:"file-line"},[n("v-uni-text",{staticClass:"file-empty"},[e._v("暂无数据")])],1)],1):e._e()],2),n("xe-upload",{ref:"XeUpload",attrs:{options:e.uploadOptions},on:{callback:function(t){arguments[0]=t=e.$handleEvent(t),e.handleUploadCallback.apply(void 0,arguments)}}})],1)},i=[]},"0a20":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var a={uniIcons:n("67fa").default},r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"uni-stat__select"},[e.label?n("span",{staticClass:"uni-label-text hide-on-phone"},[e._v(e._s(e.label+"："))]):e._e(),n("v-uni-view",{staticClass:"uni-stat-box",class:{"uni-stat__actived":e.current}},[n("v-uni-view",{staticClass:"uni-select",class:{"uni-select--disabled":e.disabled}},[n("v-uni-view",{staticClass:"uni-select__input-box",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSelector.apply(void 0,arguments)}}},[e.current?n("v-uni-view",{staticClass:"uni-select__input-text"},[e._v(e._s(e.textShow))]):n("v-uni-view",{staticClass:"uni-select__input-text uni-select__input-placeholder"},[e._v(e._s(e.typePlaceholder))]),e.current&&e.clear&&!e.disabled?n("v-uni-view",{on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.clearVal.apply(void 0,arguments)}}},[n("uni-icons",{attrs:{type:"clear",color:"#c0c4cc",size:"24"}})],1):n("v-uni-view",[n("uni-icons",{attrs:{type:e.showSelector?"top":"bottom",size:"14",color:"#999"}})],1)],1),e.showSelector?n("v-uni-view",{staticClass:"uni-select--mask",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toggleSelector.apply(void 0,arguments)}}}):e._e(),e.showSelector?n("v-uni-view",{staticClass:"uni-select__selector",style:e.getOffsetByPlacement},[n("v-uni-view",{class:"bottom"==e.placement?"uni-popper__arrow_bottom":"uni-popper__arrow_top"}),n("v-uni-scroll-view",{staticClass:"uni-select__selector-scroll",attrs:{"scroll-y":"true"}},[0===e.mixinDatacomResData.length?n("v-uni-view",{staticClass:"uni-select__selector-empty"},[n("v-uni-text",[e._v(e._s(e.emptyTips))])],1):e._l(e.mixinDatacomResData,(function(t,a){return n("v-uni-view",{key:a,staticClass:"uni-select__selector-item",on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.change(t)}}},[n("v-uni-text",{class:{"uni-select__selector__disabled":t.disable}},[e._v(e._s(e.formatItemName(t)))])],1)}))],2)],1):e._e()],1)],1)],1)},i=[]},1090:function(e,t,n){"use strict";n.r(t);var a=n("0a20"),r=n("9028");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("9ddd");var o=n("828b"),s=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,"974dabca",null,!1,a["a"],void 0);t["default"]=s.exports},"1bab":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={appid:"__UNI__5F78BD0"}},"1bc6":function(e,t,n){var a=n("c40a");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var r=n("967d").default;r("d4cb4c5e",a,!0,{sourceMap:!1,shadowMode:!1})},"1edf":function(e,t,n){"use strict";n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("40b8")),i={name:"u-tag",mixins:[uni.$u.mpMixin,uni.$u.mixin,r.default],data:function(){return{}},computed:{style:function(){var e={};return this.bgColor&&(e.backgroundColor=this.bgColor),this.color&&(e.color=this.color),this.borderColor&&(e.borderColor=this.borderColor),e},textColor:function(){var e={};return this.color&&(e.color=this.color),e},imgStyle:function(){var e="large"===this.size?"17px":"medium"===this.size?"15px":"13px";return{width:e,height:e}},closeSize:function(){var e="large"===this.size?15:"medium"===this.size?13:12;return e},iconSize:function(){var e="large"===this.size?21:"medium"===this.size?19:16;return e},elIconColor:function(){return this.iconColor?this.iconColor:this.plain?this.type:"#ffffff"}},methods:{closeHandler:function(){this.$emit("close",this.name)},clickHandler:function(){this.$emit("click",this.name)}}};t.default=i},"202c7":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){}));var a=function(){var e=this.$createElement,t=this._self._c||e;return t("v-uni-view")},r=[]},"20f3":function(e,t,n){"use strict";var a=n("8bdb"),r=n("5145");a({target:"Array",proto:!0,forced:r!==[].lastIndexOf},{lastIndexOf:r})},2353:function(e,t,n){var a=n("dbad");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var r=n("967d").default;r("3c0daaa0",a,!0,{sourceMap:!1,shadowMode:!1})},"2f17":function(e,t,n){var a=n("c86c");t=a(!1),t.push([e.i,"uni-view[data-v-00dc1734]{box-sizing:border-box}.btn-click[data-v-00dc1734]{transition:all .3s;opacity:1}.btn-click[data-v-00dc1734]:active{opacity:.5}.mgb-16[data-v-00dc1734]{margin-bottom:%?16?%}.mgb-16[data-v-00dc1734]:last-child{margin-bottom:0}.upload-wrap[data-v-00dc1734]{width:100%;border-radius:%?16?%;background:#fff;padding:%?32?%}.upload-wrap .upload-btn[data-v-00dc1734]{width:100%;height:%?176?%;border:%?2?% dashed #aaa;background:#fafafa;border-radius:%?16?%;display:flex;align-items:center;justify-content:center;flex-direction:column}.upload-wrap .upload-btn .upload-icon[data-v-00dc1734]{width:%?48?%;height:30px;margin-bottom:%?8?%}.upload-wrap .upload-btn .upload-text[data-v-00dc1734]{font-size:%?26?%;color:#9e9e9e;line-height:%?40?%}.upload-wrap .file-wrap .file-line[data-v-00dc1734]{width:100%;background:#f5f5f5;border-radius:%?8?%;padding:%?16?%;font-size:%?26?%;color:#1a1a1a;line-height:%?40?%;display:flex;align-items:center;justify-content:space-between}.upload-wrap .file-wrap .file-line .file-info[data-v-00dc1734]{width:90%;display:flex;align-items:center}.upload-wrap .file-wrap .file-line .file-info .file-name[data-v-00dc1734]{max-width:80%;padding-left:%?16?%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.upload-wrap .file-wrap .file-line .file-icon[data-v-00dc1734]{width:%?40?%;height:%?40?%;flex-shrink:0}.upload-wrap .file-wrap .file-line .file-empty[data-v-00dc1734]{color:#999}",""]),e.exports=t},3378:function(e,t,n){"use strict";n.r(t);var a=n("202c7"),r=n("5fdd");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);var o=n("d432");for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);n("ac3f");var s=n("828b");r["default"].__module="XeUpload";var u=Object(s["a"])(o["default"],a["b"],a["c"],!1,null,"25178f5c",null,!1,a["a"],r["default"]);t["default"]=u.exports},4085:function(e,t,n){"use strict";var a=n("8bdb"),r=n("85c1");a({global:!0,forced:r.globalThis!==r},{globalThis:r})},"40b8":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var a={props:{type:{type:String,default:uni.$u.props.tag.type},disabled:{type:[Boolean,String],default:uni.$u.props.tag.disabled},size:{type:String,default:uni.$u.props.tag.size},shape:{type:String,default:uni.$u.props.tag.shape},text:{type:[String,Number],default:uni.$u.props.tag.text},bgColor:{type:String,default:uni.$u.props.tag.bgColor},color:{type:String,default:uni.$u.props.tag.color},borderColor:{type:String,default:uni.$u.props.tag.borderColor},closeColor:{type:String,default:uni.$u.props.tag.closeColor},name:{type:[String,Number],default:uni.$u.props.tag.name},plainFill:{type:Boolean,default:uni.$u.props.tag.plainFill},plain:{type:Boolean,default:uni.$u.props.tag.plain},closable:{type:Boolean,default:uni.$u.props.tag.closable},show:{type:Boolean,default:uni.$u.props.tag.show},icon:{type:String,default:uni.$u.props.tag.icon}}};t.default=a},4352:function(e,t,n){"use strict";(function(e){n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.uploadFile=t.chooseVideo=t.chooseMessageFile=t.chooseMedia=t.chooseImage=t.chooseFile=t.appUploadFile=void 0;var r=a(n("9b1b"));n("bf0f"),n("fd3c"),n("4626"),n("5ac7");var i=n("7529"),o=function(e){return(0,i.awaitWrap)(new Promise((function(t,n){uni.chooseImage((0,r.default)((0,r.default)({},e),{},{success:function(e){var n=null===e||void 0===e?void 0:e.tempFiles.map((function(e){return{tempFilePath:e.path,tempFile:e,size:e.size,name:e.name,type:e.type,fileType:"image"}}));return t((0,r.default)((0,r.default)({type:"image"},e),{},{tempFiles:n}))},fail:function(e){return n({mode:"chooseImage",data:e})}}))})))};t.chooseImage=o;var s=function(e){return(0,i.awaitWrap)(new Promise((function(t,n){uni.chooseVideo((0,r.default)((0,r.default)({},e),{},{success:function(e){var n,a,i=[(0,r.default)((0,r.default)({},e),{},{tempFilePath:e.tempFilePath,tempFile:null!==(n=e.tempFile)&&void 0!==n?n:{},size:e.size,name:e.name,type:null===(a=e.tempFile)||void 0===a?void 0:a.type,fileType:"video"})];return t({type:"video",tempFiles:i})},fail:function(e){return n({mode:"chooseVideo",data:e})}}))})))};t.chooseVideo=s;t.chooseMedia=function(t,n){return t?uni.chooseMedia||"image"!==t?uni.chooseMedia||"video"!==t?(0,i.awaitWrap)(new Promise((function(e,a){uni.chooseMedia((0,r.default)((0,r.default)({},n),{},{mediaType:[t],success:function(t){return e(t)},fail:function(e){return a({mode:"chooseMedia",data:e})}}))}))):s(n):o(n):e.error("chooseMedia type cannot be empty")};t.chooseFile=function(e){return(0,i.awaitWrap)(new Promise((function(t,n){uni.chooseFile((0,r.default)((0,r.default)({},e),{},{success:function(e){var n=null===e||void 0===e?void 0:e.tempFiles.map((function(e){var t="file";return e.type.includes("image")&&(t="image"),e.type.includes("video")&&(t="video"),{tempFilePath:e.path,tempFile:e,size:e.size,name:e.name,type:e.type,fileType:t}}));return t((0,r.default)((0,r.default)({type:"file"},e),{},{tempFiles:n}))},fail:function(e){return n({mode:"chooseFile",data:e})}}))})))};t.chooseMessageFile=function(e){return(0,i.awaitWrap)(new Promise((function(t,n){wx.chooseMessageFile((0,r.default)((0,r.default)({},e),{},{success:function(e){var n=null===e||void 0===e?void 0:e.tempFiles.map((function(e){var t;return(0,r.default)((0,r.default)({},e),{},{tempFilePath:e.path,fileType:null!==(t=e.type)&&void 0!==t?t:"file"})}));return t((0,r.default)((0,r.default)({type:"file"},e),{},{tempFiles:n}))},fail:function(e){return n({mode:"chooseMessageFile",data:e})}}))})))};t.uploadFile=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(n,a){uni.uploadFile((0,r.default)((0,r.default)({},e),{},{success:function(e){return n((0,r.default)((0,r.default)({},t),{},{response:JSON.parse(e.data)}))},fail:function(e){return a({mode:"uploadFile",data:e})}}))}))};t.appUploadFile=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,a=e.url,i=e.header,o=e.formData;return new Promise((function(e,s){var u=new XMLHttpRequest;for(var c in u.open("POST",a,!0),i)u.setRequestHeader(c,i[c]);n&&(u.upload.onprogress=n),u.onreadystatechange=function(){4===u.readyState&&(200===u.status?e((0,r.default)((0,r.default)({},t),{},{response:JSON.parse(u.responseText)})):s({mode:"uploadFile",data:{data:u.responseText,errMsg:"uploadFile fail."}}))},u.send(o)}))}}).call(this,n("ba7c")["default"])},4468:function(e,t,n){var a=n("867c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var r=n("967d").default;r("6f184da2",a,!0,{sourceMap:!1,shadowMode:!1})},"46b3e":function(e,t,n){"use strict";n.r(t);var a=n("6028"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},"506a":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var a={gracePage:n("1367").default,graceDialog:n("6fb7").default,uniDataSelect:n("1090").default,uniDatetimePicker:n("ca85").default,uInput:n("bb5c").default,uTag:n("8857").default,uButton:n("d9f5").default},r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[n("v-uni-view",{attrs:{slot:"gHeader"},slot:"gHeader"},[n("v-uni-view",{staticClass:"grace-header-body"},[n("v-uni-view",{staticClass:"grace-header-content"},[n("v-uni-view",{staticStyle:{width:"100%",display:"flex","justify-content":"center",color:"azure"}},[n("v-uni-text",[e._v("远程会诊")])],1)],1)],1)],1),n("v-uni-view",{staticStyle:{padding:"0 10px","background-color":"#F8F8F8",height:"100vh"},attrs:{slot:"gBody"},slot:"gBody"},[n("graceDialog",{ref:"fullNoticeDialog",attrs:{isTitle:!1,closeBtnColor:"#999"},on:{closeDialog:function(t){arguments[0]=t=e.$handleEvent(t),e.closeDialog2.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"content2",attrs:{slot:"content"},slot:"content"},[n("v-uni-view",{staticStyle:{display:"flex",width:"250px","margin-left":"20px","justify-content":"space-around","align-items":"center","margin-top":"50px"}},[n("v-uni-text",{staticStyle:{"margin-right":"20px"}},[e._v("选择会诊专家")]),n("uni-data-select",{attrs:{localdata:e.experts},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.change.apply(void 0,arguments)}},model:{value:e.selectValue,callback:function(t){e.selectValue=t},expression:"selectValue"}})],1),n("v-uni-view",{staticStyle:{display:"flex",width:"250px","margin-left":"20px","justify-content":"space-between","align-items":"center","margin-top":"20px"}},[n("v-uni-text",{staticStyle:{"margin-right":"20px"}},[e._v("选择会诊医生")]),n("uni-data-select",{attrs:{localdata:e.doctorList},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.change2.apply(void 0,arguments)}},model:{value:e.selectDoctor,callback:function(t){e.selectDoctor=t},expression:"selectDoctor"}})],1),n("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-around","align-items":"center","margin-top":"20px"}},[n("v-uni-text",[e._v("选择会诊日期")]),n("v-uni-view",{staticStyle:{width:"140px"}},[n("uni-datetime-picker",{attrs:{type:"date"},model:{value:e.remoteDate,callback:function(t){e.remoteDate=t},expression:"remoteDate"}})],1)],1),n("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-around","align-items":"center","margin-top":"20px"}},[n("v-uni-text",[e._v("填写中毒物质名称")]),n("v-uni-view",{staticStyle:{width:"140px"}},[n("u-input",{attrs:{placeholder:"请输入"},model:{value:e.poisonName,callback:function(t){e.poisonName=t},expression:"poisonName"}})],1)],1),n("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-around","align-items":"center","margin-top":"20px"}},[n("v-uni-text",[e._v("填写中毒症状")]),n("v-uni-view",{staticStyle:{width:"140px"}},[n("u-input",{attrs:{placeholder:"请输入"},model:{value:e.symptoms,callback:function(t){e.symptoms=t},expression:"symptoms"}})],1)],1),n("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-around","align-items":"center","margin-top":"20px"}},[n("v-uni-text",[e._v("中毒时间")]),n("v-uni-view",{staticStyle:{width:"140px"}},[n("uni-datetime-picker",{attrs:{type:"date"},model:{value:e.poisonTime,callback:function(t){e.poisonTime=t},expression:"poisonTime"}})],1)],1),n("v-uni-view",{staticStyle:{"margin-top":"20px"}},[n("v-uni-view",{staticStyle:{"padding-left":"30px"}},[e._v("上传资料")]),n("UploadDemo",{ref:"UploadDemo",attrs:{type:"file"}})],1)],1),n("v-uni-view",{staticClass:"grace-space-between",staticStyle:{padding:"20px 20px"},attrs:{slot:"btns"},slot:"btns"},[n("v-uni-button",{staticStyle:{"background-color":"#008AFF",border:"none",color:"#fff",width:"80%",height:"40px"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.confirmReserve.apply(void 0,arguments)}}},[e._v("确认")])],1)],1),n("v-uni-view",{staticStyle:{"margin-top":"20px"}},[n("v-uni-button",{staticStyle:{"background-color":"#008AFF",color:"#fff"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.reserve.apply(void 0,arguments)}}},[n("v-uni-text",{staticStyle:{"padding-right":"10px","font-size":"18px"}},[e._v("+")]),e._v("预约远程会诊")],1)],1),n("v-uni-view",{staticStyle:{"margin-top":"10px"}},[n("v-uni-text",{staticStyle:{"font-weight":"bold"}},[e._v("已预约会诊")])],1),e._l(e.info,(function(t,a){return n("v-uni-view",{key:a,staticStyle:{"background-color":"#fff",padding:"10px","border-radius":"5px","margin-top":"10px","box-shadow":"0 0 5px #ccc"}},[n("v-uni-view",{staticClass:"flex space-between"},[n("v-uni-view",[n("v-uni-text",[e._v("会诊日期：")]),n("v-uni-text",[e._v(e._s(t.startTime))])],1),n("v-uni-view",[0===t.status?n("u-tag",{attrs:{type:"warning",round:!0,text:"待开始"}}):e._e(),1===t.status?n("u-tag",{attrs:{type:"primary",round:!0,text:"进行中"}}):e._e(),2===t.status?n("u-tag",{attrs:{type:"success",round:!0,text:"已结束"}}):e._e()],1)],1),n("v-uni-view",{staticStyle:{"margin-top":"5px"}},[n("v-uni-text",[e._v("会诊专家：")]),n("v-uni-text",[e._v(e._s(t.expertName))])],1),n("v-uni-view",{staticStyle:{"margin-top":"5px"}},[n("v-uni-text",[e._v("会诊医生：")]),n("v-uni-text",[e._v(e._s(t.doctorName))])],1),n("v-uni-view",{staticStyle:{"margin-top":"5px"}},[n("v-uni-text",[e._v("中毒物质名称：")]),n("v-uni-text",[e._v(e._s(t.poisonName))])],1),n("v-uni-view",{staticStyle:{"margin-top":"5px"}},[n("v-uni-text",[e._v("中毒时间：")]),n("v-uni-text",[e._v(e._s(t.poisonTime))])],1),n("v-uni-view",{staticClass:"flex space-between",staticStyle:{"margin-top":"10px"}},[1===t.status?n("v-uni-button",{staticClass:"flex1",staticStyle:{display:"flex","align-items":"center","justify-content":"center"},on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.goMeeting(t._id)}}},[n("v-uni-text",{staticStyle:{color:"#008AFF","font-size":"15px"}},[e._v("进入远程会诊")]),n("v-uni-text",{staticClass:"grace-text-small grace-blue grace-icons icon-arrow-right icon-left-margin",staticStyle:{color:"#008AFF"}})],1):e._e(),0===t.status?n("u-button",{staticClass:"flex1",on:{click:function(n){arguments[0]=n=e.$handleEvent(n),e.deleteMeeting(t._id)}}},[e._v("删除")]):e._e()],1)],1)})),n("v-uni-view",{staticStyle:{"margin-top":"10px"}},[n("v-uni-text",{staticStyle:{"font-weight":"bold"}},[e._v("近期会诊记录")])],1),0===e.history.length?n("v-uni-view",{staticStyle:{display:"flex","justify-content":"center","padding-bottom":"30px"}},[n("v-uni-text",{staticStyle:{color:"#ccc"}},[e._v("暂无数据")])],1):n("v-uni-view",{staticStyle:{"padding-bottom":"30px"}},e._l(e.history,(function(t,a){return n("v-uni-view",{key:a,staticStyle:{"background-color":"#fff",padding:"10px","border-radius":"5px","margin-top":"10px","box-shadow":"0 0 5px #ccc"}},[n("v-uni-view",[n("v-uni-text",[e._v("会诊日期：")]),n("v-uni-text",[e._v(e._s(t.startTime))])],1),n("v-uni-view",[n("v-uni-text",[e._v("会诊专家：")]),n("v-uni-text",[e._v(e._s(t.expertName))])],1),n("v-uni-view",[n("v-uni-text",[e._v("会诊医生：")]),n("v-uni-text",[e._v(e._s(t.doctorName))])],1),n("v-uni-view",[n("v-uni-text",[e._v("会诊结论：")]),n("v-uni-text",[e._v(e._s(t.conclusion))])],1),n("v-uni-view",[n("v-uni-text",[e._v("会诊建议：")]),n("v-uni-text",[e._v(e._s(t.suggestion))])],1),n("v-uni-view",[n("v-uni-text",[e._v("会诊结果：")]),n("v-uni-text",{staticStyle:{color:"#008AFF"},on:{click:function(n){arguments[0]=n=e.$handleEvent(n),function(){e.downloadReportFile(t)}.apply(void 0,arguments)}}},[e._v("下载")])],1)],1)})),1)],2)],1)},i=[]},"562b":function(e,t,n){"use strict";n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("4626"),n("c223"),n("8f71"),n("bf0f"),n("fd3c"),n("18f7"),n("de6c");var r=a(n("9b1b")),i=a(n("2634")),o=a(n("5de6")),s=a(n("2fdc")),u=n("4352"),c=n("7529"),l={name:"XeUpload",props:{options:{default:function(){return{}},type:Object}},data:function(){return{id:0,renderInput:""}},computed:{mergeOptions:function(e){var t=e.options,n=void 0===t?{}:t;return(0,c.deepMerge)({name:"file"},n)},mergeProps:function(e){var t=e.id,n=e.renderInput,a=e.mergeOptions;return{id:t,renderInput:n,upload:a}}},methods:{upload:function(e){var t=arguments,n=this;return(0,s.default)((0,i.default)().mark((function a(){var r,s,c,l,d,f,p,h,g,v,m;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(r=t.length>1&&void 0!==t[1]?t[1]:{},s=[],!["image","video"].includes(e)){a.next=12;break}return a.next=5,(0,u.chooseMedia)(e,r);case 5:if(c=a.sent,l=(0,o.default)(c,2),d=l[0],f=l[1],!d){a.next=11;break}return a.abrupt("return",n.handleError(d));case 11:s=(null===f||void 0===f?void 0:f.tempFiles)||[];case 12:if(!["file"].includes(e)){a.next=24;break}return h={},g=null,a.next=17,(0,u.chooseFile)(r);case 17:if(v=a.sent,m=(0,o.default)(v,2),g=m[0],h=m[1],!g){a.next=23;break}return a.abrupt("return",n.handleError(g));case 23:s=(null===(p=h)||void 0===p?void 0:p.tempFiles)||[];case 24:n.handleUpload(s);case 25:case"end":return a.stop()}}),a)})))()},initInput:function(e){var t=this.id,n=e;(0,c.isArray)(e)&&(n=e.join(",")),this.renderInput='<input type="file" id="xe-upload-'.concat(t,'" name="xe-upload" ').concat(n?'accept="'+n+'"':""," />")},handleUpload:function(){var e=arguments,t=this;return(0,s.default)((0,i.default)().mark((function n(){var a,s,l,d,f,p,h;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=e.length>0&&void 0!==e[0]?e[0]:[],0!==a.filter((function(e){return Boolean(e)})).length){n.next=3;break}return n.abrupt("return");case 3:if(s=t.mergeOptions,s.url){n.next=6;break}return n.abrupt("return",t.handleEmits({type:"choose",data:a}));case 6:return l=a.map((function(e){return(0,u.uploadFile)((0,r.default)((0,r.default)({},s),{},{filePath:e.tempFilePath}),e)})),n.next=9,(0,c.awaitWrap)(Promise.all(l));case 9:if(d=n.sent,f=(0,o.default)(d,2),p=f[0],h=f[1],!p){n.next=15;break}return n.abrupt("return",t.handleError(p));case 15:t.handleEmits({type:"success",data:h});case 16:case"end":return n.stop()}}),n)})))()},handleError:function(e){this.handleEmits({type:"warning",data:e})},handleEmits:function(e){var t=this;return(0,s.default)((0,i.default)().mark((function n(){return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:t.$emit("callback",e);case 1:case"end":return n.stop()}}),n)})))()}}};t.default=l},"5d50":function(e,t,n){"use strict";var a=n("1bc6"),r=n.n(a);r.a},"5fdd":function(e,t,n){"use strict";n.r(t);var a=n("f0e4"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},6028:function(e,t,n){"use strict";(function(e){n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("b7c7")),i=a(n("9b1b"));n("4626"),n("fd3c"),n("aa9c"),n("dd2b");var o=a(n("3378")),s=function(){return"https://xjbthxzdbackend.jkqy.cn/"},u={name:"UploadDemo",components:{xeUpload:o.default},props:{type:{default:"image",type:String},list:{default:function(){return[]},type:Array},disabled:{default:!1,type:Boolean}},data:function(){return{uploadOptions:{url:"".concat(s(),"uploadMass/uploadNormalFile")},fileList:[],title:{image:"图片",video:"视频",file:"文件"},icons:{upload:"/static/icon_upload.png",close:"/static/icon_close.png",image:"/static/icon_image.png",video:"/static/icon_video.png",file:"/static/icon_file.png"}}},watch:{list:{handler:function(e){this.fileList=e||[]},immediate:!0,deep:!0}},methods:{handleUploadClick:function(){this.$refs.XeUpload.upload(this.type)},handleUploadCallback:function(t){if(e.log("UploadCallback",t),["choose","success"].includes(t.type)){var n,a=(t.data||[]).map((function(e){var t=e.response,n=(e.tempFilePath,e.name,e.fileType),a=t.data||{},r=t.data.url,o=t.data.name;return(0,i.default)((0,i.default)({},a),{},{url:r,name:o,fileType:n})}));(n=this.fileList).push.apply(n,(0,r.default)(a))}},handleUploadFile:function(t){var n=t.url;e.log("UploadFile",n),uni.uploadFile({url:"http://192.168.31.185:3000/api/upload",filePath:n,name:"file",success:function(t){e.log("handleUpload success",t);var n=JSON.parse(t.data);uni.showToast({title:n.success?"上传成功":"上传失败",icon:"none"})},fail:function(t){e.log("handleUpload fail",t),uni.showToast({title:"出错啦",icon:"none"})}})},handlePreview:function(t){var n=t.url;e.log("PreviewFile",n),n="".concat(s())+n;var a=this.getFileType(n);return"image"===a?uni.previewImage({current:n,urls:[n]}):"office"===a?(e.log("Office file preview not supported on this platform"),uni.openDocument({filePath:n,fail:function(t){e.log(t),uni.showToast({icon:"none",title:"文件预览失败"})}})):void uni.showModal({title:"提示",content:n,showCancel:!1})},handleDeleteFile:function(e){this.fileList.splice(e,1)},getFileType:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.split("."),n=t[t.length-1];if(!n)return"";n=n.toLocaleLowerCase();var a=["png","jpg","jpeg","bmp","gif","webp"];if(a.includes(n))return"image";var r=["mp4","m4v"];if(r.includes(n))return"video";var i=["mp3","m4a","wav","aac"];if(i.includes(n))return"audio";var o=["pdf","doc","docx","xls","xlsx","ppt","pptx","txt","plain"];return o.includes(n)?"office":"unknown"}}};t.default=u}).call(this,n("ba7c")["default"])},"62b0":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t&&("object"===(0,a.default)(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return(0,r.default)(e)},n("7a76"),n("c9b5");var a=i(n("fcf3")),r=i(n("f478"));function i(e){return e&&e.__esModule?e:{default:e}}},"650d":function(e,t,n){"use strict";var a=n("2353"),r=n.n(a);r.a},6730:function(e,t,n){"use strict";var a=n("8bdb"),r=n("71e9");a({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return r(URL.prototype.toString,this)}})},"68ef":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=u,n("c1a3"),n("bf0f"),n("18f7"),n("de6c"),n("7a76"),n("c9b5");var a=s(n("f1f8")),r=s(n("e668")),i=s(n("d441")),o=s(n("d2c4"));function s(e){return e&&e.__esModule?e:{default:e}}function u(e){var n="function"===typeof Map?new Map:void 0;return t.default=u=function(e){if(null===e||!(0,i.default)(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof n){if(n.has(e))return n.get(e);n.set(e,t)}function t(){return(0,o.default)(e,arguments,(0,a.default)(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),(0,r.default)(t,e)},u(e)}},"6a88":function(e,t,n){"use strict";var a=n("8bdb"),r=n("6aa6"),i=n("9f9e"),o=n("8598"),s=n("5ee2"),u=n("e7e3"),c=n("1c06"),l=n("e37c"),d=n("af9e"),f=r("Reflect","construct"),p=Object.prototype,h=[].push,g=d((function(){function e(){}return!(f((function(){}),[],e)instanceof e)})),v=!d((function(){f((function(){}))})),m=g||v;a({target:"Reflect",stat:!0,forced:m,sham:m},{construct:function(e,t){s(e),u(t);var n=arguments.length<3?e:s(arguments[2]);if(v&&!g)return f(e,t,n);if(e===n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var a=[null];return i(h,a,t),new(i(o,e,a))}var r=n.prototype,d=l(c(r)?r:p),m=i(e,d,t);return c(m)?m:d}})},"6c31":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}},n("bf0f"),n("7996"),n("6a88")},"6fb7":function(e,t,n){"use strict";n.r(t);var a=n("a1ef"),r=n("933b");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("5d50");var o=n("828b"),s=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,"a889d8f4",null,!1,a["a"],void 0);t["default"]=s.exports},7529:function(e,t,n){"use strict";n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.isObject=t.isArray=t.fileToBlob=t.fileToBase64=t.deepMerge=t.deepClone=t.base64ToPath=t.awaitWrap=void 0;var r=a(n("fcf3"));n("bf0f"),n("c223"),n("18f7"),n("de6c"),n("dc89"),n("2425"),n("7a76"),n("c9b5"),n("dd2b"),n("5c47"),n("2c10");var i=function(e){return!!e&&"[object Object]"===Object.prototype.toString.call(e)};t.isObject=i;var o=function(e){return!!e&&Array.isArray(e)};t.isArray=o;t.awaitWrap=function(e){return e.then((function(e){return[null,e]})).catch((function(e){return[e,{}]}))};var s=function e(t){if(!i(t)&&!o(t))return t;var n=o(t)?[]:{};for(var a in t)t.hasOwnProperty(a)&&(t[a]&&"object"===(0,r.default)(t[a])?(n[a]=o(t[a])?[]:{},n[a]=e(t[a])):n[a]=t[a]);return n};t.deepClone=s;t.deepMerge=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t=s(t),"object"!==(0,r.default)(t)||"object"!==(0,r.default)(n))return!1;for(var a in n)n.hasOwnProperty(a)&&(a in t?"object"!==(0,r.default)(t[a])||"object"!==(0,r.default)(n[a])?t[a]=n[a]:t[a].concat&&n[a].concat?t[a]=t[a].concat(n[a]):t[a]=e(t[a],n[a]):t[a]=n[a]);return t};t.fileToBlob=function(e){if(e){var t=e.type,n=new Blob([e],{type:t||"application/*"}),a=window.URL.createObjectURL(n);return a}};function u(e){var t=e.split(",");return t[t.length-1]}function c(e,t){for(var n=e.split("."),a=t.split("."),r=!1,i=0;i<a.length;i++){var o=n[i]-a[i];if(0!==o){r=o>0;break}}return r}t.fileToBase64=function(e){if(e)return new Promise((function(t,n){var a=new FileReader;a.onloadend=function(){var e=a.result;t(e)},a.onerror=function(){n({mode:"fileToBase64",data:{errMsg:"File to base64 fail."}})},a.readAsDataURL(e)}))};var l=0;function d(){return Date.now()+String(l++)}t.base64ToPath=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return new Promise((function(n,a){if("object"!==("undefined"===typeof plus?"undefined":(0,r.default)(plus)))return a(new Error("not support"));var i="";if(t){var o=t.split("."),s=o.splice(-1);i="".concat(o.join("."),"-").concat(d(),".").concat(s)}else{var l=e.split(",")[0].match(/data\:\S+\/(\S+);/);l||a(new Error("base64 error"));var f=l[1];i="".concat(d(),".").concat(f)}var p="".concat("_doc","/").concat("uniapp_temp","/").concat(i);if(c("Android"===plus.os.name?"1.9.9.80627":"1.9.9.80472",plus.runtime.innerVersion)){var h=new plus.nativeObj.Bitmap(i);h.loadBase64Data(e,(function(){h.save(p,{},(function(){h.clear(),n(p)}),(function(e){h.clear(),a(e)}))}),(function(e){h.clear(),a(e)}))}else plus.io.resolveLocalFileSystemURL("_doc",(function(t){t.getDirectory("uniapp_temp",{create:!0,exclusive:!1},(function(t){t.getFile(i,{create:!0,exclusive:!1},(function(t){t.createWriter((function(t){t.onwrite=function(){n(p)},t.onerror=a,t.seek(0),t.writeAsBinary(u(e))}),a)}),a)}),a)}),a)}))}},7996:function(e,t,n){"use strict";var a=n("8bdb"),r=n("85c1"),i=n("181d");a({global:!0},{Reflect:{}}),i(r.Reflect,"Reflect",!0)},8598:function(e,t,n){"use strict";var a=n("bb80"),r=n("7992"),i=n("1c06"),o=n("338c"),s=n("37ad"),u=n("8f26"),c=Function,l=a([].concat),d=a([].join),f={},p=function(e,t,n){if(!o(f,t)){for(var a=[],r=0;r<t;r++)a[r]="a["+r+"]";f[t]=c("C,a","return new C("+d(a,",")+")")}return f[t](e,n)};e.exports=u?c.bind:function(e){var t=r(this),n=t.prototype,a=s(arguments,1),o=function(){var n=l(a,s(arguments));return this instanceof o?p(t,n.length,n):t.apply(e,n)};return i(n)&&(o.prototype=n),o}},"861b":function(e,t,n){"use strict";(function(e,a){var r=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.uniCloud=t.default=t.UniCloudError=void 0;var i=r(n("f478")),o=r(n("5de6")),s=r(n("fcf3")),u=r(n("b7c7")),c=r(n("3471")),l=r(n("2634")),d=r(n("2fdc")),f=r(n("9b1b")),p=r(n("acb1")),h=r(n("cad9")),g=r(n("68ef")),v=r(n("80b1")),m=r(n("efe5"));n("4085"),n("7a76"),n("c9b5"),n("bf0f"),n("ab80"),n("f7a5"),n("aa9c"),n("e966"),n("c223"),n("dd2b"),n("5ef2"),n("2797"),n("dc8a"),n("473f"),n("4626"),n("5ac7"),n("4100"),n("5c47"),n("d4b5"),n("0c26"),n("0506"),n("fd3c"),n("6a54"),n("a1c1"),n("de6c"),n("c1a3"),n("18f7"),n("af8f"),n("64aa"),n("8f71"),n("23f4"),n("7d2f"),n("9c4e"),n("4db2"),n("c976"),n("4d8f"),n("7b97"),n("668a"),n("c5b7"),n("8ff5"),n("2378"),n("641a"),n("64e0"),n("cce3"),n("efba"),n("d009"),n("bd7d"),n("7edd"),n("d798"),n("f547"),n("5e54"),n("b60a"),n("8c18"),n("12973"),n("f991"),n("198e"),n("8557"),n("63b1"),n("1954"),n("1cf1"),n("01a2"),n("e39c"),n("e062"),n("aa77"),n("2c10"),n("f555"),n("dc69"),n("9370"),n("6730"),n("08eb"),n("15d1"),n("d5c6"),n("5a56"),n("f074"),n("20f3");var y=r(n("9572"));function b(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof e||"undefined"!=typeof self&&self;var w=b((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),a={},r=a.lib={},i=r.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},o=r.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,a=this.sigBytes,r=e.sigBytes;if(this.clamp(),a%4)for(var i=0;i<r;i++){var o=n[i>>>2]>>>24-i%4*8&255;t[a+i>>>2]|=o<<24-(a+i)%4*8}else for(i=0;i<r;i+=4)t[a+i>>>2]=n[i>>>2];return this.sigBytes+=r,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,a=[],r=function(t){var n=987654321,a=4294967295;return function(){var r=((n=36969*(65535&n)+(n>>16)&a)<<16)+(t=18e3*(65535&t)+(t>>16)&a)&a;return r/=4294967296,(r+=.5)*(e.random()>.5?1:-1)}},i=0;i<t;i+=4){var s=r(4294967296*(n||e.random()));n=987654071*s(),a.push(4294967296*s()|0)}return new o.init(a,t)}}),s=a.enc={},u=s.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,a=[],r=0;r<n;r++){var i=t[r>>>2]>>>24-r%4*8&255;a.push((i>>>4).toString(16)),a.push((15&i).toString(16))}return a.join("")},parse:function(e){for(var t=e.length,n=[],a=0;a<t;a+=2)n[a>>>3]|=parseInt(e.substr(a,2),16)<<24-a%8*4;return new o.init(n,t/2)}},c=s.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,a=[],r=0;r<n;r++){var i=t[r>>>2]>>>24-r%4*8&255;a.push(String.fromCharCode(i))}return a.join("")},parse:function(e){for(var t=e.length,n=[],a=0;a<t;a++)n[a>>>2]|=(255&e.charCodeAt(a))<<24-a%4*8;return new o.init(n,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},d=r.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new o.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,a=n.words,r=n.sigBytes,i=this.blockSize,s=r/(4*i),u=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*i,c=e.min(4*u,r);if(u){for(var l=0;l<u;l+=i)this._doProcessBlock(a,l);var d=a.splice(0,u);n.sigBytes-=c}return new o.init(d,c)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});r.Hasher=d.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new f.HMAC.init(e,n).finalize(t)}}});var f=a.algo={};return a}(Math),n)})),_=w,x=(b((function(e,t){var n;e.exports=(n=_,function(e){var t=n,a=t.lib,r=a.WordArray,i=a.Hasher,o=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var u=o.MD5=i.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var a=t+n,r=e[a];e[a]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8)}var i=this._hash.words,o=e[t+0],u=e[t+1],p=e[t+2],h=e[t+3],g=e[t+4],v=e[t+5],m=e[t+6],y=e[t+7],b=e[t+8],w=e[t+9],_=e[t+10],x=e[t+11],k=e[t+12],S=e[t+13],T=e[t+14],I=e[t+15],P=i[0],O=i[1],A=i[2],C=i[3];P=c(P,O,A,C,o,7,s[0]),C=c(C,P,O,A,u,12,s[1]),A=c(A,C,P,O,p,17,s[2]),O=c(O,A,C,P,h,22,s[3]),P=c(P,O,A,C,g,7,s[4]),C=c(C,P,O,A,v,12,s[5]),A=c(A,C,P,O,m,17,s[6]),O=c(O,A,C,P,y,22,s[7]),P=c(P,O,A,C,b,7,s[8]),C=c(C,P,O,A,w,12,s[9]),A=c(A,C,P,O,_,17,s[10]),O=c(O,A,C,P,x,22,s[11]),P=c(P,O,A,C,k,7,s[12]),C=c(C,P,O,A,S,12,s[13]),A=c(A,C,P,O,T,17,s[14]),P=l(P,O=c(O,A,C,P,I,22,s[15]),A,C,u,5,s[16]),C=l(C,P,O,A,m,9,s[17]),A=l(A,C,P,O,x,14,s[18]),O=l(O,A,C,P,o,20,s[19]),P=l(P,O,A,C,v,5,s[20]),C=l(C,P,O,A,_,9,s[21]),A=l(A,C,P,O,I,14,s[22]),O=l(O,A,C,P,g,20,s[23]),P=l(P,O,A,C,w,5,s[24]),C=l(C,P,O,A,T,9,s[25]),A=l(A,C,P,O,h,14,s[26]),O=l(O,A,C,P,b,20,s[27]),P=l(P,O,A,C,S,5,s[28]),C=l(C,P,O,A,p,9,s[29]),A=l(A,C,P,O,y,14,s[30]),P=d(P,O=l(O,A,C,P,k,20,s[31]),A,C,v,4,s[32]),C=d(C,P,O,A,b,11,s[33]),A=d(A,C,P,O,x,16,s[34]),O=d(O,A,C,P,T,23,s[35]),P=d(P,O,A,C,u,4,s[36]),C=d(C,P,O,A,g,11,s[37]),A=d(A,C,P,O,y,16,s[38]),O=d(O,A,C,P,_,23,s[39]),P=d(P,O,A,C,S,4,s[40]),C=d(C,P,O,A,o,11,s[41]),A=d(A,C,P,O,h,16,s[42]),O=d(O,A,C,P,m,23,s[43]),P=d(P,O,A,C,w,4,s[44]),C=d(C,P,O,A,k,11,s[45]),A=d(A,C,P,O,I,16,s[46]),P=f(P,O=d(O,A,C,P,p,23,s[47]),A,C,o,6,s[48]),C=f(C,P,O,A,y,10,s[49]),A=f(A,C,P,O,T,15,s[50]),O=f(O,A,C,P,v,21,s[51]),P=f(P,O,A,C,k,6,s[52]),C=f(C,P,O,A,h,10,s[53]),A=f(A,C,P,O,_,15,s[54]),O=f(O,A,C,P,u,21,s[55]),P=f(P,O,A,C,b,6,s[56]),C=f(C,P,O,A,I,10,s[57]),A=f(A,C,P,O,m,15,s[58]),O=f(O,A,C,P,S,21,s[59]),P=f(P,O,A,C,g,6,s[60]),C=f(C,P,O,A,x,10,s[61]),A=f(A,C,P,O,p,15,s[62]),O=f(O,A,C,P,w,21,s[63]),i[0]=i[0]+P|0,i[1]=i[1]+O|0,i[2]=i[2]+A|0,i[3]=i[3]+C|0},_doFinalize:function(){var t=this._data,n=t.words,a=8*this._nDataBytes,r=8*t.sigBytes;n[r>>>5]|=128<<24-r%32;var i=e.floor(a/4294967296),o=a;n[15+(r+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(r+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),t.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,u=s.words,c=0;c<4;c++){var l=u[c];u[c]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,n,a,r,i,o){var s=e+(t&n|~t&a)+r+o;return(s<<i|s>>>32-i)+t}function l(e,t,n,a,r,i,o){var s=e+(t&a|n&~a)+r+o;return(s<<i|s>>>32-i)+t}function d(e,t,n,a,r,i,o){var s=e+(t^n^a)+r+o;return(s<<i|s>>>32-i)+t}function f(e,t,n,a,r,i,o){var s=e+(n^(t|~a))+r+o;return(s<<i|s>>>32-i)+t}t.MD5=i._createHelper(u),t.HmacMD5=i._createHmacHelper(u)}(Math),n.MD5)})),b((function(e,t){var n;e.exports=(n=_,void function(){var e=n,t=e.lib.Base,a=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=a.parse(t));var n=e.blockSize,r=4*n;t.sigBytes>r&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),o=this._iKey=t.clone(),s=i.words,u=o.words,c=0;c<n;c++)s[c]^=1549556828,u[c]^=909522486;i.sigBytes=o.sigBytes=r,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}())})),b((function(e,t){e.exports=_.HmacMD5}))),k=b((function(e,t){e.exports=_.enc.Utf8})),S=b((function(e,t){var n;e.exports=(n=_,function(){var e=n,t=e.lib.WordArray;function a(e,n,a){for(var r=[],i=0,o=0;o<n;o++)if(o%4){var s=a[e.charCodeAt(o-1)]<<o%4*2,u=a[e.charCodeAt(o)]>>>6-o%4*2;r[i>>>2]|=(s|u)<<24-i%4*8,i++}return t.create(r,i)}e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,a=this._map;e.clamp();for(var r=[],i=0;i<n;i+=3)for(var o=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<n;s++)r.push(a.charAt(o>>>6*(3-s)&63));var u=a.charAt(64);if(u)for(;r.length%4;)r.push(u);return r.join("")},parse:function(e){var t=e.length,n=this._map,r=this._reverseMap;if(!r){r=this._reverseMap=[];for(var i=0;i<n.length;i++)r[n.charCodeAt(i)]=i}var o=n.charAt(64);if(o){var s=e.indexOf(o);-1!==s&&(t=s)}return a(e,t,r)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),n.enc.Base64)})),T="uni_id_token",I="uni_id_token_expired",P={DEFAULT:"FUNCTION",FUNCTION:"FUNCTION",OBJECT:"OBJECT",CLIENT_DB:"CLIENT_DB"},O="pending",A="fulfilled",C="rejected";function E(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function R(e){return"object"===E(e)}function D(e){return"function"==typeof e}function L(e){return function(){try{return e.apply(e,arguments)}catch(e){a.error(e)}}}var N="REJECTED",U="NOT_PENDING",M=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.createPromise,a=t.retryRule,r=void 0===a?N:a;(0,v.default)(this,e),this.createPromise=n,this.status=null,this.promise=null,this.retryRule=r}return(0,m.default)(e,[{key:"needRetry",get:function(){if(!this.status)return!0;switch(this.retryRule){case N:return this.status===C;case U:return this.status!==O}}},{key:"exec",value:function(){var e=this;return this.needRetry?(this.status=O,this.promise=this.createPromise().then((function(t){return e.status=A,Promise.resolve(t)}),(function(t){return e.status=C,Promise.reject(t)})),this.promise):this.promise}}]),e}(),F=function(){function e(){(0,v.default)(this,e),this._callback={}}return(0,m.default)(e,[{key:"addListener",value:function(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}},{key:"on",value:function(e,t){return this.addListener(e,t)}},{key:"removeListener",value:function(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');var n=this._callback[e];if(n){var a=function(e,t){for(var n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(a,1)}}},{key:"off",value:function(e,t){return this.removeListener(e,t)}},{key:"removeAllListener",value:function(e){delete this._callback[e]}},{key:"emit",value:function(e){for(var t=this._callback[e],n=arguments.length,a=new Array(n>1?n-1:0),r=1;r<n;r++)a[r-1]=arguments[r];if(t)for(var i=0;i<t.length;i++)t[i].apply(t,a)}}]),e}();function j(e){return e&&"string"==typeof e?JSON.parse(e):e}var q=j([]),B="web",K=(j(void 0),j([])||[]);try{(n("1bab").default||n("1bab")).appid}catch(wa){}var z,$={};function H(e){var t,n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=$,n=e,Object.prototype.hasOwnProperty.call(t,n)||($[e]=a),$[e]}"app"===B&&($=uni._globalUniCloudObj?uni._globalUniCloudObj:uni._globalUniCloudObj={});var W=["invoke","success","fail","complete"],V=H("_globalUniCloudInterceptor");function J(e,t){V[e]||(V[e]={}),R(t)&&Object.keys(t).forEach((function(n){W.indexOf(n)>-1&&function(e,t,n){var a=V[e][t];a||(a=V[e][t]=[]),-1===a.indexOf(n)&&D(n)&&a.push(n)}(e,n,t[n])}))}function G(e,t){V[e]||(V[e]={}),R(t)?Object.keys(t).forEach((function(n){W.indexOf(n)>-1&&function(e,t,n){var a=V[e][t];if(a){var r=a.indexOf(n);r>-1&&a.splice(r,1)}}(e,n,t[n])})):delete V[e]}function Q(e,t){return e&&0!==e.length?e.reduce((function(e,n){return e.then((function(){return n(t)}))}),Promise.resolve()):Promise.resolve()}function Y(e,t){return V[e]&&V[e][t]||[]}function X(e){J("callObject",e)}var Z=H("_globalUniCloudListener"),ee={RESPONSE:"response",NEED_LOGIN:"needLogin",REFRESH_TOKEN:"refreshToken"},te={CLIENT_DB:"clientdb",CLOUD_FUNCTION:"cloudfunction",CLOUD_OBJECT:"cloudobject"};function ne(e){return Z[e]||(Z[e]=[]),Z[e]}function ae(e,t){var n=ne(e);n.includes(t)||n.push(t)}function re(e,t){var n=ne(e),a=n.indexOf(t);-1!==a&&n.splice(a,1)}function ie(e,t){for(var n=ne(e),a=0;a<n.length;a++)(0,n[a])(t)}var oe,se=!1;function ue(){return oe||(oe=new Promise((function(e){se&&e(),function t(){if("function"==typeof getCurrentPages){var n=getCurrentPages();n&&n[0]&&(se=!0,e())}se||setTimeout((function(){t()}),30)}()})),oe)}function ce(e){var t={};for(var n in e){var a=e[n];D(a)&&(t[n]=L(a))}return t}var le=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(e){var a;(0,v.default)(this,n);var r=e.message||e.errMsg||"unknown system error";return a=t.call(this,r),a.errMsg=r,a.code=a.errCode=e.code||e.errCode||"SYSTEM_ERROR",a.errSubject=a.subject=e.subject||e.errSubject,a.cause=e.cause,a.requestId=e.requestId,a}return(0,m.default)(n,[{key:"toJson",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}]),n}((0,g.default)(Error));t.UniCloudError=le;var de,fe,pe={request:function(e){return uni.request(e)},uploadFile:function(e){return uni.uploadFile(e)},setStorageSync:function(e,t){return uni.setStorageSync(e,t)},getStorageSync:function(e){return uni.getStorageSync(e)},removeStorageSync:function(e){return uni.removeStorageSync(e)},clearStorageSync:function(){return uni.clearStorageSync()},connectSocket:function(e){return uni.connectSocket(e)}};function he(){return{token:pe.getStorageSync(T)||pe.getStorageSync("uniIdToken"),tokenExpired:pe.getStorageSync(I)}}function ge(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.token,n=e.tokenExpired;t&&pe.setStorageSync(T,t),n&&pe.setStorageSync(I,n)}function ve(){return de||(de="mp-weixin"===B&&wx.canIUse("getAppBaseInfo")&&wx.canIUse("getDeviceInfo")?(0,f.default)((0,f.default)({},uni.getAppBaseInfo()),uni.getDeviceInfo()):uni.getSystemInfoSync()),de}var me={};function ye(){var e=uni.getLocale&&uni.getLocale()||"en";if(fe)return(0,f.default)((0,f.default)((0,f.default)({},me),fe),{},{locale:e,LOCALE:e});var t=ve(),n=t.deviceId,a=t.osName,r=t.uniPlatform,i=t.appId,o=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(var s in t)Object.hasOwnProperty.call(t,s)&&-1===o.indexOf(s)&&delete t[s];return fe=(0,f.default)((0,f.default)({PLATFORM:r,OS:a,APPID:i,DEVICEID:n},function(){var e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;var n=uni.getLaunchOptionsSync(),a=n.scene,r=n.channel;e=r,t=a}}catch(e){}return{channel:e,scene:t}}()),t),(0,f.default)((0,f.default)((0,f.default)({},me),fe),{},{locale:e,LOCALE:e})}var be,we={sign:function(e,t){var n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),x(n,t).toString()},wrappedRequest:function(e,t){return new Promise((function(n,a){t(Object.assign(e,{complete:function(e){e||(e={});var t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){var r=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",i=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return a(new le({code:r,message:i,requestId:t}))}var o=e.data;if(o.error)return a(new le({code:o.error.code,message:o.error.message,requestId:t}));o.result=o.data,o.requestId=t,delete o.data,n(o)}}))}))},toBase64:function(e){return S.stringify(k.parse(e))}},_e=function(){function e(t){var n=this;(0,v.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),this.config=Object.assign({},{endpoint:0===t.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},t),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=pe,this._getAccessTokenPromiseHub=new M({createPromise:function(){return n.requestAuth(n.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((function(e){if(!e.result||!e.result.accessToken)throw new le({code:"AUTH_FAILED",message:"获取accessToken失败"});n.setAccessToken(e.result.accessToken)}))},retryRule:U})}return(0,m.default)(e,[{key:"hasAccessToken",get:function(){return!!this.accessToken}},{key:"setAccessToken",value:function(e){this.accessToken=e}},{key:"requestWrapped",value:function(e){return we.wrappedRequest(e,this.adapter.request)}},{key:"requestAuth",value:function(e){return this.requestWrapped(e)}},{key:"request",value:function(e,t){var n=this;return Promise.resolve().then((function(){return n.hasAccessToken?t?n.requestWrapped(e):n.requestWrapped(e).catch((function(t){return new Promise((function(e,n){!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((function(){return n.getAccessToken()})).then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))})):n.getAccessToken().then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))}))}},{key:"rebuildRequest",value:function(e){var t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=we.sign(t.data,this.config.clientSecret),t}},{key:"setupRequest",value:function(e,t){var n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),a={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,a["x-basement-token"]=this.accessToken),a["x-serverless-sign"]=we.sign(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:a}}},{key:"getAccessToken",value:function(){return this._getAccessTokenPromiseHub.exec()}},{key:"authorize",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getAccessToken();case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request((0,f.default)((0,f.default)({},this.setupRequest(t)),{},{timeout:e.timeout}))}},{key:"getOSSUploadOptionsFromPath",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFileToOSS",value:function(e){var t=this,n=e.url,a=e.formData,r=e.name,i=e.filePath,o=e.fileType,s=e.onUploadProgress;return new Promise((function(e,u){var c=t.adapter.uploadFile({url:n,formData:a,name:r,filePath:i,fileType:o,header:{"X-OSS-server-side-encrpytion":"AES256"},success:function(t){t&&t.statusCode<400?e(t):u(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){u(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"reportOSSUpload",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFile",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,a,r,i,o,s,u,c,d,f,p,h,g,v,m,y,b,w,_,x,k,S;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,a=t.cloudPath,r=t.fileType,i=void 0===r?"image":r,o=t.cloudPathAsRealPath,s=void 0!==o&&o,u=t.onUploadProgress,c=t.config,"string"===E(a)){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(a=a.trim()){e.next=5;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(a)){e.next=7;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:if(d=c&&c.envType||this.config.envType,!(s&&("/"!==a[0]&&(a="/"+a),a.indexOf("\\")>-1))){e.next=10;break}throw new le({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});case 10:return e.next=12,this.getOSSUploadOptionsFromPath({env:d,filename:s?a.split("/").pop():a,fileId:s?a:void 0});case 12:return f=e.sent.result,p="https://"+f.cdnDomain+"/"+f.ossPath,h=f.securityToken,g=f.accessKeyId,v=f.signature,m=f.host,y=f.ossPath,b=f.id,w=f.policy,_=f.ossCallbackUrl,x={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:g,Signature:v,host:m,id:b,key:y,policy:w,success_action_status:200},h&&(x["x-oss-security-token"]=h),_&&(k=JSON.stringify({callbackUrl:_,callbackBody:JSON.stringify({fileId:b,spaceId:this.config.spaceId}),callbackBodyType:"application/json"}),x.callback=we.toBase64(k)),S={url:"https://"+f.host,formData:x,fileName:"file",name:"file",filePath:n,fileType:i},e.next=27,this.uploadFileToOSS(Object.assign({},S,{onUploadProgress:u}));case 27:if(!_){e.next=29;break}return e.abrupt("return",{success:!0,filePath:n,fileID:p});case 29:return e.next=31,this.reportOSSUpload({id:b});case 31:if(!e.sent.success){e.next=33;break}return e.abrupt("return",{success:!0,filePath:n,fileID:p});case 33:throw new le({code:"UPLOAD_FAILED",message:"文件上传失败"});case 34:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.fileList;return new Promise((function(t,a){Array.isArray(n)&&0!==n.length||a(new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),e.getFileInfo({fileList:n}).then((function(e){t({fileList:n.map((function(t,n){var a=e.fileList[n];return{fileID:t,tempFileURL:a&&a.url||t}}))})}))}))}},{key:"getFileInfo",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,a,r=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=r.length>0&&void 0!==r[0]?r[0]:{},n=t.fileList,Array.isArray(n)&&0!==n.length){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});case 3:return a={method:"serverless.file.resource.info",params:JSON.stringify({id:n.map((function(e){return e.split("?")[0]})).join(",")})},e.next=6,this.request(this.setupRequest(a));case 6:return e.t0=e.sent.result,e.abrupt("return",{fileList:e.t0});case 8:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),xe={init:function(e){var t=new _e(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},ke="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";!function(e){e.local="local",e.none="none",e.session="session"}(be||(be={}));var Se,Te=function(){},Ie=b((function(e,t){var n;e.exports=(n=_,function(e){var t=n,a=t.lib,r=a.WordArray,i=a.Hasher,o=t.algo,s=[],u=[];!function(){function t(t){for(var n=e.sqrt(t),a=2;a<=n;a++)if(!(t%a))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var a=2,r=0;r<64;)t(a)&&(r<8&&(s[r]=n(e.pow(a,.5))),u[r]=n(e.pow(a,1/3)),r++),a++}();var c=[],l=o.SHA256=i.extend({_doReset:function(){this._hash=new r.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,a=n[0],r=n[1],i=n[2],o=n[3],s=n[4],l=n[5],d=n[6],f=n[7],p=0;p<64;p++){if(p<16)c[p]=0|e[t+p];else{var h=c[p-15],g=(h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3,v=c[p-2],m=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;c[p]=g+c[p-7]+m+c[p-16]}var y=a&r^a&i^r&i,b=(a<<30|a>>>2)^(a<<19|a>>>13)^(a<<10|a>>>22),w=f+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&d)+u[p]+c[p];f=d,d=l,l=s,s=o+w|0,o=i,i=r,r=a,a=w+(b+y)|0}n[0]=n[0]+a|0,n[1]=n[1]+r|0,n[2]=n[2]+i|0,n[3]=n[3]+o|0,n[4]=n[4]+s|0,n[5]=n[5]+l|0,n[6]=n[6]+d|0,n[7]=n[7]+f|0},_doFinalize:function(){var t=this._data,n=t.words,a=8*this._nDataBytes,r=8*t.sigBytes;return n[r>>>5]|=128<<24-r%32,n[14+(r+64>>>9<<4)]=e.floor(a/4294967296),n[15+(r+64>>>9<<4)]=a,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=i._createHelper(l),t.HmacSHA256=i._createHmacHelper(l)}(Math),n.SHA256)})),Pe=Ie,Oe=b((function(e,t){e.exports=_.HmacSHA256})),Ae=function(){var e;if(!Promise){e=function(){},e.promise={};var t=function(){throw new le({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var n=new Promise((function(t,n){e=function(e,a){return e?n(e):t(a)}}));return e.promise=n,e};function Ce(e){return void 0===e}function Ee(e){return"[object Null]"===Object.prototype.toString.call(e)}function Re(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function De(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:32,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n="",a=0;a<e;a++)n+=t.charAt(Math.floor(62*Math.random()));return n}!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Se||(Se={}));var Le={adapter:null,runtime:void 0},Ne=["anonymousUuidKey"],Ue=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){var e;return(0,v.default)(this,n),e=t.call(this),Le.adapter.root.tcbObject||(Le.adapter.root.tcbObject={}),e}return(0,m.default)(n,[{key:"setItem",value:function(e,t){Le.adapter.root.tcbObject[e]=t}},{key:"getItem",value:function(e){return Le.adapter.root.tcbObject[e]}},{key:"removeItem",value:function(e){delete Le.adapter.root.tcbObject[e]}},{key:"clear",value:function(){delete Le.adapter.root.tcbObject}}]),n}(Te);function Me(e,t){switch(e){case"local":return t.localStorage||new Ue;case"none":return new Ue;default:return t.sessionStorage||new Ue}}var Fe=function(){function e(t){if((0,v.default)(this,e),!this._storage){this._persistence=Le.adapter.primaryStorage||t.persistence,this._storage=Me(this._persistence,Le.adapter);var n="access_token_".concat(t.env),a="access_token_expire_".concat(t.env),r="refresh_token_".concat(t.env),i="anonymous_uuid_".concat(t.env),o="login_type_".concat(t.env),s="token_type_".concat(t.env),u="user_info_".concat(t.env);this.keys={accessTokenKey:n,accessTokenExpireKey:a,refreshTokenKey:r,anonymousUuidKey:i,loginTypeKey:o,userInfoKey:u,deviceIdKey:"device_id",tokenTypeKey:s}}}return(0,m.default)(e,[{key:"updatePersistence",value:function(e){if(e!==this._persistence){var t="local"===this._persistence;this._persistence=e;var n=Me(e,Le.adapter);for(var a in this.keys){var r=this.keys[a];if(!t||!Ne.includes(a)){var i=this._storage.getItem(r);Ce(i)||Ee(i)||(n.setItem(r,i),this._storage.removeItem(r))}}this._storage=n}}},{key:"setStore",value:function(e,t,n){if(this._storage){var a={version:n||"localCachev1",content:t},r=JSON.stringify(a);try{this._storage.setItem(e,r)}catch(e){throw e}}}},{key:"getStore",value:function(e,t){try{if(!this._storage)return}catch(e){return""}t=t||"localCachev1";var n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}},{key:"removeStore",value:function(e){this._storage.removeItem(e)}}]),e}(),je={},qe={};function Be(e){return je[e]}var Ke=(0,m.default)((function e(t,n){(0,v.default)(this,e),this.data=n||null,this.name=t})),ze=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(e,a){var r;return(0,v.default)(this,n),r=t.call(this,"error",{error:e,data:a}),r.error=e,r}return(0,m.default)(n)}(Ke),$e=new(function(){function e(){(0,v.default)(this,e),this._listeners={}}return(0,m.default)(e,[{key:"on",value:function(e,t){return function(e,t,n){n[e]=n[e]||[],n[e].push(t)}(e,t,this._listeners),this}},{key:"off",value:function(e,t){return function(e,t,n){if(n&&n[e]){var a=n[e].indexOf(t);-1!==a&&n[e].splice(a,1)}}(e,t,this._listeners),this}},{key:"fire",value:function(e,t){if(e instanceof ze)return a.error(e.error),this;var n="string"==typeof e?new Ke(e,t||{}):e,r=n.name;if(this._listens(r)){n.target=this;var i,o=this._listeners[r]?(0,u.default)(this._listeners[r]):[],s=(0,c.default)(o);try{for(s.s();!(i=s.n()).done;){var l=i.value;l.call(this,n)}}catch(d){s.e(d)}finally{s.f()}}return this}},{key:"_listens",value:function(e){return this._listeners[e]&&this._listeners[e].length>0}}]),e}());function He(e,t){$e.on(e,t)}function We(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};$e.fire(e,t)}function Ve(e,t){$e.off(e,t)}var Je,Ge="loginStateChanged",Qe="loginStateExpire",Ye="loginTypeChanged",Xe="anonymousConverted",Ze="refreshAccessToken";!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}(Je||(Je={}));var et=function(){function e(){(0,v.default)(this,e),this._fnPromiseMap=new Map}return(0,m.default)(e,[{key:"run",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var a,r=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=this._fnPromiseMap.get(t),e.abrupt("return",(a||(a=new Promise(function(){var e=(0,d.default)((0,l.default)().mark((function e(a,i){var o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,r._runIdlePromise();case 3:return o=n(),e.t0=a,e.next=7,o;case 7:e.t1=e.sent,(0,e.t0)(e.t1),e.next=14;break;case 11:e.prev=11,e.t2=e["catch"](0),i(e.t2);case 14:return e.prev=14,r._fnPromiseMap.delete(t),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[0,11,14,17]])})));return function(t,n){return e.apply(this,arguments)}}()),this._fnPromiseMap.set(t,a)),a));case 2:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_runIdlePromise",value:function(){return Promise.resolve()}}]),e}(),tt=function(){function e(t){(0,v.default)(this,e),this._singlePromise=new et,this._cache=Be(t.env),this._baseURL="https://".concat(t.env,".ap-shanghai.tcb-api.tencentcloudapi.com"),this._reqClass=new Le.adapter.reqClass({timeout:t.timeout,timeoutMsg:"请求在".concat(t.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]})}return(0,m.default)(e,[{key:"_getDeviceId",value:function(){if(this._deviceID)return this._deviceID;var e=this._cache.keys.deviceIdKey,t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=De(),this._cache.setStore(e,t)),this._deviceID=t,t}},{key:"_request",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var a,r,i,o,s,u=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=u.length>2&&void 0!==u[2]?u[2]:{},r={"x-request-id":De(),"x-device-id":this._getDeviceId()},!a.withAccessToken){e.next=9;break}return i=this._cache.keys.tokenTypeKey,e.next=6,this.getAccessToken();case 6:o=e.sent,s=this._cache.getStore(i),r.authorization="".concat(s," ").concat(o);case 9:return e.abrupt("return",this._reqClass["get"===a.method?"get":"post"]({url:"".concat(this._baseURL).concat(t),data:n,headers:r}));case 10:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_fetchAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,a,r,i,o,s,u,c,f,p=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.loginTypeKey,a=t.accessTokenKey,r=t.accessTokenExpireKey,i=t.tokenTypeKey,o=this._cache.getStore(n),!o||o===Je.ANONYMOUS){e.next=3;break}throw new le({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});case 3:return e.next=5,this._singlePromise.run("fetchAccessToken",(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,p._request("/auth/v1/signin/anonymously",{},{method:"post"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)}))));case 5:return s=e.sent,u=s.access_token,c=s.expires_in,f=s.token_type,e.abrupt("return",(this._cache.setStore(i,f),this._cache.setStore(a,u),this._cache.setStore(r,Date.now()+1e3*c),u));case 10:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"isAccessTokenExpired",value:function(e,t){var n=!0;return e&&t&&(n=t<Date.now()),n}},{key:"getAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,a,r,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,a=t.accessTokenExpireKey,r=this._cache.getStore(n),i=this._cache.getStore(a),e.abrupt("return",this.isAccessTokenExpired(r,i)?this._fetchAccessToken():r);case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,a,r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,a=t.accessTokenExpireKey,r=t.loginTypeKey,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(a),this._cache.setStore(r,Je.ANONYMOUS),this.getAccessToken()));case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getUserInfo",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._singlePromise.run("getUserInfo",(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)})))));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),nt=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],at={"X-SDK-Version":"1.3.5"};function rt(e,t,n){var a=e[t];e[t]=function(t){var r={},i={};n.forEach((function(n){var a=n.call(e,t),o=a.data,s=a.headers;Object.assign(r,o),Object.assign(i,s)}));var o=t.data;return o&&function(){var e;if(e=o,"[object FormData]"!==Object.prototype.toString.call(e))t.data=(0,f.default)((0,f.default)({},o),r);else for(var n in r)o.append(n,r[n])}(),t.headers=(0,f.default)((0,f.default)({},t.headers||{}),i),a.call(e,t)}}function it(){var e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:(0,f.default)((0,f.default)({},at),{},{"x-seqid":e})}}var ot=function(){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,v.default)(this,e),this.config=n,this._reqClass=new Le.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:"请求在".concat(this.config.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]}),this._cache=Be(this.config.env),this._localCache=(t=this.config.env,qe[t]),this.oauth=new tt(this.config),rt(this._reqClass,"post",[it]),rt(this._reqClass,"upload",[it]),rt(this._reqClass,"download",[it])}return(0,m.default)(e,[{key:"post",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.post(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"upload",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.upload(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"download",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.download(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken()),e.prev=1,e.next=4,this._refreshAccessTokenPromise;case 4:t=e.sent,e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](1),n=e.t0;case 10:if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,!n){e.next=12;break}throw n;case 12:return e.abrupt("return",t);case 13:case"end":return e.stop()}}),e,this,[[1,7]])})));return function(){return e.apply(this,arguments)}}()},{key:"_refreshAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,a,r,i,o,s,u,c,d,f,p,h;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,a=t.accessTokenExpireKey,r=t.refreshTokenKey,i=t.loginTypeKey,o=t.anonymousUuidKey,this._cache.removeStore(n),this._cache.removeStore(a),s=this._cache.getStore(r),s){e.next=5;break}throw new le({message:"未登录CloudBase"});case 5:return u={refresh_token:s},e.next=8,this.request("auth.fetchAccessTokenWithRefreshToken",u);case 8:if(c=e.sent,!c.data.code){e.next=21;break}if(d=c.data.code,"SIGN_PARAM_INVALID"!==d&&"REFRESH_TOKEN_EXPIRED"!==d&&"INVALID_REFRESH_TOKEN"!==d){e.next=20;break}if(this._cache.getStore(i)!==Je.ANONYMOUS||"INVALID_REFRESH_TOKEN"!==d){e.next=19;break}return f=this._cache.getStore(o),p=this._cache.getStore(r),e.next=17,this.send("auth.signInAnonymously",{anonymous_uuid:f,refresh_token:p});case 17:return h=e.sent,e.abrupt("return",(this.setRefreshToken(h.refresh_token),this._refreshAccessToken()));case 19:We(Qe),this._cache.removeStore(r);case 20:throw new le({code:c.data.code,message:"刷新access token失败：".concat(c.data.code)});case 21:if(!c.data.access_token){e.next=23;break}return e.abrupt("return",(We(Ze),this._cache.setStore(n,c.data.access_token),this._cache.setStore(a,c.data.access_token_expire+Date.now()),{accessToken:c.data.access_token,accessTokenExpire:c.data.access_token_expire}));case 23:c.data.refresh_token&&(this._cache.removeStore(r),this._cache.setStore(r,c.data.refresh_token),this._refreshAccessToken());case 24:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,a,r,i,o,s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,a=t.accessTokenExpireKey,r=t.refreshTokenKey,this._cache.getStore(r)){e.next=3;break}throw new le({message:"refresh token不存在，登录状态异常"});case 3:if(i=this._cache.getStore(n),o=this._cache.getStore(a),s=!0,e.t0=this._shouldRefreshAccessTokenHook,!e.t0){e.next=9;break}return e.next=8,this._shouldRefreshAccessTokenHook(i,o);case 8:e.t0=!e.sent;case 9:if(e.t1=e.t0,!e.t1){e.next=12;break}s=!1;case 12:return e.abrupt("return",(!i||!o||o<Date.now())&&s?this.refreshAccessToken():{accessToken:i,accessTokenExpire:o});case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"request",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n,a){var r,i,o,s,u,c,d,p,h,g,v,m,y,b,w;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r="x-tcb-trace_".concat(this.config.env),i="application/x-www-form-urlencoded",o=(0,f.default)({action:t,env:this.config.env,dataVersion:"2019-08-16"},n),e.t0=-1===nt.indexOf(t),!e.t0){e.next=9;break}return this._cache.keys,e.next=8,this.oauth.getAccessToken();case 8:o.access_token=e.sent;case 9:if("storage.uploadFile"!==t){e.next=15;break}for(u in s=new FormData,s)s.hasOwnProperty(u)&&void 0!==s[u]&&s.append(u,o[u]);i="multipart/form-data",e.next=17;break;case 15:for(c in i="application/json",s={},o)void 0!==o[c]&&(s[c]=o[c]);case 17:return d={headers:{"content-type":i}},a&&a.timeout&&(d.timeout=a.timeout),a&&a.onUploadProgress&&(d.onUploadProgress=a.onUploadProgress),p=this._localCache.getStore(r),p&&(d.headers["X-TCB-Trace"]=p),h=n.parse,g=n.inQuery,v=n.search,m={env:this.config.env},h&&(m.parse=!0),g&&(m=(0,f.default)((0,f.default)({},g),m)),y=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=/\?/.test(t),r="";for(var i in n)""===r?!a&&(t+="?"):r+="&",r+="".concat(i,"=").concat(encodeURIComponent(n[i]));return/^http(s)?\:\/\//.test(t+=r)?t:"".concat(e).concat(t)}(ke,"//tcb-api.tencentcloudapi.com/web",m),v&&(y+=v),e.next=28,this.post((0,f.default)({url:y,data:s},d));case 28:if(b=e.sent,w=b.header&&b.header["x-tcb-trace"],w&&this._localCache.setStore(r,w),(200===Number(b.status)||200===Number(b.statusCode))&&b.data){e.next=32;break}throw new le({code:"NETWORK_ERROR",message:"network request error"});case 32:return e.abrupt("return",b);case 33:case"end":return e.stop()}}),e,this)})));return function(t,n,a){return e.apply(this,arguments)}}()},{key:"send",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,a,r,i,o=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=o.length>1&&void 0!==o[1]?o[1]:{},a=o.length>2&&void 0!==o[2]?o[2]:{},e.next=4,this.request(t,n,(0,f.default)((0,f.default)({},a),{},{onUploadProgress:n.onUploadProgress}));case 4:if(r=e.sent,"ACCESS_TOKEN_DISABLED"!==r.data.code&&"ACCESS_TOKEN_EXPIRED"!==r.data.code||-1!==nt.indexOf(t)){e.next=14;break}return e.next=8,this.oauth.refreshAccessToken();case 8:return e.next=10,this.request(t,n,(0,f.default)((0,f.default)({},a),{},{onUploadProgress:n.onUploadProgress}));case 10:if(i=e.sent,!i.data.code){e.next=13;break}throw new le({code:i.data.code,message:Re(i.data.message)});case 13:return e.abrupt("return",i.data);case 14:if(!r.data.code){e.next=16;break}throw new le({code:r.data.code,message:Re(r.data.message)});case 16:return e.abrupt("return",r.data);case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,a=t.accessTokenExpireKey,r=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(a),this._cache.setStore(r,e)}}]),e}(),st={};function ut(e){return st[e]}var ct=function(){function e(t){(0,v.default)(this,e),this.config=t,this._cache=Be(t.env),this._request=ut(t.env)}return(0,m.default)(e,[{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,a=t.accessTokenExpireKey,r=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(a),this._cache.setStore(r,e)}},{key:"setAccessToken",value:function(e,t){var n=this._cache.keys,a=n.accessTokenKey,r=n.accessTokenExpireKey;this._cache.setStore(a,e),this._cache.setStore(r,t)}},{key:"refreshUserInfo",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getUserInfo",{});case 2:return t=e.sent,n=t.data,e.abrupt("return",(this.setLocalUserInfo(n),n));case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e)}}]),e}(),lt=function(){function e(t){if((0,v.default)(this,e),!t)throw new le({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=t,this._cache=Be(this._envId),this._request=ut(this._envId),this.setUserInfo()}return(0,m.default)(e,[{key:"linkWithTicket",value:function(e){if("string"!=typeof e)throw new le({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}},{key:"linkWithRedirect",value:function(e){e.signInWithRedirect()}},{key:"updatePassword",value:function(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}},{key:"updateEmail",value:function(e){return this._request.send("auth.updateEmail",{newEmail:e})}},{key:"updateUsername",value:function(e){if("string"!=typeof e)throw new le({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}},{key:"getLinkedUidList",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,a,r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getLinkedUidList",{});case 2:return t=e.sent,n=t.data,a=!1,r=n.users,e.abrupt("return",(r.forEach((function(e){e.wxOpenId&&e.wxPublicId&&(a=!0)})),{users:r,hasPrimaryUid:a}));case 7:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setPrimaryUid",value:function(e){return this._request.send("auth.setPrimaryUid",{uid:e})}},{key:"unlink",value:function(e){return this._request.send("auth.unlink",{platform:e})}},{key:"update",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,a,r,i,o,s,u,c;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.nickName,a=t.gender,r=t.avatarUrl,i=t.province,o=t.country,s=t.city,e.next=8,this._request.send("auth.updateUserInfo",{nickName:n,gender:a,avatarUrl:r,province:i,country:o,city:s});case 8:u=e.sent,c=u.data,this.setLocalUserInfo(c);case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refresh",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.oauth.getUserInfo();case 2:return t=e.sent,e.abrupt("return",(this.setLocalUserInfo(t),t));case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setUserInfo",value:function(){var e=this,t=this._cache.keys.userInfoKey,n=this._cache.getStore(t);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((function(t){e[t]=n[t]})),this.location={country:n.country,province:n.province,city:n.city}}},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e),this.setUserInfo()}}]),e}(),dt=function(){function e(t){if((0,v.default)(this,e),!t)throw new le({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=Be(t);var n=this._cache.keys,a=n.refreshTokenKey,r=n.accessTokenKey,i=n.accessTokenExpireKey,o=this._cache.getStore(a),s=this._cache.getStore(r),u=this._cache.getStore(i);this.credential={refreshToken:o,accessToken:s,accessTokenExpire:u},this.user=new lt(t)}return(0,m.default)(e,[{key:"isAnonymousAuth",get:function(){return this.loginType===Je.ANONYMOUS}},{key:"isCustomAuth",get:function(){return this.loginType===Je.CUSTOM}},{key:"isWeixinAuth",get:function(){return this.loginType===Je.WECHAT||this.loginType===Je.WECHAT_OPEN||this.loginType===Je.WECHAT_PUBLIC}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}]),e}(),ft=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._cache.updatePersistence("local"),e.next=3,this._request.oauth.getAccessToken();case 3:return We(Ge),We(Ye,{env:this.config.env,loginType:Je.ANONYMOUS,persistence:"local"}),t=new dt(this.config.env),e.next=8,t.user.refresh();case 8:return e.abrupt("return",t);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,a,r,i,o,s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=this._cache.keys,a=n.anonymousUuidKey,r=n.refreshTokenKey,i=this._cache.getStore(a),o=this._cache.getStore(r),e.next=7,this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:i,refresh_token:o,ticket:t});case 7:if(s=e.sent,!s.refresh_token){e.next=16;break}return this._clearAnonymousUUID(),this.setRefreshToken(s.refresh_token),e.next=13,this._request.refreshAccessToken();case 13:return We(Xe,{env:this.config.env}),We(Ye,{loginType:Je.CUSTOM,persistence:"local"}),e.abrupt("return",{credential:{refreshToken:s.refresh_token}});case 16:throw new le({message:"匿名转化失败"});case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"_setAnonymousUUID",value:function(e){var t=this._cache.keys,n=t.anonymousUuidKey,a=t.loginTypeKey;this._cache.removeStore(n),this._cache.setStore(n,e),this._cache.setStore(a,Je.ANONYMOUS)}},{key:"_clearAnonymousUUID",value:function(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}]),n}(ct),pt=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,a;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"ticket must be a string"});case 2:return n=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signInWithTicket",{ticket:t,refresh_token:this._cache.getStore(n)||""});case 5:if(a=e.sent,!a.refresh_token){e.next=15;break}return this.setRefreshToken(a.refresh_token),e.next=10,this._request.refreshAccessToken();case 10:return We(Ge),We(Ye,{env:this.config.env,loginType:Je.CUSTOM,persistence:this.config.persistence}),e.next=14,this.refreshUserInfo();case 14:return e.abrupt("return",new dt(this.config.env));case 15:throw new le({message:"自定义登录失败"});case 16:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),n}(ct),ht=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var a,r,i,o,s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"email must be a string"});case 2:return a=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signIn",{loginType:"EMAIL",email:t,password:n,refresh_token:this._cache.getStore(a)||""});case 5:if(r=e.sent,i=r.refresh_token,o=r.access_token,s=r.access_token_expire,!i){e.next=22;break}if(this.setRefreshToken(i),!o||!s){e.next=15;break}this.setAccessToken(o,s),e.next=17;break;case 15:return e.next=17,this._request.refreshAccessToken();case 17:return e.next=19,this.refreshUserInfo();case 19:return We(Ge),We(Ye,{env:this.config.env,loginType:Je.EMAIL,persistence:this.config.persistence}),e.abrupt("return",new dt(this.config.env));case 22:throw r.code?new le({code:r.code,message:"邮箱登录失败: ".concat(r.message)}):new le({message:"邮箱登录失败"});case 23:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"activate",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.activateEndUserMail",{token:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"resetPasswordWithToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.resetPasswordWithToken",{token:t,newPassword:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(ct),gt=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var r,i,o,s,u;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"username must be a string"});case 2:return"string"!=typeof n&&(n="",a.warn("password is empty")),r=this._cache.keys.refreshTokenKey,e.next=6,this._request.send("auth.signIn",{loginType:Je.USERNAME,username:t,password:n,refresh_token:this._cache.getStore(r)||""});case 6:if(i=e.sent,o=i.refresh_token,s=i.access_token_expire,u=i.access_token,!o){e.next=23;break}if(this.setRefreshToken(o),!u||!s){e.next=16;break}this.setAccessToken(u,s),e.next=18;break;case 16:return e.next=18,this._request.refreshAccessToken();case 18:return e.next=20,this.refreshUserInfo();case 20:return We(Ge),We(Ye,{env:this.config.env,loginType:Je.USERNAME,persistence:this.config.persistence}),e.abrupt("return",new dt(this.config.env));case 23:throw i.code?new le({code:i.code,message:"用户名密码登录失败: ".concat(i.message)}):new le({message:"用户名密码登录失败"});case 24:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(ct),vt=function(){function e(t){(0,v.default)(this,e),this.config=t,this._cache=Be(t.env),this._request=ut(t.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),He(Ye,this._onLoginTypeChanged)}return(0,m.default)(e,[{key:"currentUser",get:function(){var e=this.hasLoginState();return e&&e.user||null}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}},{key:"anonymousAuthProvider",value:function(){return new ft(this.config)}},{key:"customAuthProvider",value:function(){return new pt(this.config)}},{key:"emailAuthProvider",value:function(){return new ht(this.config)}},{key:"usernameAuthProvider",value:function(){return new gt(this.config)}},{key:"signInAnonymously",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new ft(this.config).signIn());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signInWithEmailAndPassword",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new ht(this.config).signIn(t,n));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"signInWithUsernameAndPassword",value:function(e,t){return new gt(this.config).signIn(e,t)}},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._anonymousAuthProvider||(this._anonymousAuthProvider=new ft(this.config)),He(Xe,this._onAnonymousConverted),e.next=3,this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(t);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"signOut",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){var t,n,a,r,i,o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.loginType!==Je.ANONYMOUS){e.next=2;break}throw new le({message:"匿名用户不支持登出操作"});case 2:if(t=this._cache.keys,n=t.refreshTokenKey,a=t.accessTokenKey,r=t.accessTokenExpireKey,i=this._cache.getStore(n),i){e.next=5;break}return e.abrupt("return");case 5:return e.next=7,this._request.send("auth.logout",{refresh_token:i});case 7:return o=e.sent,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(a),this._cache.removeStore(r),We(Ge),We(Ye,{env:this.config.env,loginType:Je.NULL,persistence:this.config.persistence}),o));case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signUpWithEmailAndPassword",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.signUpWithEmailAndPassword",{email:t,password:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"sendPasswordResetEmail",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.sendPasswordResetEmail",{email:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"onLoginStateChanged",value:function(e){var t=this;He(Ge,(function(){var n=t.hasLoginState();e.call(t,n)}));var n=this.hasLoginState();e.call(this,n)}},{key:"onLoginStateExpired",value:function(e){He(Qe,e.bind(this))}},{key:"onAccessTokenRefreshed",value:function(e){He(Ze,e.bind(this))}},{key:"onAnonymousConverted",value:function(e){He(Xe,e.bind(this))}},{key:"onLoginTypeChanged",value:function(e){var t=this;He(Ye,(function(){var n=t.hasLoginState();e.call(t,n)}))}},{key:"getAccessToken",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.getAccessToken();case 2:return e.t0=e.sent.accessToken,e.t1=this.config.env,e.abrupt("return",{accessToken:e.t0,env:e.t1});case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"hasLoginState",value:function(){var e=this._cache.keys,t=e.accessTokenKey,n=e.accessTokenExpireKey,a=this._cache.getStore(t),r=this._cache.getStore(n);return this._request.oauth.isAccessTokenExpired(a,r)?null:new dt(this.config.env)}},{key:"isUsernameRegistered",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,a;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"username must be a string"});case 2:return e.next=4,this._request.send("auth.isUsernameRegistered",{username:t});case 4:return n=e.sent,a=n.data,e.abrupt("return",a&&a.isRegistered);case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getLoginState",value:function(){return Promise.resolve(this.hasLoginState())}},{key:"signInWithTicket",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new pt(this.config).signIn(t));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"shouldRefreshAccessToken",value:function(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}},{key:"getUserInfo",value:function(){return this._request.send("auth.getUserInfo",{}).then((function(e){return e.code?e:(0,f.default)((0,f.default)({},e.data),{},{requestId:e.seqId})}))}},{key:"getAuthHeader",value:function(){var e=this._cache.keys,t=e.refreshTokenKey,n=e.accessTokenKey,a=this._cache.getStore(t);return{"x-cloudbase-credentials":this._cache.getStore(n)+"/@@/"+a}}},{key:"_onAnonymousConverted",value:function(e){var t=e.data.env;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}},{key:"_onLoginTypeChanged",value:function(e){var t=e.data,n=t.loginType,a=t.persistence,r=t.env;r===this.config.env&&(this._cache.updatePersistence(a),this._cache.setStore(this._cache.keys.loginTypeKey,n))}}]),e}(),mt=function(e,t){t=t||Ae();var n=ut(this.config.env),a=e.cloudPath,r=e.filePath,i=e.onUploadProgress,o=e.fileType,s=void 0===o?"image":o;return n.send("storage.getUploadMetadata",{path:a}).then((function(e){var o=e.data,u=o.url,c=o.authorization,l=o.token,d=o.fileId,f=o.cosFileId,p=e.requestId,h={key:a,signature:c,"x-cos-meta-fileid":f,success_action_status:"201","x-cos-security-token":l};n.upload({url:u,data:h,file:r,name:a,fileType:s,onUploadProgress:i}).then((function(e){201===e.statusCode?t(null,{fileID:d,requestId:p}):t(new le({code:"STORAGE_REQUEST_FAIL",message:"STORAGE_REQUEST_FAIL: ".concat(e.data)}))})).catch((function(e){t(e)}))})).catch((function(e){t(e)})),t.promise},yt=function(e,t){t=t||Ae();var n=ut(this.config.env),a=e.cloudPath;return n.send("storage.getUploadMetadata",{path:a}).then((function(e){t(null,e)})).catch((function(e){t(e)})),t.promise},bt=function(e,t){var n=e.fileList;if(t=t||Ae(),!n||!Array.isArray(n))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};var a,r=(0,c.default)(n);try{for(r.s();!(a=r.n()).done;){var i=a.value;if(!i||"string"!=typeof i)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"}}}catch(s){r.e(s)}finally{r.f()}var o={fileid_list:n};return ut(this.config.env).send("storage.batchDeleteFile",o).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},wt=function(e,t){var n=e.fileList;t=t||Ae(),n&&Array.isArray(n)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});var a,r=[],i=(0,c.default)(n);try{for(i.s();!(a=i.n()).done;){var o=a.value;"object"==(0,s.default)(o)?(o.hasOwnProperty("fileID")&&o.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),r.push({fileid:o.fileID,max_age:o.maxAge})):"string"==typeof o?r.push({fileid:o}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"})}}catch(l){i.e(l)}finally{i.f()}var u={file_list:r};return ut(this.config.env).send("storage.batchGetDownloadUrl",u).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},_t=function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var a,r,i,o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.fileID,e.next=3,wt.call(this,{fileList:[{fileID:a,maxAge:600}]});case 3:if(r=e.sent.fileList[0],"SUCCESS"===r.code){e.next=6;break}return e.abrupt("return",n?n(r):new Promise((function(e){e(r)})));case 6:if(i=ut(this.config.env),o=r.download_url,o=encodeURI(o),n){e.next=10;break}return e.abrupt("return",i.download({url:o}));case 10:return e.t0=n,e.next=13,i.download({url:o});case 13:e.t1=e.sent,(0,e.t0)(e.t1);case 15:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),xt=function(e,t){var n,a=e.name,r=e.data,i=e.query,o=e.parse,s=e.search,u=e.timeout,c=t||Ae();try{n=r?JSON.stringify(r):""}catch(a){return Promise.reject(a)}if(!a)return Promise.reject(new le({code:"PARAM_ERROR",message:"函数名不能为空"}));var l={inQuery:i,parse:o,search:s,function_name:a,request_data:n};return ut(this.config.env).send("functions.invokeFunction",l,{timeout:u}).then((function(e){if(e.code)c(null,e);else{var t=e.data.response_data;if(o)c(null,{result:t,requestId:e.requestId});else try{t=JSON.parse(e.data.response_data),c(null,{result:t,requestId:e.requestId})}catch(e){c(new le({message:"response data must be json"}))}}return c.promise})).catch((function(e){c(e)})),c.promise},kt={timeout:15e3,persistence:"session"},St={},Tt=function(){function e(t){(0,v.default)(this,e),this.config=t||this.config,this.authObj=void 0}return(0,m.default)(e,[{key:"init",value:function(t){switch(Le.adapter||(this.requestClient=new Le.adapter.reqClass({timeout:t.timeout||5e3,timeoutMsg:"请求在".concat((t.timeout||5e3)/1e3,"s内未完成，已中断")})),this.config=(0,f.default)((0,f.default)({},kt),t),!0){case this.config.timeout>6e5:a.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:a.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new e(this.config)}},{key:"auth",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.persistence;if(this.authObj)return this.authObj;var n,a=t||Le.adapter.primaryStorage||kt.persistence;return a!==this.config.persistence&&(this.config.persistence=a),function(e){var t=e.env;je[t]=new Fe(e),qe[t]=new Fe((0,f.default)((0,f.default)({},e),{},{persistence:"local"}))}(this.config),n=this.config,st[n.env]=new ot(n),this.authObj=new vt(this.config),this.authObj}},{key:"on",value:function(e,t){return He.apply(this,[e,t])}},{key:"off",value:function(e,t){return Ve.apply(this,[e,t])}},{key:"callFunction",value:function(e,t){return xt.apply(this,[e,t])}},{key:"deleteFile",value:function(e,t){return bt.apply(this,[e,t])}},{key:"getTempFileURL",value:function(e,t){return wt.apply(this,[e,t])}},{key:"downloadFile",value:function(e,t){return _t.apply(this,[e,t])}},{key:"uploadFile",value:function(e,t){return mt.apply(this,[e,t])}},{key:"getUploadMetadata",value:function(e,t){return yt.apply(this,[e,t])}},{key:"registerExtension",value:function(e){St[e.name]=e}},{key:"invokeExtension",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t,n){var a;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=St[t],a){e.next=3;break}throw new le({message:"扩展".concat(t," 必须先注册")});case 3:return e.next=5,a.invoke(n,this);case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"useAdapters",value:function(e){var t=function(e){var t,n,a=(t=e,"[object Array]"===Object.prototype.toString.call(t)?e:[e]),r=(0,c.default)(a);try{for(r.s();!(n=r.n()).done;){var i=n.value,o=i.isMatch,s=i.genAdapter,u=i.runtime;if(o())return{adapter:s(),runtime:u}}}catch(l){r.e(l)}finally{r.f()}}(e)||{},n=t.adapter,a=t.runtime;n&&(Le.adapter=n),a&&(Le.runtime=a)}}]),e}(),It=new Tt;function Pt(e,t,n){void 0===n&&(n={});var a=/\?/.test(t),r="";for(var i in n)""===r?!a&&(t+="?"):r+="&",r+=i+"="+encodeURIComponent(n[i]);return/^http(s)?:\/\//.test(t+=r)?t:""+e+t}var Ot=function(){function e(){(0,v.default)(this,e)}return(0,m.default)(e,[{key:"get",value:function(e){var t=e.url,n=e.data,a=e.headers,r=e.timeout;return new Promise((function(e,i){pe.request({url:Pt("https:",t),data:n,method:"GET",header:a,timeout:r,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"post",value:function(e){var t=e.url,n=e.data,a=e.headers,r=e.timeout;return new Promise((function(e,i){pe.request({url:Pt("https:",t),data:n,method:"POST",header:a,timeout:r,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"upload",value:function(e){return new Promise((function(t,n){var a=e.url,r=e.file,i=e.data,o=e.headers,s=e.fileType,u=pe.uploadFile({url:Pt("https:",a),name:"file",formData:Object.assign({},i),filePath:r,fileType:s,header:o,success:function(e){var n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(n.statusCode=parseInt(i.success_action_status,10)),t(n)},fail:function(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&u&&"function"==typeof u.onProgressUpdate&&u.onProgressUpdate((function(t){e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}]),e}(),At={setItem:function(e,t){pe.setStorageSync(e,t)},getItem:function(e){return pe.getStorageSync(e)},removeItem:function(e){pe.removeStorageSync(e)},clear:function(){pe.clearStorageSync()}},Ct={genAdapter:function(){return{root:{},reqClass:Ot,localStorage:At,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};It.useAdapters(Ct);var Et=It,Rt=Et.init;Et.init=function(e){e.env=e.spaceId;var t=Rt.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;var n=t.auth;return t.auth=function(e){var t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((function(e){var n;t[e]=(n=t[e],function(e){e=e||{};var t=ce(e),a=t.success,r=t.fail,i=t.complete;if(!(a||r||i))return n.call(this,e);n.call(this,e).then((function(e){a&&a(e),i&&i(e)}),(function(e){r&&r(e),i&&i(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var Dt=Et;function Lt(e,t){return Nt.apply(this,arguments)}function Nt(){return Nt=(0,d.default)((0,l.default)().mark((function e(t,n){var a,r,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="http://".concat(t,":").concat(n,"/system/ping"),e.prev=1,e.next=4,i={url:a,timeout:500},new Promise((function(e,t){pe.request((0,f.default)((0,f.default)({},i),{},{success:function(t){e(t)},fail:function(e){t(e)}}))}));case 4:return r=e.sent,e.abrupt("return",!(!r.data||0!==r.data.code));case 8:return e.prev=8,e.t0=e["catch"](1),e.abrupt("return",!1);case 11:case"end":return e.stop()}}),e,null,[[1,8]])}))),Nt.apply(this,arguments)}function Ut(e,t){return Mt.apply(this,arguments)}function Mt(){return Mt=(0,d.default)((0,l.default)().mark((function e(t,n){var a,r,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:r=0;case 1:if(!(r<t.length)){e.next=11;break}return i=t[r],e.next=5,Lt(i,n);case 5:if(!e.sent){e.next=8;break}return a=i,e.abrupt("break",11);case 8:r++,e.next=1;break;case 11:return e.abrupt("return",{address:a,port:n});case 12:case"end":return e.stop()}}),e)}))),Mt.apply(this,arguments)}var Ft={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"},jt=function(){function e(t){if((0,v.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),!t.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},t),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=pe}return(0,m.default)(e,[{key:"request",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,a=this,r=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(r.length>1&&void 0!==r[1])||r[1],n=!1,!n){e.next=8;break}return e.next=5,this.setupLocalRequest(t);case 5:e.t0=e.sent,e.next=9;break;case 8:e.t0=this.setupRequest(t);case 9:return t=e.t0,e.abrupt("return",Promise.resolve().then((function(){return n?a.requestLocal(t):we.wrappedRequest(t,a.adapter.request)})));case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"requestLocal",value:function(e){var t=this;return new Promise((function(n,a){t.adapter.request(Object.assign(e,{complete:function(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){var t=e.data&&e.data.code||"SYS_ERR",r=e.data&&e.data.message||"request:fail";return a(new le({code:t,message:r}))}n({success:!0,result:e.data})}}))}))}},{key:"setupRequest",value:function(e){var t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=we.sign(t,this.config.clientSecret);var a=ye();n["x-client-info"]=encodeURIComponent(JSON.stringify(a));var r=he(),i=r.token;return n["x-client-token"]=i,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}},{key:"setupLocalRequest",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,a,r,i,o,s,u,c,d;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=ye(),a=he(),r=a.token,i=Object.assign({},t,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:n,token:r}),o=this.__dev__&&this.__dev__.debugInfo||{},s=o.address,u=o.servePort,e.next=9,Ut(s,u);case 9:return c=e.sent,d=c.address,e.abrupt("return",{url:"http://".concat(d,":").concat(u,"/").concat(Ft[t.method]),method:"POST",data:i,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))});case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}},{key:"getUploadFileOptions",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}},{key:"reportUploadFile",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}},{key:"uploadFile",value:function(e){var t,n=this,a=e.filePath,r=e.cloudPath,i=e.fileType,o=void 0===i?"image":i,s=e.onUploadProgress;if(!r)throw new le({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});return this.getUploadFileOptions({cloudPath:r}).then((function(e){var r=e.result,i=r.url,u=r.formData,c=r.name;return t=e.result.fileUrl,new Promise((function(e,t){var r=n.adapter.uploadFile({url:i,formData:u,name:c,filePath:a,fileType:o,success:function(n){n&&n.statusCode<400?e(n):t(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){t(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof s&&r&&"function"==typeof r.onProgressUpdate&&r.onProgressUpdate((function(e){s({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((function(){return n.reportUploadFile({cloudPath:r})})).then((function(e){return new Promise((function(n,r){e.success?n({success:!0,filePath:a,fileID:t}):r(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))}))}},{key:"deleteFile",value:function(e){var t=e.fileList,n={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:t})};return this.request(n).then((function(e){if(e.success)return e.result;throw new le({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList,n=e.maxAge;if(!Array.isArray(t)||0===t.length)throw new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});var a={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:t,maxAge:n})};return this.request(a).then((function(e){if(e.success)return{fileList:e.result.fileList.map((function(e){return{fileID:e.fileID,tempFileURL:e.tempFileURL}}))};throw new le({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}]),e}(),qt={init:function(e){var t=new jt(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Bt=b((function(e,t){e.exports=_.enc.Hex}));function Kt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function zt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.data,a=t.functionName,r=t.method,i=t.headers,s=t.signHeaderKeys,u=void 0===s?[]:s,c=t.config,l=String(Date.now()),d=Kt(),f=Object.assign({},i,{"x-from-app-id":c.spaceAppId,"x-from-env-id":c.spaceId,"x-to-env-id":c.spaceId,"x-from-instance-id":l,"x-from-function-name":a,"x-client-timestamp":l,"x-alipay-source":"client","x-request-id":d,"x-alipay-callid":d,"x-trace-id":d}),p=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(u),h=e.split("?")||[],g=(0,o.default)(h,2),v=g[0],m=void 0===v?"":v,y=g[1],b=void 0===y?"":y,w=function(e){var t="HMAC-SHA256",n=e.signedHeaders.join(";"),a=e.signedHeaders.map((function(t){return"".concat(t.toLowerCase(),":").concat(e.headers[t],"\n")})).join(""),r=Pe(e.body).toString(Bt),i="".concat(e.method.toUpperCase(),"\n").concat(e.path,"\n").concat(e.query,"\n").concat(a,"\n").concat(n,"\n").concat(r,"\n"),o=Pe(i).toString(Bt),s="".concat(t,"\n").concat(e.timestamp,"\n").concat(o,"\n"),u=Oe(s,e.secretKey).toString(Bt);return"".concat(t," Credential=").concat(e.secretId,", SignedHeaders=").concat(n,", Signature=").concat(u)}({path:m,query:b,method:r,headers:f,timestamp:l,body:JSON.stringify(n),secretId:c.accessKey,secretKey:c.secretKey,signedHeaders:p.sort()});return{url:"".concat(c.endpoint).concat(e),headers:Object.assign({},f,{Authorization:w})}}function $t(e){var t=e.url,n=e.data,a=e.method,r=void 0===a?"POST":a,i=e.headers,o=void 0===i?{}:i,u=e.timeout;return new Promise((function(e,a){pe.request({url:t,method:r,data:"object"==(0,s.default)(n)?JSON.stringify(n):n,header:o,dataType:"json",timeout:u,complete:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=o["x-trace-id"]||"";if(!t.statusCode||t.statusCode>=400){var r=t.data||{},i=r.message,s=r.errMsg,u=r.trace_id;return a(new le({code:"SYS_ERR",message:i||s||"request:fail",requestId:u||n}))}e({status:t.statusCode,data:t.data,headers:t.header,requestId:n})}})}))}function Ht(e,t){var n=e.path,a=e.data,r=e.method,i=void 0===r?"GET":r,o=zt(n,{functionName:"",data:a,method:i,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t}),s=o.url,u=o.headers;return $t({url:s,data:a,method:i,headers:u}).then((function(e){var t=e.data||{};if(!t.success)throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((function(e){throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Wt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new le({code:"INVALID_PARAM",message:"fileID不合法"});var r=t.substring(0,n),i=t.substring(n+1);return r!==this.config.spaceId&&a.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),i}function Vt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}var Jt=function(){function e(t){(0,v.default)(this,e),this.config=t}return(0,m.default)(e,[{key:"signedURL",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="/ws/function/".concat(e),a=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),r=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Kt(),timestamp:""+Date.now()}),i=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return r[e]?"".concat(e,"=").concat(r[e]):null})).filter(Boolean).join("&"),"host:".concat(a)].join("\n"),o=["HMAC-SHA256",Pe(i).toString(Bt)].join("\n"),s=Oe(o,this.config.secretKey).toString(Bt),u=Object.keys(r).map((function(e){return"".concat(e,"=").concat(encodeURIComponent(r[e]))})).join("&");return"".concat(this.config.wsEndpoint).concat(n,"?").concat(u,"&signature=").concat(s)}}]),e}(),Gt=function(){function e(t){if((0,v.default)(this,e),["spaceId","spaceAppId","accessKey","secretKey"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),t.endpoint){if("string"!=typeof t.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(t.endpoint))throw new Error("endpoint must start with https://");t.endpoint=t.endpoint.replace(/\/$/,"")}this.config=Object.assign({},t,{endpoint:t.endpoint||"https://".concat(t.spaceId,".api-hz.cloudbasefunction.cn"),wsEndpoint:t.wsEndpoint||"wss://".concat(t.spaceId,".api-hz.cloudbasefunction.cn")}),this._websocket=new Jt(this.config)}return(0,m.default)(e,[{key:"callFunction",value:function(e){return function(e,t){var n=e.name,a=e.data,r=e.async,i=void 0!==r&&r,o=e.timeout,s="POST",u={"x-to-function-name":n};i&&(u["x-function-invoke-type"]="async");var c=zt("/functions/invokeFunction",{functionName:n,data:a,method:s,headers:u,signHeaderKeys:["x-to-function-name"],config:t}),l=c.url,d=c.headers;return $t({url:l,data:a,method:s,headers:d,timeout:o}).then((function(e){var t=0;if(i){var n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new le({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((function(e){throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}},{key:"uploadFileToOSS",value:function(e){var t=e.url,n=e.filePath,a=e.fileType,r=e.formData,i=e.onUploadProgress;return new Promise((function(e,o){var s=pe.uploadFile({url:t,filePath:n,fileType:a,formData:r,name:"file",success:function(t){t&&t.statusCode<400?e(t):o(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){o(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof i&&s&&"function"==typeof s.onProgressUpdate&&s.onProgressUpdate((function(e){i({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"uploadFile",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,a,r,i,o,s,u,c,d,f,p;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,a=t.cloudPath,r=void 0===a?"":a,i=t.fileType,o=void 0===i?"image":i,s=t.onUploadProgress,"string"===E(r)){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(r=r.trim()){e.next=5;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(r)){e.next=7;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:return e.next=9,Ht({path:"/".concat(r.replace(/^\//,""),"?post_url")},this.config);case 9:return u=e.sent,c=u.file_id,d=u.upload_url,f=u.form_data,p=f&&f.reduce((function(e,t){return e[t.key]=t.value,e}),{}),e.abrupt("return",this.uploadFileToOSS({url:d,filePath:n,fileType:o,formData:p,onUploadProgress:s}).then((function(){return{fileID:c}})));case 15:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,r=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.fileList,e.abrupt("return",new Promise((function(e,t){(!n||n.length<0)&&e({code:"INVALID_PARAM",message:"fileList不能为空数组"}),n.length>50&&e({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});var i,o=[],s=(0,c.default)(n);try{for(s.s();!(i=s.n()).done;){var u=i.value,l=void 0;"string"!==E(u)&&e({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{l=Wt.call(r,u)}catch(e){a.warn(e.errCode,e.errMsg),l=u}o.push({file_id:l,expire:600})}}catch(d){s.e(d)}finally{s.f()}Ht({path:"/?download_url",data:{file_list:o},method:"POST"},r.config).then((function(t){var n=t.file_list,a=void 0===n?[]:n;e({fileList:a.map((function(e){return{fileID:Vt.call(r,e.file_id),tempFileURL:e.download_url}}))})})).catch((function(e){return t(e)}))})));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},{key:"connectWebSocket",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(t){var n,a;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.name,a=t.query,e.abrupt("return",pe.connectSocket({url:this._websocket.signedURL(n,a),complete:function(){}}));case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),Qt={init:function(e){e.provider="alipay";var t=new Gt(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function Yt(e){var t,n=e.data;t=ye();var a=JSON.parse(JSON.stringify(n||{}));if(Object.assign(a,{clientInfo:t}),!a.uniIdToken){var r=he(),i=r.token;i&&(a.uniIdToken=i)}return a}var Xt=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}],Zt=/[\\^$.*+?()[\]{}|]/g,en=RegExp(Zt.source);function tn(e,t,n){return e.replace(new RegExp((a=t)&&en.test(a)?a.replace(Zt,"\\$&"):a,"g"),n);var a}var nn={NONE:"none",REQUEST:"request",RESPONSE:"response",BOTH:"both"},an="_globalUniCloudStatus",rn="_globalUniCloudSecureNetworkCache__{spaceId}",on="uni-secure-network",sn={SYSTEM_ERROR:{code:2e4,message:"System error"},APP_INFO_INVALID:{code:20101,message:"Invalid client"},GET_ENCRYPT_KEY_FAILED:{code:20102,message:"Get encrypt key failed"}};function un(e){var t=e||{},n=t.errSubject,a=t.subject,r=t.errCode,i=t.errMsg,o=t.code,s=t.message,u=t.cause;return new le({subject:n||a||on,code:r||o||sn.SYSTEM_ERROR.code,message:i||s,cause:u})}var cn;cn="0123456789abcdef";var ln;function dn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.secretType;return t===nn.REQUEST||t===nn.RESPONSE||t===nn.BOTH}function fn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name,n=e.data,a=void 0===n?{}:n;return"app"===B&&"DCloud-clientDB"===t&&"encryption"===a.redirectTo&&"getAppClientKey"===a.action}function pn(e){e.functionName,e.result,e.logPvd}function hn(e){var t=e.callFunction,n=function(n){var a=this,r=n.name;n.data=Yt.call(e,{data:n.data});var i={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],o=dn(n),s=fn(n),u=o||s;return t.call(this,n).then((function(e){return e.errCode=0,!u&&pn.call(a,{functionName:r,result:e,logPvd:i}),Promise.resolve(e)}),(function(e){return!u&&pn.call(a,{functionName:r,result:e,logPvd:i}),e&&e.message&&(e.message=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.message,n=void 0===t?"":t,a=e.extraInfo,r=void 0===a?{}:a,i=e.formatter,o=void 0===i?[]:i,s=0;s<o.length;s++){var u=o[s],c=u.rule,l=u.content,d=u.mode,f=n.match(c);if(f){for(var p=l,h=1;h<f.length;h++)p=tn(p,"{$".concat(h,"}"),f[h]);for(var g in r)p=tn(p,"{".concat(g,"}"),r[g]);return"replace"===d?p:n+p}}return n}({message:"[".concat(n.name,"]: ").concat(e.message),formatter:Xt,extraInfo:{functionName:r}})),Promise.reject(e)}))};e.callFunction=function(t){var r,i,o=e.config,s=o.provider,u=o.spaceId,c=t.name;return t.data=t.data||{},r=n,r=r.bind(e),i=fn(t)?n.call(e,t):function(e){var t=e.name,n=e.data,a=void 0===n?{}:n;return"mp-weixin"===B&&"uni-id-co"===t&&"secureNetworkHandshakeByWeixin"===a.method}(t)?r.call(e,t):dn(t)?new ln({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,r=e.functionName,i=ve(),o=i.appId,s=i.uniPlatform,u=i.osName,c=s;"app"===s&&(c=u);var l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,a=q;if(!a)return{};t=function(e){return"tencent"===e?"tcb":e}(t);var r=a.find((function(e){return e.provider===t&&e.spaceId===n}));return r&&r.config}({provider:t,spaceId:n});if(!l||!l.accessControl||!l.accessControl.enable)return!1;var d=l.accessControl.function||{},f=Object.keys(d);if(0===f.length)return!0;var p=function(e,t){for(var n,a,r,i=0;i<e.length;i++){var o=e[i];o!==t?"*"!==o?o.split(",").map((function(e){return e.trim()})).indexOf(t)>-1&&(a=o):r=o:n=o}return n||a||r}(f,r);if(!p)return!1;if((d[p]||[]).find((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.appId===o&&(e.platform||"").toLowerCase()===c.toLowerCase()})))return!0;throw a.error("此应用[appId: ".concat(o,", platform: ").concat(c,"]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client")),un(sn.APP_INFO_INVALID)}({provider:s,spaceId:u,functionName:c})?new ln({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):r(t),Object.defineProperty(i,"result",{get:function(){return a.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{}}}),i.then((function(e){return e}))}}ln="mp-weixin"!==B&&"app"!==B?function(){return(0,m.default)((function e(){throw(0,v.default)(this,e),un({message:"Platform ".concat(B," is not supported by secure network")})}))}():function(){return(0,m.default)((function e(){throw(0,v.default)(this,e),un({message:"Platform ".concat(B," is not enabled, please check whether secure network module is enabled in your manifest.json")})}))}();var gn=Symbol("CLIENT_DB_INTERNAL");function vn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=gn,e.inspect=null,e.__ob__=void 0,new Proxy(e,{get:function(e,n,a){if("_uniClient"===n)return null;if("symbol"==(0,s.default)(n))return e[n];if(n in e||"string"!=typeof n){var r=e[n];return"function"==typeof r?r.bind(e):r}return t.get(e,n,a)}})}function mn(e){return{on:function(t,n){e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:function(t,n){e[t]=e[t]||[];var a=e[t].indexOf(n);-1!==a&&e[t].splice(a,1)}}}var yn=["db.Geo","db.command","command.aggregate"];function bn(e,t){return yn.indexOf("".concat(e,".").concat(t))>-1}function wn(e){switch(E(e)){case"array":return e.map((function(e){return wn(e)}));case"object":return e._internalType===gn||Object.keys(e).forEach((function(t){e[t]=wn(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function _n(e){return e&&e.content&&e.content.$method}var xn=function(){function e(t,n,a){(0,v.default)(this,e),this.content=t,this.prevStage=n||null,this.udb=null,this._database=a}return(0,m.default)(e,[{key:"toJSON",value:function(){for(var e=this,t=[e.content];e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((function(e){return{$method:e.$method,$param:wn(e.$param)}}))}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}},{key:"getAction",value:function(){var e=this.toJSON().$db.find((function(e){return"action"===e.$method}));return e&&e.$param&&e.$param[0]}},{key:"getCommand",value:function(){return{$db:this.toJSON().$db.filter((function(e){return"action"!==e.$method}))}}},{key:"isAggregate",get:function(){for(var e=this;e;){var t=_n(e),n=_n(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}},{key:"isCommand",get:function(){for(var e=this;e;){if("command"===_n(e))return!0;e=e.prevStage}return!1}},{key:"isAggregateCommand",get:function(){for(var e=this;e;){var t=_n(e),n=_n(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}},{key:"getNextStageFn",value:function(e){var t=this;return function(){return kn({$method:e,$param:wn(Array.from(arguments))},t,t._database)}}},{key:"count",get:function(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}},{key:"remove",get:function(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}},{key:"get",value:function(){return this._send("get",Array.from(arguments))}},{key:"add",get:function(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}},{key:"update",value:function(){return this._send("update",Array.from(arguments))}},{key:"end",value:function(){return this._send("end",Array.from(arguments))}},{key:"set",get:function(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}},{key:"_send",value:function(e,t){var n=this.getAction(),a=this.getCommand();return a.$db.push({$method:e,$param:wn(t)}),this._database._callCloudFunction({action:n,command:a})}}]),e}();function kn(e,t,n){return vn(new xn(e,t,n),{get:function(e,t){var a="db";return e&&e.content&&(a=e.content.$method),bn(a,t)?kn({$method:t},e,n):function(){return kn({$method:t,$param:wn(Array.from(arguments))},e,n)}}})}function Sn(e){var t=e.path,n=e.method;return function(){function e(){(0,v.default)(this,e),this.param=Array.from(arguments)}return(0,m.default)(e,[{key:"toJSON",value:function(){return{$newDb:[].concat((0,u.default)(t.map((function(e){return{$method:e}}))),[{$method:n,$param:this.param}])}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}}]),e}()}var Tn=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.uniClient,a=void 0===n?{}:n,r=t.isJQL,i=void 0!==r&&r;(0,v.default)(this,e),this._uniClient=a,this._authCallBacks={},this._dbCallBacks={},a._isDefault&&(this._dbCallBacks=H("_globalUniCloudDatabaseCallback")),i||(this.auth=mn(this._authCallBacks)),this._isJQL=i,Object.assign(this,mn(this._dbCallBacks)),this.env=vn({},{get:function(e,t){return{$env:t}}}),this.Geo=vn({},{get:function(e,t){return Sn({path:["Geo"],method:t})}}),this.serverDate=Sn({path:[],method:"serverDate"}),this.RegExp=Sn({path:[],method:"RegExp"})}return(0,m.default)(e,[{key:"getCloudEnv",value:function(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}},{key:"_callback",value:function(e,t){var n=this._dbCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,u.default)(t))}))}},{key:"_callbackAuth",value:function(e,t){var n=this._authCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,u.default)(t))}))}},{key:"multiSend",value:function(){var e=Array.from(arguments),t=e.map((function(e){var t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}]),e}();function In(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return vn(new e(t),{get:function(e,t){return bn("db",t)?kn({$method:t},null,e):function(){return kn({$method:t,$param:wn(Array.from(arguments))},null,e)}}})}var Pn=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"_parseResult",value:function(e){return this._isJQL?e.result:e}},{key:"_callCloudFunction",value:function(e){var t=this,n=e.action,r=e.command,i=e.multiCommand,o=e.queryList;function s(e,t){if(i&&o)for(var n=0;n<o.length;n++){var a=o[n];a.udb&&"function"==typeof a.udb.setResult&&(t?a.udb.setResult(t):a.udb.setResult(e.result.dataList[n]))}}var u=this,c=this._isJQL?"databaseForJQL":"database";function l(e){return u._callback("error",[e]),Q(Y(c,"fail"),e).then((function(){return Q(Y(c,"complete"),e)})).then((function(){return s(null,e),ie(ee.RESPONSE,{type:te.CLIENT_DB,content:e}),Promise.reject(e)}))}var d=Q(Y(c,"invoke")),f=this._uniClient;return d.then((function(){return f.callFunction({name:"DCloud-clientDB",type:P.CLIENT_DB,data:{action:n,command:r,multiCommand:i}})})).then((function(e){var n=e.result,r=n.code,i=n.message,o=n.token,d=n.tokenExpired,f=n.systemInfo,p=void 0===f?[]:f;if(p)for(var h=0;h<p.length;h++){var g=p[h],v=g.level,m=g.message,y=g.detail,b="[System Info]"+m;y&&(b="".concat(b,"\n详细信息：").concat(y)),(a["app"===B&&"warn"===v?"error":v]||a.log)(b)}if(r)return l(new le({code:r,message:i,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,o&&d&&(ge({token:o,tokenExpired:d}),t._callbackAuth("refreshToken",[{token:o,tokenExpired:d}]),t._callback("refreshToken",[{token:o,tokenExpired:d}]),ie(ee.REFRESH_TOKEN,{token:o,tokenExpired:d}));for(var w=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}],_=function(t){var n=w[t],r=n.prop,i=n.tips;if(r in e.result){var o=e.result[r];Object.defineProperty(e.result,r,{get:function(){return a.warn(i),o}})}},x=0;x<w.length;x++)_(x);return function(e){return Q(Y(c,"success"),e).then((function(){return Q(Y(c,"complete"),e)})).then((function(){s(e,null);var t=u._parseResult(e);return ie(ee.RESPONSE,{type:te.CLIENT_DB,content:t}),Promise.resolve(t)}))}(e)}),(function(e){return/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&a.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),l(new le({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId}))}))}}]),n}(Tn),On="token无效，跳转登录页面",An="token过期，跳转登录页面",Cn={TOKEN_INVALID_TOKEN_EXPIRED:An,TOKEN_INVALID_INVALID_CLIENTID:On,TOKEN_INVALID:On,TOKEN_INVALID_WRONG_TOKEN:On,TOKEN_INVALID_ANONYMOUS_USER:On},En={"uni-id-token-expired":An,"uni-id-check-token-failed":On,"uni-id-token-not-exist":On,"uni-id-check-device-feature-failed":On},Rn=(0,f.default)((0,f.default)((0,f.default)({},Cn),En),{},{default:"用户未登录或登录状态过期，自动跳转登录页面"});function Dn(e,t){var n="";return n=e?"".concat(e,"/").concat(t):t,n.replace(/^\//,"")}function Ln(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=[],a=[];return e.forEach((function(e){!0===e.needLogin?n.push(Dn(t,e.path)):!1===e.needLogin&&a.push(Dn(t,e.path))})),{needLoginPage:n,notNeedLoginPage:a}}function Nn(e){return e.split("?")[0].replace(/^\//,"")}function Un(){return function(e){var t=e&&e.$page&&e.$page.fullPath;return t?("/"!==t.charAt(0)&&(t="/"+t),t):""}(function(){var e=getCurrentPages();return e[e.length-1]}())}function Mn(){return Nn(Un())}function Fn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;var n=t.list,a=Nn(e);return n.some((function(e){return e.pagePath===a}))}var jn,qn=!!y.default.uniIdRouter,Bn=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y.default,t=e.pages,n=void 0===t?[]:t,a=e.subPackages,r=void 0===a?[]:a,i=e.uniIdRouter,o=void 0===i?{}:i,s=e.tabBar,c=void 0===s?{}:s,l=o.loginPage,d=o.needLogin,f=void 0===d?[]:d,p=o.resToLogin,h=void 0===p||p,g=Ln(n),v=g.needLoginPage,m=g.notNeedLoginPage,b=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[],n=[];return e.forEach((function(e){var a=e.root,r=e.pages,i=void 0===r?[]:r,o=Ln(i,a),s=o.needLoginPage,c=o.notNeedLoginPage;t.push.apply(t,(0,u.default)(s)),n.push.apply(n,(0,u.default)(c))})),{needLoginPage:t,notNeedLoginPage:n}}(r),w=b.needLoginPage,_=b.notNeedLoginPage;return{loginPage:l,routerNeedLogin:f,resToLogin:h,needLoginPage:[].concat((0,u.default)(v),(0,u.default)(w)),notNeedLoginPage:[].concat((0,u.default)(m),(0,u.default)(_)),loginPageInTabBar:Fn(l,c)}}(),Kn=Bn.loginPage,zn=Bn.routerNeedLogin,$n=Bn.resToLogin,Hn=Bn.needLoginPage,Wn=Bn.notNeedLoginPage,Vn=Bn.loginPageInTabBar;if(Hn.indexOf(Kn)>-1)throw new Error("Login page [".concat(Kn,'] should not be "needLogin", please check your pages.json'));function Jn(e){var t=Mn();if("/"===e.charAt(0))return e;var n=e.split("?"),a=(0,o.default)(n,2),r=a[0],i=a[1],s=r.replace(/^\//,"").split("/"),u=t.split("/");u.pop();for(var c=0;c<s.length;c++){var l=s[c];".."===l?u.pop():"."!==l&&u.push(l)}return""===u[0]&&u.shift(),"/"+u.join("/")+(i?"?"+i:"")}function Gn(e){var t=Nn(Jn(e));return!(Wn.indexOf(t)>-1)&&(Hn.indexOf(t)>-1||zn.some((function(t){return function(e,t){return new RegExp(t).test(e)}(e,t)})))}function Qn(e){var t=e.redirect,n=Nn(t),a=Nn(Kn);return Mn()!==a&&n!==a}function Yn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.api,n=e.redirect;if(n&&Qn({redirect:n})){var a=function(e,t){return"/"!==e.charAt(0)&&(e="/"+e),t?e.indexOf("?")>-1?e+"&uniIdRedirectUrl=".concat(encodeURIComponent(t)):e+"?uniIdRedirectUrl=".concat(encodeURIComponent(t)):e}(Kn,n);Vn?"navigateTo"!==t&&"redirectTo"!==t||(t="switchTab"):"switchTab"===t&&(t="navigateTo");var r={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((function(){r[t]({url:a})}),0)}}function Xn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.url,n={abortLoginPageJump:!1,autoToLoginPage:!1},a=function(){var e,t=he(),n=t.token,a=t.tokenExpired;if(n){if(a<Date.now()){var r="uni-id-token-expired";e={errCode:r,errMsg:Rn[r]}}}else{var i="uni-id-check-token-failed";e={errCode:i,errMsg:Rn[i]}}return e}();if(Gn(t)&&a){if(a.uniIdRedirectUrl=t,ne(ee.NEED_LOGIN).length>0)return setTimeout((function(){ie(ee.NEED_LOGIN,a)}),0),n.abortLoginPageJump=!0,n;n.autoToLoginPage=!0}return n}function Zn(){!function(){var e=Un(),t=Xn({url:e}),n=t.abortLoginPageJump,a=t.autoToLoginPage;n||a&&Yn({api:"redirectTo",redirect:e})}();for(var e=["navigateTo","redirectTo","reLaunch","switchTab"],t=function(t){var n=e[t];uni.addInterceptor(n,{invoke:function(e){var t=Xn({url:e.url}),a=t.abortLoginPageJump,r=t.autoToLoginPage;return a?e:r?(Yn({api:n,redirect:Jn(e.url)}),!1):e}})},n=0;n<e.length;n++)t(n)}function ea(){this.onResponse((function(e){var t=e.type,n=e.content,a=!1;switch(t){case"cloudobject":a=function(e){if("object"!=(0,s.default)(e))return!1;var t=e||{},n=t.errCode;return n in Rn}(n);break;case"clientdb":a=function(e){if("object"!=(0,s.default)(e))return!1;var t=e||{},n=t.errCode;return n in Cn}(n)}a&&function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=ne(ee.NEED_LOGIN);ue().then((function(){var n=Un();if(n&&Qn({redirect:n}))return t.length>0?ie(ee.NEED_LOGIN,Object.assign({uniIdRedirectUrl:n},e)):void(Kn&&Yn({api:"navigateTo",redirect:n}))}))}(n)}))}function ta(e){!function(e){e.onResponse=function(e){ae(ee.RESPONSE,e)},e.offResponse=function(e){re(ee.RESPONSE,e)}}(e),function(e){e.onNeedLogin=function(e){ae(ee.NEED_LOGIN,e)},e.offNeedLogin=function(e){re(ee.NEED_LOGIN,e)},qn&&(H(an).needLoginInit||(H(an).needLoginInit=!0,ue().then((function(){Zn.call(e)})),$n&&ea.call(e)))}(e),function(e){e.onRefreshToken=function(e){ae(ee.REFRESH_TOKEN,e)},e.offRefreshToken=function(e){re(ee.REFRESH_TOKEN,e)}}(e)}var na="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",aa=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function ra(){var e,t,n=he().token||"",a=n.split(".");if(!n||3!==a.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{e=JSON.parse((t=a[1],decodeURIComponent(jn(t).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(n){throw new Error("获取当前用户信息出错，详细错误信息为："+n.message)}return e.tokenExpired=1e3*e.exp,delete e.exp,delete e.iat,e}jn="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!aa.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,a,r="",i=0;i<e.length;)t=na.indexOf(e.charAt(i++))<<18|na.indexOf(e.charAt(i++))<<12|(n=na.indexOf(e.charAt(i++)))<<6|(a=na.indexOf(e.charAt(i++))),r+=64===n?String.fromCharCode(t>>16&255):64===a?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return r}:atob;var ia=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n="chooseAndUploadFile:ok",a="chooseAndUploadFile:fail";function r(e,t){return e.tempFiles.forEach((function(e,n){e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((function(e){return e.path}))),e}function i(e,t,a){var r=a.onChooseFile,i=a.onUploadProgress;return t.then((function(e){if(r){var t=r(e);if(void 0!==t)return Promise.resolve(t).then((function(t){return void 0===t?e:t}))}return e})).then((function(t){return!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=arguments.length>3?arguments[3]:void 0;(t=Object.assign({},t)).errMsg=n;var i=t.tempFiles,o=i.length,s=0;return new Promise((function(n){for(;s<a;)u();function u(){var a=s++;if(a>=o)!i.find((function(e){return!e.url&&!e.errMsg}))&&n(t);else{var c=i[a];e.uploadFile({provider:c.provider,filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,cloudPathAsRealPath:c.cloudPathAsRealPath,onUploadProgress:function(e){e.index=a,e.tempFile=c,e.tempFilePath=c.path,r&&r(e)}}).then((function(e){c.url=e.fileID,a<o&&u()})).catch((function(e){c.errMsg=e.errMsg||e.message,a<o&&u()}))}}}))}(e,t,5,i)}))}t.initChooseAndUploadFile=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"all"};return"image"===t.type?i(e,function(e){var t=e.count,n=e.sizeType,i=e.sourceType,o=void 0===i?["album","camera"]:i,s=e.extension;return new Promise((function(e,i){uni.chooseImage({count:t,sizeType:n,sourceType:o,extension:s,success:function(t){e(r(t,"image"))},fail:function(e){i({errMsg:e.errMsg.replace("chooseImage:fail",a)})}})}))}(t),t):"video"===t.type?i(e,function(e){var t=e.camera,n=e.compressed,i=e.maxDuration,o=e.sourceType,s=void 0===o?["album","camera"]:o,u=e.extension;return new Promise((function(e,o){uni.chooseVideo({camera:t,compressed:n,maxDuration:i,sourceType:s,extension:u,success:function(t){var n=t.tempFilePath,a=t.duration,i=t.size,o=t.height,s=t.width;e(r({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:i,type:t.tempFile&&t.tempFile.type||"",width:s,height:o,duration:a,fileType:"video",cloudPath:""}]},"video"))},fail:function(e){o({errMsg:e.errMsg.replace("chooseVideo:fail",a)})}})}))}(t),t):i(e,function(e){var t=e.count,n=e.extension;return new Promise((function(e,i){var o=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(o=wx.chooseMessageFile),"function"!=typeof o)return i({errMsg:a+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});o({type:"all",count:t,extension:n,success:function(t){e(r(t))},fail:function(e){i({errMsg:e.errMsg.replace("chooseFile:fail",a)})}})}))}(t),t)}}})),oa=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(ia),sa={auto:"auto",onready:"onready",manual:"manual"};function ua(e){return{props:{localdata:{type:Array,default:function(){return[]}},options:{type:[Object,Array],default:function(){return{}}},spaceInfo:{type:Object,default:function(){return{}}},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:function(){return{mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}},created:function(){var e=this;this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((function(){var t=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((function(n){t.push(e[n])})),t}),(function(t,n){if(e.loadtime!==sa.manual){for(var a=!1,r=[],i=2;i<t.length;i++)t[i]!==n[i]&&(r.push(t[i]),a=!0);t[0]!==n[0]&&(e.mixinDatacomPage.current=e.pageCurrent),e.mixinDatacomPage.size=e.pageSize,e.onMixinDatacomPropsChange(a,r)}}))},methods:{onMixinDatacomPropsChange:function(e,t){},mixinDatacomEasyGet:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.getone,a=void 0!==n&&n,r=t.success,i=t.fail;this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((function(t){e.mixinDatacomLoading=!1;var n=t.result,i=n.data,o=n.count;e.getcount&&(e.mixinDatacomPage.count=o),e.mixinDatacomHasMore=i.length<e.pageSize;var s=a?i.length?i[0]:void 0:i;e.mixinDatacomResData=s,r&&r(s)})).catch((function(t){e.mixinDatacomLoading=!1,e.mixinDatacomErrorMessage=t,e.mixinDatacomError=t,i&&i(t)})))},mixinDatacomGet:function(){var t,n,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};a=a||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);var r=a.action||this.action;r&&(n=n.action(r));var i=a.collection||this.collection;n=Array.isArray(i)?(t=n).collection.apply(t,(0,u.default)(i)):n.collection(i);var o=a.where||this.where;o&&Object.keys(o).length&&(n=n.where(o));var s=a.field||this.field;s&&(n=n.field(s));var c=a.foreignKey||this.foreignKey;c&&(n=n.foreignKey(c));var l=a.groupby||this.groupby;l&&(n=n.groupBy(l));var d=a.groupField||this.groupField;d&&(n=n.groupField(d)),!0===(void 0!==a.distinct?a.distinct:this.distinct)&&(n=n.distinct());var f=a.orderby||this.orderby;f&&(n=n.orderBy(f));var p=void 0!==a.pageCurrent?a.pageCurrent:this.mixinDatacomPage.current,h=void 0!==a.pageSize?a.pageSize:this.mixinDatacomPage.size,g=void 0!==a.getcount?a.getcount:this.getcount,v=void 0!==a.gettree?a.gettree:this.gettree,m=void 0!==a.gettreepath?a.gettreepath:this.gettreepath,y={getCount:g},b={limitLevel:void 0!==a.limitlevel?a.limitlevel:this.limitlevel,startWith:void 0!==a.startwith?a.startwith:this.startwith};return v&&(y.getTree=b),m&&(y.getTreePath=b),n=n.skip(h*(p-1)).limit(h).get(y),n}}}}function ca(e){return H(rn.replace("{spaceId}",e.config.spaceId))}function la(){return da.apply(this,arguments)}function da(){return da=(0,d.default)((0,l.default)().mark((function e(){var t,n,a,r,i,o,s,u=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=u.length>0&&void 0!==u[0]?u[0]:{},n=t.openid,a=t.callLoginByWeixin,r=void 0!==a&&a,i=ca(this),"mp-weixin"===B){e.next=4;break}throw new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `".concat(B,"`"));case 4:if(!n||!r){e.next=6;break}throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");case 6:if(!n){e.next=8;break}return e.abrupt("return",(i.mpWeixinOpenid=n,{}));case 8:return e.next=10,new Promise((function(e,t){uni.login({success:function(t){e(t.code)},fail:function(e){t(new Error(e.errMsg))}})}));case 10:return o=e.sent,s=this.importObject("uni-id-co",{customUI:!0}),e.next=14,s.secureNetworkHandshakeByWeixin({code:o,callLoginByWeixin:r});case 14:return i.mpWeixinCode=o,e.abrupt("return",{code:o});case 16:case"end":return e.stop()}}),e,this)}))),da.apply(this,arguments)}function fa(e){return pa.apply(this,arguments)}function pa(){return pa=(0,d.default)((0,l.default)().mark((function e(t){var n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=ca(this),e.abrupt("return",(n.initPromise||(n.initPromise=la.call(this,t).then((function(e){return e})).catch((function(e){throw delete n.initPromise,e}))),n.initPromise));case 2:case"end":return e.stop()}}),e,this)}))),pa.apply(this,arguments)}function ha(e){!function(e){me=e}(e)}function ga(e){var t="mp-weixin"===B&&wx.canIUse("getAppBaseInfo"),n={getAppBaseInfo:t?uni.getAppBaseInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(a){return new Promise((function(r,i){t&&"getAppBaseInfo"===e?r(n[e]()):n[e]((0,f.default)((0,f.default)({},a),{},{success:function(e){r(e)},fail:function(e){i(e)}}))}))}}var va=function(e){(0,p.default)(n,e);var t=(0,h.default)(n);function n(){var e;return(0,v.default)(this,n),e=t.call(this),e._uniPushMessageCallback=e._receivePushMessage.bind((0,i.default)(e)),e._currentMessageId=-1,e._payloadQueue=[],e}return(0,m.default)(n,[{key:"init",value:function(){var e=this;return Promise.all([ga("getAppBaseInfo")(),ga("getPushClientId")()]).then((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=(0,o.default)(t,2),a=n[0];a=void 0===a?{}:a;var r=a.appId,i=n[1];i=void 0===i?{}:i;var s=i.cid;if(!r)throw new Error("Invalid appId, please check the manifest.json file");if(!s)throw new Error("Invalid push client id");e._appId=r,e._pushClientId=s,e._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),e.emit("open"),e._initMessageListener()}),(function(t){throw e.emit("error",t),e.close(),t}))}},{key:"open",value:function(){var e=(0,d.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.init());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"_isUniCloudSSE",value:function(e){if("receive"!==e.type)return!1;var t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}},{key:"_receivePushMessage",value:function(e){if(this._isUniCloudSSE(e)){var t=e&&e.data&&e.data.payload,n=t.action,a=t.messageId,r=t.message;this._payloadQueue.push({action:n,messageId:a,message:r}),this._consumMessage()}}},{key:"_consumMessage",value:function(){for(var e=this;;){var t=this._payloadQueue.find((function(t){return t.messageId===e._currentMessageId+1}));if(!t)break;this._currentMessageId++,this._parseMessagePayload(t)}}},{key:"_parseMessagePayload",value:function(e){var t=e.action,n=e.messageId,a=e.message;"end"===t?this._end({messageId:n,message:a}):"message"===t&&this._appendMessage({messageId:n,message:a})}},{key:"_appendMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("message",t)}},{key:"_end",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("end",t),this.close()}},{key:"_initMessageListener",value:function(){uni.onPushMessage(this._uniPushMessageCallback)}},{key:"_destroy",value:function(){uni.offPushMessage(this._uniPushMessageCallback)}},{key:"toJSON",value:function(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}},{key:"close",value:function(){this._destroy(),this.emit("close")}}]),n}(F);var ma={tcb:Dt,tencent:Dt,aliyun:xe,private:qt,dcloud:qt,alipay:Qt},ya=new(function(){function e(){(0,v.default)(this,e)}return(0,m.default)(e,[{key:"init",value:function(e){var t={},n=ma[e.provider];if(!n)throw new Error("未提供正确的provider参数");return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new M({createPromise:function(){var t=Promise.resolve();t=new Promise((function(e){setTimeout((function(){e()}),1)}));var n=e.auth();return t.then((function(){return n.getLoginState()})).then((function(e){return e?Promise.resolve():n.signInAnonymously()}))}}))}(t),hn(t),function(e){var t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),function(e){e.database=function(t){if(t&&Object.keys(t).length>0)return e.init(t).database();if(this._database)return this._database;var n=In(Pn,{uniClient:e});return this._database=n,n},e.databaseForJQL=function(t){if(t&&Object.keys(t).length>0)return e.init(t).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;var n=In(Pn,{uniClient:e,isJQL:!0});return this._databaseForJQL=n,n}}(t),function(e){e.getCurrentUserInfo=ra,e.chooseAndUploadFile=oa.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return ua(e)}}),e.SSEChannel=va,e.initSecureNetworkByWeixin=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.openid,a=t.callLoginByWeixin,r=void 0!==a&&a;return fa.call(e,{openid:n,callLoginByWeixin:r})}}(e),e.setCustomClientInfo=ha,e.importObject=function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==(0,s.default)(t.secretMethods)&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},n);var a=n,r=a.customUI,i=a.loadingOptions,o=a.errorOptions,u=a.parseSystemError,c=!r;return new Proxy({},{get:function(a,r){switch(r){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fn,n=e.interceptorName,a=e.getCallbackArgs;return(0,d.default)((0,l.default)().mark((function e(){var r,i,o,s,u,c,d=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(r=d.length,i=new Array(r),o=0;o<r;o++)i[o]=d[o];return s=a?a({params:i}):{},e.prev=2,e.next=5,Q(Y(n,"invoke"),(0,f.default)({},s));case 5:return e.next=7,t.apply(void 0,i);case 7:return u=e.sent,e.next=10,Q(Y(n,"success"),(0,f.default)((0,f.default)({},s),{},{result:u}));case 10:return e.abrupt("return",u);case 13:return e.prev=13,e.t0=e["catch"](2),c=e.t0,e.next=18,Q(Y(n,"fail"),(0,f.default)((0,f.default)({},s),{},{error:c}));case 18:throw c;case 19:return e.prev=19,e.next=22,Q(Y(n,"complete"),c?(0,f.default)((0,f.default)({},s),{},{error:c}):(0,f.default)((0,f.default)({},s),{},{result:u}));case 22:return e.finish(19);case 23:case"end":return e.stop()}}),e,null,[[2,13,19,23]])})))}({fn:function(){var a=(0,d.default)((0,l.default)().mark((function a(){var h,g,v,m,y,b,w,_,x,k,S,T,I,O,A,C=arguments;return(0,l.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:for(c&&uni.showLoading({title:i.title,mask:i.mask}),g=C.length,v=new Array(g),m=0;m<g;m++)v[m]=C[m];return y={name:t,type:P.OBJECT,data:{method:r,params:v}},"object"==(0,s.default)(n.secretMethods)&&function(e,t){var n=t.data.method,a=e.secretMethods||{},r=a[n]||a["*"];r&&(t.secretType=r)}(n,y),b=!1,a.prev=5,a.next=8,e.callFunction(y);case 8:h=a.sent,a.next=14;break;case 11:a.prev=11,a.t0=a["catch"](5),b=!0,h={result:new le(a.t0)};case 14:if(w=h.result||{},_=w.errSubject,x=w.errCode,k=w.errMsg,S=w.newToken,c&&uni.hideLoading(),S&&S.token&&S.tokenExpired&&(ge(S),ie(ee.REFRESH_TOKEN,(0,f.default)({},S))),!x){a.next=39;break}if(T=k,!b||!u){a.next=24;break}return a.next=20,u({objectName:t,methodName:r,params:v,errSubject:_,errCode:x,errMsg:k});case 20:if(a.t1=a.sent.errMsg,a.t1){a.next=23;break}a.t1=k;case 23:T=a.t1;case 24:if(!c){a.next=37;break}if("toast"!==o.type){a.next=29;break}uni.showToast({title:T,icon:"none"}),a.next=37;break;case 29:if("modal"===o.type){a.next=31;break}throw new Error("Invalid errorOptions.type: ".concat(o.type));case 31:return a.next=33,(0,d.default)((0,l.default)().mark((function e(){var t,n,a,r,i,o,s=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=s.length>0&&void 0!==s[0]?s[0]:{},n=t.title,a=t.content,r=t.showCancel,i=t.cancelText,o=t.confirmText,e.abrupt("return",new Promise((function(e,t){uni.showModal({title:n,content:a,showCancel:r,cancelText:i,confirmText:o,success:function(t){e(t)},fail:function(){e({confirm:!1,cancel:!0})}})})));case 2:case"end":return e.stop()}}),e)})))({title:"提示",content:T,showCancel:o.retry,cancelText:"取消",confirmText:o.retry?"重试":"确定"});case 33:if(I=a.sent,O=I.confirm,!o.retry||!O){a.next=37;break}return a.abrupt("return",p.apply(void 0,v));case 37:throw A=new le({subject:_,code:x,message:k,requestId:h.requestId}),A.detail=h.result,ie(ee.RESPONSE,{type:te.CLOUD_OBJECT,content:A}),A;case 39:return a.abrupt("return",(ie(ee.RESPONSE,{type:te.CLOUD_OBJECT,content:h.result}),h.result));case 40:case"end":return a.stop()}}),a,null,[[5,11]])})));function p(){return a.apply(this,arguments)}return p}(),interceptorName:"callObject",getCallbackArgs:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.params;return{objectName:t,methodName:r,params:n}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((function(e){if(t[e]){var n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){var a=this,r=!1;if("callFunction"===t){var i=n&&n.type||P.DEFAULT;r=i!==P.DEFAULT}var o="callFunction"===t&&!r,s=this._initPromiseHub.exec();n=n||{};var u=ce(n),c=u.success,l=u.fail,d=u.complete,f=s.then((function(){return r?Promise.resolve():Q(Y(t,"invoke"),n)})).then((function(){return e.call(a,n)})).then((function(e){return r?Promise.resolve(e):Q(Y(t,"success"),e).then((function(){return Q(Y(t,"complete"),e)})).then((function(){return o&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e}),Promise.resolve(e)}))}),(function(e){return r?Promise.reject(e):Q(Y(t,"fail"),e).then((function(){return Q(Y(t,"complete"),e)})).then((function(){return ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e}),Promise.reject(e)}))}));if(!(c||l||d))return f;f.then((function(e){c&&c(e),d&&d(e),o&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e})}),(function(e){l&&l(e),d&&d(e),o&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e})}))}}(t[e],e).bind(t)}})),t.init=this.init,t}}]),e}());t.uniCloud=ya,function(){var e=K,n={};if(e&&1===e.length)n=e[0],t.uniCloud=ya=ya.init(n),ya._isDefault=!0;else{var r,i=["database","getCurrentUserInfo","importObject"];r=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",[].concat(["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile"],i).forEach((function(e){ya[e]=function(){if(a.error(r),-1===i.indexOf(e))return Promise.reject(new le({code:"SYS_ERR",message:r}));a.error(r)}}))}if(Object.assign(ya,{get mixinDatacom(){return ua(ya)}}),ta(ya),ya.addInterceptor=J,ya.removeInterceptor=G,ya.interceptObject=X,"app"===B&&(uni.__uniCloud=ya),"app"===B||"web"===B){var o=function(){return z||(z=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),z)}();o.uniCloud=ya,o.UniCloudError=le}}();var ba=ya;t.default=ba}).call(this,n("0ee4"),n("ba7c")["default"])},"867c":function(e,t,n){var a=n("c86c");t=a(!1),t.push([e.i,'@charset "UTF-8";@media screen and (max-width:500px){.hide-on-phone[data-v-974dabca]{display:none}}.uni-stat__select[data-v-974dabca]{display:flex;align-items:center;cursor:pointer;width:100%;flex:1;box-sizing:border-box}.uni-stat-box[data-v-974dabca]{background-color:#fff;width:100%;flex:1}.uni-stat__actived[data-v-974dabca]{width:100%;flex:1}.uni-label-text[data-v-974dabca]{font-size:14px;font-weight:700;color:#6a6a6a;margin:auto 0;margin-right:5px}.uni-select[data-v-974dabca]{font-size:14px;border:1px solid #e5e5e5;box-sizing:border-box;border-radius:4px;padding:0 5px;padding-left:10px;position:relative;display:flex;-webkit-user-select:none;user-select:none;flex-direction:row;align-items:center;border-bottom:solid 1px #e5e5e5;width:100%;flex:1;height:35px}.uni-select--disabled[data-v-974dabca]{background-color:#f5f7fa;cursor:not-allowed}.uni-select__label[data-v-974dabca]{font-size:16px;height:35px;padding-right:10px;color:#909399}.uni-select__input-box[data-v-974dabca]{height:35px;width:0;position:relative;display:flex;flex:1;flex-direction:row;align-items:center}.uni-select__input[data-v-974dabca]{flex:1;font-size:14px;height:22px;line-height:22px}.uni-select__input-plac[data-v-974dabca]{font-size:14px;color:#909399}.uni-select__selector[data-v-974dabca]{box-sizing:border-box;position:absolute;left:0;width:100%;background-color:#fff;border:1px solid #ebeef5;border-radius:6px;box-shadow:0 2px 12px 0 rgba(0,0,0,.1);z-index:3;padding:4px 0}.uni-select__selector-scroll[data-v-974dabca]{max-height:200px;box-sizing:border-box}@media (min-width:768px){.uni-select__selector-scroll[data-v-974dabca]{max-height:600px}}.uni-select__selector-empty[data-v-974dabca],\n.uni-select__selector-item[data-v-974dabca]{display:flex;cursor:pointer;line-height:35px;font-size:14px;text-align:center;\n  /* border-bottom: solid 1px $uni-border-3; */padding:0 10px}.uni-select__selector-item[data-v-974dabca]:hover{background-color:#f9f9f9}.uni-select__selector-empty[data-v-974dabca]:last-child,\n.uni-select__selector-item[data-v-974dabca]:last-child{border-bottom:none}.uni-select__selector__disabled[data-v-974dabca]{opacity:.4;cursor:default}\n/* picker 弹出层通用的指示小三角 */.uni-popper__arrow_bottom[data-v-974dabca],\n.uni-popper__arrow_bottom[data-v-974dabca]::after,\n.uni-popper__arrow_top[data-v-974dabca],\n.uni-popper__arrow_top[data-v-974dabca]::after{position:absolute;display:block;width:0;height:0;border-color:transparent;border-style:solid;border-width:6px}.uni-popper__arrow_bottom[data-v-974dabca]{-webkit-filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));top:-6px;left:10%;margin-right:3px;border-top-width:0;border-bottom-color:#ebeef5}.uni-popper__arrow_bottom[data-v-974dabca]::after{content:" ";top:1px;margin-left:-6px;border-top-width:0;border-bottom-color:#fff}.uni-popper__arrow_top[data-v-974dabca]{-webkit-filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));filter:drop-shadow(0 2px 12px rgba(0,0,0,.03));bottom:-6px;left:10%;margin-right:3px;border-bottom-width:0;border-top-color:#ebeef5}.uni-popper__arrow_top[data-v-974dabca]::after{content:" ";bottom:1px;margin-left:-6px;border-bottom-width:0;border-top-color:#fff}.uni-select__input-text[data-v-974dabca]{width:100%;color:#333;white-space:nowrap;text-overflow:ellipsis;-o-text-overflow:ellipsis;overflow:hidden}.uni-select__input-placeholder[data-v-974dabca]{color:#6a6a6a;font-size:12px}.uni-select--mask[data-v-974dabca]{position:fixed;top:0;bottom:0;right:0;left:0;z-index:2}',""]),e.exports=t},8746:function(e,t,n){"use strict";var a=n("f17e"),r=n.n(a);r.a},8857:function(e,t,n){"use strict";n.r(t);var a=n("92cd"),r=n("ab74");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("650d");var o=n("828b"),s=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,"2cf78b47",null,!1,a["a"],void 0);t["default"]=s.exports},"8b7e":function(e,t,n){var a=n("c86c");t=a(!1),t.push([e.i,".xe-upload[data-v-25178f5c]{display:none}",""]),e.exports=t},9028:function(e,t,n){"use strict";n.r(t);var a=n("a332"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},"92cd":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var a={uTransition:n("3217").default,uIcon:n("aa10").default},r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("u-transition",{attrs:{mode:"fade",show:e.show}},[n("v-uni-view",{staticClass:"u-tag-wrapper"},[n("v-uni-view",{staticClass:"u-tag",class:["u-tag--"+e.shape,!e.plain&&"u-tag--"+e.type,e.plain&&"u-tag--"+e.type+"--plain","u-tag--"+e.size,e.plain&&e.plainFill&&"u-tag--"+e.type+"--plain--fill"],style:[{marginRight:e.closable?"10px":0,marginTop:e.closable?"10px":0},e.style],on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.clickHandler.apply(void 0,arguments)}}},[e._t("icon",[e.icon?n("v-uni-view",{staticClass:"u-tag__icon"},[e.$u.test.image(e.icon)?n("v-uni-image",{style:[e.imgStyle],attrs:{src:e.icon}}):n("u-icon",{attrs:{color:e.elIconColor,name:e.icon,size:e.iconSize}})],1):e._e()]),n("v-uni-text",{staticClass:"u-tag__text",class:["u-tag__text--"+e.type,e.plain&&"u-tag__text--"+e.type+"--plain","u-tag__text--"+e.size],style:[e.textColor]},[e._v(e._s(e.text))])],2),e.closable?n("v-uni-view",{staticClass:"u-tag__close",class:["u-tag__close--"+e.size],style:{backgroundColor:e.closeColor},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.closeHandler.apply(void 0,arguments)}}},[n("u-icon",{attrs:{name:"close",size:e.closeSize,color:"#ffffff"}})],1):e._e()],1)],1)},i=[]},"933b":function(e,t,n){"use strict";n.r(t);var a=n("dec0"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},9370:function(e,t,n){"use strict";var a=n("8bdb"),r=n("af9e"),i=n("1099"),o=n("c215"),s=r((function(){return null!==new Date(NaN).toJSON()||1!==Date.prototype.toJSON.call({toISOString:function(){return 1}})}));a({target:"Date",proto:!0,arity:1,forced:s},{toJSON:function(e){var t=i(this),n=o(t,"number");return"number"!=typeof n||isFinite(n)?t.toISOString():null}})},9572:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pages:[{path:"pages/login/login"},{path:"pages/index/index",style:{navigationBarTitleText:"职业健康",titleNView:!1,navigationStyle:"custom"}},{path:"pages/login/bindPhoneNum"},{path:"pages/login/bindWxInfo"},{path:"pages/index/signature"},{path:"pages/login/zfbAutho",style:{navigationBarTitleText:"支付宝授权"}},{path:"pages/index/search"},{path:"pages/reorientation/reorientation",style:{navigationBarTitleText:"",enablePullDownRefresh:!1}},{path:"pages/institution/index"},{path:"pages/institution/tjRecord"},{path:"pages/institution/tjResult"},{path:"pages/institution/tjBooking"},{path:"pages/institution/tjAppoint"},{path:"pages/institution/tjMessage"},{path:"pages/institution/tjAuth"},{path:"pages/institution/institution"},{path:"pages/institution/jgDetail"},{path:"pages/institution/jgForm"},{path:"pages/institution/zdResult"},{path:"pages/institution/ysReport"},{path:"pages/institution/addInformation"},{path:"pages/lifeCycle/lifeCycle",style:{navigationBarTitleText:"生命周期管理"}},{path:"pages/workInjuryRecognition/index"},{path:"pages/workInjuryRecognition/add"},{path:"pages/workInjuryRecognition/detail"},{path:"pages/institution/zdProcessFile"}],subPackages:[{root:"pages_train",pages:[{path:"pages/training/faceInput"},{path:"pages/training/faceValid"},{path:"pages/training/courses/courses"},{path:"pages/training/courses/course"},{path:"pages/training/courses/courseWithoutPersonal"},{path:"pages/training/courses/document"},{path:"pages/training/courses/search"},{path:"pages/training/myTraining",style:{enablePullDownRefresh:!0}},{path:"pages/training/detail",style:{enablePullDownRefresh:!0}},{path:"pages/training/test"},{path:"pages/training/myCourses",style:{enablePullDownRefresh:!0}},{path:"pages/training/publicCourses",style:{enablePullDownRefresh:!0}},{path:"pages/training/testAnswer"},{path:"pages/training/testResult"},{path:"pages/training/certificate"},{path:"pages/training/publicCourseDetail"}]},{root:"pages_learning",pages:[{path:"pages/learning/artInfo"},{path:"pages/learning/industryNews"}]},{root:"pages_remote",pages:[{path:"pages/remote/list"},{path:"pages/remote/meeting"},{path:"pages/remote/remoteClinicList"}]},{root:"pages_user",pages:[{path:"pages/user/boundEnterprise"},{path:"pages/user/h5login"},{path:"pages/user/ppe"},{path:"pages/user/disease"},{path:"pages/user/blacklist"},{path:"pages/user/info"},{path:"pages/user/wjdc/list"},{path:"pages/user/wjdc/add"},{path:"pages/user/wjdc/detail"},{path:"pages/user/modify"},{path:"pages/user/modifyPhone"},{path:"pages/user/comment"},{path:"pages/user/myTraining"},{path:"pages/identify/index"},{path:"pages/identify/apply"},{path:"pages/identify/jgForm"},{path:"pages/identify/jgDetail"},{path:"pages/identify/jdResult"},{path:"pages/identify/jdProcessFile"},{path:"pages/user/complaints/list",style:{softinputMode:"adjustResize",enablePullDownRefresh:!0}},{path:"pages/user/signImg",style:{enablePullDownRefresh:!1,navigationStyle:"custom",pageOrientation:"landscape"}},{path:"pages/user/signature",style:{enablePullDownRefresh:!1,navigationStyle:"custom",pageOrientation:"landscape"}},{path:"pages/user/checkDetail",style:{enablePullDownRefresh:!1,navigationStyle:"custom"}},{path:"pages/user/ppeSign",style:{enablePullDownRefresh:!1,navigationStyle:"custom",pageOrientation:"landscape"}},{path:"pages/user/complaints/detail",style:{softinputMode:"adjustResize"}},{path:"pages/user/complaints/scoring"},{path:"pages/user/physicalExamination"},{path:"pages/user/occupationalHistory"},{path:"pages/user/physicalAndCheckResult"},{path:"pages/user/diseasesOverhaul"},{path:"pages/user/diseasesOverhaulDetail"},{path:"pages/user/message"},{path:"pages/user/violationInfo"},{path:"pages/user/questionnaire"},{path:"pages/user/questionnaireDetail"},{path:"pages/user/tjAppointmentInfo"},{path:"pages/user/tjAppointmentDetailInfo"},{path:"pages/user/employeeList"},{path:"pages/user/addItems"},{path:"pages/user/indicatorsTrend"},{path:"pages/eHealthRecord/index"},{path:"pages/eHealthRecord/auth"},{path:"pages/eHealthRecord/complaint"},{path:"pages/eHealthRecord/basicInfo"},{path:"pages/eHealthRecord/exam"},{path:"pages/eHealthRecord/diagnose"},{path:"pages/eHealthRecord/injury"},{path:"pages/eHealthRecord/healthManage"},{path:"pages/eHealthRecord/servic"},{path:"pages/eHealthRecord/report"},{path:"pages/eHealthRecord/warning"},{path:"pages/eHealthRecord/harmFactor"},{path:"pages/workInjury/query"}]},{root:"pages_lifeCycle",pages:[{path:"/pages/followUp/followUp",style:{navigationBarTitleText:"随访记录"}},{path:"/pages/Appointment/Appointment",style:{navigationBarTitleText:"就诊预约"}},{path:"/pages/Appointment/AppointmentRecord",style:{navigationBarTitleText:"预约记录"}},{path:"/pages/MedicationServices/MedicationGuidance",style:{navigationBarTitleText:"用药服务"}},{path:"/pages/MedicationServices/ApplyMedicationGuidance",style:{navigationBarTitleText:"申请用药指导"}},{path:"/pages/MedicationServices/MedicationGuidanceDetail",style:{navigationBarTitleText:"用药指导详情"}},{path:"/pages/MedicationServices/MedicationScheme",style:{navigationBarTitleText:"用药方案"}},{path:"/pages/MedicationServices/MedicationGuidanceInfo",style:{navigationBarTitleText:"用药指导资讯"}},{path:"/pages/MedicationServices/MedicationGuidanceInfoDetail",style:{navigationBarTitleText:"用药指导资讯详情"}},{path:"/pages/treatmentService/treatmentService",style:{navigationBarTitleText:"诊疗服务"}},{path:"/pages/treatmentService/treatmentServiceInfo",style:{navigationBarTitleText:"诊疗详情"}},{path:"/pages/recoveredServices/recoveredServices",style:{navigationBarTitleText:"康复指导服务"}},{path:"/pages/recoveredServices/addrecoveredServices",style:{navigationBarTitleText:"申请康复指导"}},{path:"/pages/recoveredServices/recoveredServicesDetail",style:{navigationBarTitleText:"康复指导申请详情"}},{path:"/pages/recoveredServices/downRecoveredServices",style:{navigationBarTitleText:"下载康复指导记录"}},{path:"/pages/recoveredServices/recoveredServicesRecord",style:{navigationBarTitleText:"服务记录"}}]}],globalStyle:{"mp-360":{navigationStyle:"custom"},"mp-alipay":{transparentTitle:"always",allowsBounceVertical:"NO"},navigationStyle:"custom",navigationBarTextStyle:"black","app-plus":{scrollIndicator:"none",bounce:"none"}},usingComponts:!0,easycom:{custom:{autoscan:!1,"grace(.*)":"@/graceUI/components/grace$1.vue"}},condition:{current:0,list:[{name:"",path:"",query:""}]}}},"9ddd":function(e,t,n){"use strict";var a=n("4468"),r=n.n(a);r.a},"9e43":function(e,t,n){"use strict";n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("0518")),i={getExperts:function(e){return(0,r.default)({url:"manage/consultation/getExperts",method:"get",data:e})},reserveList:function(e){return(0,r.default)({url:"manage/consultation/reserveList",method:"get",data:e})},getAgoraConfig:function(e){return(0,r.default)({url:"manage/consultation/getAgoraConfig",method:"get",data:e})},reserveRemote:function(e){return(0,r.default)({url:"manage/consultation/reserveRemote",method:"post",data:e})},loginVerification:function(e){return(0,r.default)({url:"manage/user/loginVerification",method:"get",data:e})},deleteReserve:function(e){return(0,r.default)({url:"manage/consultation/deleteReserve",method:"post",data:e})}};t.default=i},a1ef:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"grace-dialog-shade",style:{backgroundColor:e.background,zIndex:e.zIndex,height:e.showIn?"100%":"0px"},on:{touchmove:function(t){t.stopPropagation(),t.preventDefault(),arguments[0]=t=e.$handleEvent(t),e.stopFun.apply(void 0,arguments)},click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.closeDialogByShade.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"grace-dialog",class:[e.showIn?"grace-shade-in":"grace-shade-out"],style:{width:e.width,borderRadius:e.borderRadius},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.stopFun.apply(void 0,arguments)}}},[e.isTitle?n("v-uni-view",{staticClass:"grace-dialog-title",style:{fontSize:e.titleSize,color:e.titleColor,fontWeight:e.titleWeight?"bold":"",background:e.titleBg,lineHeight:e.titleHeight}},[e._v(e._s(e.title))]):e._e(),n("v-uni-view",{staticClass:"grace-dialog-content",on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.stopFun.apply(void 0,arguments)}}},[e._t("content")],2),e.isCloseBtn?n("v-uni-view",{staticClass:"grace-dialog-close-btn",style:{color:e.closeBtnColor,zIndex:e.zIndex+1},on:{click:function(t){t.stopPropagation(),arguments[0]=t=e.$handleEvent(t),e.closeDialog.apply(void 0,arguments)}}}):e._e(),e.isBtns?n("v-uni-view",[e._t("btns")],2):e._e()],1)],1)},r=[]},a332:function(e,t,n){"use strict";(function(e){n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa"),n("aa77"),n("bf0f"),n("2797"),n("5c47"),n("a1c1"),n("23f4"),n("7d2f"),n("9c4e"),n("ab80"),n("5ef2"),n("c223");var a={name:"uni-data-select",mixins:[e.mixinDatacom||{}],props:{localdata:{type:Array,default:function(){return[]}},value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"请选择"},emptyTips:{type:String,default:"无选项"},clear:{type:Boolean,default:!0},defItem:{type:Number,default:0},disabled:{type:Boolean,default:!1},format:{type:String,default:""},placement:{type:String,default:"bottom"}},data:function(){return{showSelector:!1,current:"",mixinDatacomResData:[],apps:[],channels:[],cacheKey:"uni-data-select-lastSelectedValue"}},created:function(){var e=this;this.debounceGet=this.debounce((function(){e.query()}),300),this.collection&&!this.localdata.length&&this.debounceGet()},computed:{typePlaceholder:function(){var e=this.placeholder,t={"opendb-stat-app-versions":"版本","opendb-app-channels":"渠道","opendb-app-list":"应用"}[this.collection];return t?e+t:e},valueCom:function(){return this.value},textShow:function(){var e=this.current;return e},getOffsetByPlacement:function(){switch(this.placement){case"top":return"bottom:calc(100% + 12px);";case"bottom":return"top:calc(100% + 12px);"}}},watch:{localdata:{immediate:!0,handler:function(e,t){Array.isArray(e)&&t!==e&&(this.mixinDatacomResData=e)}},valueCom:function(e,t){this.initDefVal()},mixinDatacomResData:{immediate:!0,handler:function(e){e.length&&this.initDefVal()}}},methods:{debounce:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,n=null;return function(){for(var a=this,r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];n&&clearTimeout(n),n=setTimeout((function(){e.apply(a,i)}),t)}},query:function(){this.mixinDatacomEasyGet()},onMixinDatacomPropsChange:function(){this.collection&&this.debounceGet()},initDefVal:function(){var e="";if(!this.valueCom&&0!==this.valueCom||this.isDisabled(this.valueCom)){var t;if(this.collection&&(t=this.getCache()),t||0===t)e=t;else{var n="";this.defItem>0&&this.defItem<=this.mixinDatacomResData.length&&(n=this.mixinDatacomResData[this.defItem-1].value),e=n}(e||0===e)&&this.emit(e)}else e=this.valueCom;var a=this.mixinDatacomResData.find((function(t){return t.value===e}));this.current=a?this.formatItemName(a):""},isDisabled:function(e){var t=!1;return this.mixinDatacomResData.forEach((function(n){n.value===e&&(t=n.disable)})),t},clearVal:function(){this.emit(""),this.collection&&this.removeCache()},change:function(e){e.disable||(this.showSelector=!1,this.current=this.formatItemName(e),this.emit(e.value))},emit:function(e){this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("change",e),this.collection&&this.setCache(e)},toggleSelector:function(){this.disabled||(this.showSelector=!this.showSelector)},formatItemName:function(e){var t=e.text,n=e.value,a=e.channel_code;if(a=a?"(".concat(a,")"):"",this.format){var r="";for(var i in r=this.format,e)r=r.replace(new RegExp("{".concat(i,"}"),"g"),e[i]);return r}return this.collection.indexOf("app-list")>0?"".concat(t,"(").concat(n,")"):t||"未命名".concat(a)},getLoadData:function(){return this.mixinDatacomResData},getCurrentCacheKey:function(){return this.collection},getCache:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getCurrentCacheKey(),t=uni.getStorageSync(this.cacheKey)||{};return t[e]},setCache:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getCurrentCacheKey(),n=uni.getStorageSync(this.cacheKey)||{};n[t]=e,uni.setStorageSync(this.cacheKey,n)},removeCache:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getCurrentCacheKey(),t=uni.getStorageSync(this.cacheKey)||{};delete t[e],uni.setStorageSync(this.cacheKey,t)}}};t.default=a}).call(this,n("861b")["uniCloud"])},a377:function(e,t,n){var a=n("8b7e");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var r=n("967d").default;r("466551a0",a,!0,{sourceMap:!1,shadowMode:!1})},a859:function(e,t,n){"use strict";(function(e){n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("bf0f"),n("d4b5"),n("7a76"),n("c9b5"),n("18f7"),n("de6c"),n("dc89"),n("2425"),n("8f71"),n("64aa"),n("2797");var r=a(n("2634")),i=a(n("2fdc")),o=a(n("6fb7")),s=a(n("9e43")),u=a(n("b7af")),c=function(){return"https://xjbthxzdbackend.jkqy.cn/"},l=function(){var t=(0,i.default)((0,r.default)().mark((function t(n){var a,i,o,s,u;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.log("downloadFile",n),!n){t.next=28;break}return a=n.split("/").pop(),t.prev=3,i="".concat(c(),"uploadMass/downloadFileBypath"),t.next=7,fetch(i,{method:"post",body:JSON.stringify({filePath:n}),headers:{"Content-Type":"application/json"}});case 7:if(o=t.sent,e.log("response",o),o.ok){t.next=11;break}throw new Error("下载失败");case 11:return t.next=13,o.blob();case 13:s=t.sent,u=document.createElement("a"),u.href=window.URL.createObjectURL(s),u.download=a,u.style.display="none",document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(u.href),t.next=28;break;case 24:t.prev=24,t.t0=t["catch"](3),e.error("下载失败:",t.t0),uni.showToast({title:"下载失败",icon:"none"});case 28:case"end":return t.stop()}}),t,null,[[3,24]])})));return function(e){return t.apply(this,arguments)}}(),d={data:function(){return{userinfo:"",poisonName:"",poisonTime:"",symptoms:"",remoteDate:"",selectValue:"",selectName:"",selectDoctor:"",selectDoctorName:"",experts:[],doctorList:[],info:[],history:[],recordFiles:[],uploadOptions:{url:"".concat(c(),"uploadMass/uploadNormalFile")}}},methods:{change:function(e){var t=this;this.selectName=this.experts.filter((function(e){return e.id===t.selectValue}))[0].name},change2:function(e){var t=this;this.selectDoctorName=this.doctorList.filter((function(e){return e._id===t.selectDoctor}))[0].name},goMeeting:function(e){uni.navigateTo({url:"/pages_remote/pages/remote/meeting?id=".concat(e)})},confirmReserve:function(){var e=this;return(0,i.default)((0,r.default)().mark((function t(){var n;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,s.default.reserveRemote({patientName:e.userinfo.name,patientId:e.userinfo._id,patientUnit:e.userinfo.company,patientPhone:e.userinfo.phoneNum,patientAge:e.userinfo.age,idNumber:e.userinfo.idNo,status:0,poisonName:e.poisonName,symptoms:e.symptoms,poisonTime:e.poisonTime,doctorId:e.selectDoctor,doctorName:e.selectDoctorName,expertId:String(e.selectValue),expertName:e.selectName,startTime:e.remoteDate,gender:Number(e.userinfo.gender),recordFiles:e.$refs.UploadDemo.fileList});case 2:if(n=t.sent,400!==n.data.code){t.next=6;break}return uni.showToast({title:n.data.message,icon:"none",mask:!0}),t.abrupt("return");case 6:e.$refs.fullNoticeDialog.hide(),uni.showToast({title:"已预约会诊",icon:"success",mask:!0}),e.getTableList(),e.poisonName="",e.symptoms="",e.poisonTime="",e.remoteDate="";case 13:case"end":return t.stop()}}),t)})))()},reserve:function(){this.$refs.fullNoticeDialog.open()},closeDialog2:function(){this.$refs.fullNoticeDialog.hide()},getTableList:function(){var e=this;return(0,i.default)((0,r.default)().mark((function t(){var n;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,s.default.reserveList({patientIdCard:e.userinfo.idNo});case 2:n=t.sent,e.history=n.data.filter((function(e){return 2===e.status})),e.info=n.data.filter((function(e){return 0===e.status||1===e.status}));case 5:case"end":return t.stop()}}),t)})))()},deleteMeeting:function(e){var t=this;uni.showModal({title:"提示",content:"确定删除该预约吗？",success:function(){var n=(0,i.default)((0,r.default)().mark((function n(a){var i;return(0,r.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!a.confirm){n.next=5;break}return n.next=3,s.default.deleteReserve({id:e});case 3:i=n.sent,200===i.status&&(uni.showToast({title:"删除成功",icon:"success",mask:!0}),t.getTableList());case 5:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()})},downloadReportFile:function(t){t.reportFileUrl?(e.log("downloadReportFile",t),l(t.reportFileUrl)):uni.showToast({title:"暂无报告文件",icon:"none",mask:!0})}},components:{graceDialog:o.default,UploadDemo:u.default},mounted:function(){var e=this;return(0,i.default)((0,r.default)().mark((function t(){var n,a;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,s.default.getExperts();case 2:return n=t.sent,e.experts=n.data.exportList,e.experts.forEach((function(e){e.value=e.id,e.text=e.name})),e.doctorList=n.data.doctorList,e.doctorList.forEach((function(e){e.value=e._id,e.text=e.name})),t.next=9,s.default.loginVerification();case 9:a=t.sent,e.userinfo=a.data,e.getTableList();case 12:case"end":return t.stop()}}),t)})))()}};t.default=d}).call(this,n("ba7c")["default"])},ab74:function(e,t,n){"use strict";n.r(t);var a=n("1edf"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},ac3f:function(e,t,n){"use strict";var a=n("a377"),r=n.n(a);r.a},acb1:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,a.default)(e,t)},n("7a76"),n("c9b5"),n("6a54");var a=function(e){return e&&e.__esModule?e:{default:e}}(n("e668"))},ad35:function(e,t,n){"use strict";n.r(t);var a=n("a859"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},b7af:function(e,t,n){"use strict";n.r(t);var a=n("0019"),r=n("46b3e");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("de54");var o=n("828b"),s=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,"00dc1734",null,!1,a["a"],void 0);t["default"]=s.exports},c40a:function(e,t,n){var a=n("c86c");t=a(!1),t.push([e.i,'.grace-dialog-shade[data-v-a889d8f4]{position:fixed;width:100%;height:100%;overflow:hidden;left:0;top:0;bottom:0;z-index:9991;display:flex;justify-content:center;align-items:center}.grace-dialog[data-v-a889d8f4]{width:%?580?%;background:#fff;position:relative;transition:all .2s linear 0s}.grace-dialog-title[data-v-a889d8f4]{line-height:%?100?%;font-size:%?30?%;text-align:center}.grace-dialog-content[data-v-a889d8f4]{transition:all .2s linear 0s}.grace-dialog-close-btn[data-v-a889d8f4]{position:absolute;z-index:9993;right:0;top:0;font-size:%?30?%;width:%?80?%;height:%?80?%;line-height:%?80?%;text-align:center;font-family:grace-iconfont}.grace-dialog-close-btn[data-v-a889d8f4]:before{content:"\\e632"}.grace-shade-in[data-v-a889d8f4]{-webkit-animation:grace-shade-in-a-data-v-a889d8f4 .2s linear forwards;animation:grace-shade-in-a-data-v-a889d8f4 .2s linear forwards}@-webkit-keyframes grace-shade-in-a-data-v-a889d8f4{0%{-webkit-transform:scale(.1);transform:scale(.1);opacity:0}100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}}@keyframes grace-shade-in-a-data-v-a889d8f4{0%{-webkit-transform:scale(.1);transform:scale(.1);opacity:0}100%{-webkit-transform:scale(1);transform:scale(1);opacity:1}}.grace-shade-out[data-v-a889d8f4]{-webkit-animation:grace-shade-out-a-data-v-a889d8f4 .2s ease-out forwards;animation:grace-shade-out-a-data-v-a889d8f4 .2s ease-out forwards}@-webkit-keyframes grace-shade-out-a-data-v-a889d8f4{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}100%{-webkit-transform:scale(.1);transform:scale(.1);opacity:0}}@keyframes grace-shade-out-a-data-v-a889d8f4{0%{-webkit-transform:scale(1);transform:scale(1);opacity:1}100%{-webkit-transform:scale(.1);transform:scale(.1);opacity:0}}',""]),e.exports=t},cad9:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,r.default)();return function(){var n,r=(0,a.default)(e);if(t){var o=(0,a.default)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return(0,i.default)(this,n)}},n("6a88"),n("bf0f"),n("7996");var a=o(n("f1f8")),r=o(n("6c31")),i=o(n("62b0"));function o(e){return e&&e.__esModule?e:{default:e}}},d2c4:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=o,n("6a88"),n("bf0f"),n("7996"),n("aa9c");var a=i(n("e668")),r=i(n("6c31"));function i(e){return e&&e.__esModule?e:{default:e}}function o(e,n,i){return(0,r.default)()?t.default=o=Reflect.construct.bind():t.default=o=function(e,t,n){var r=[null];r.push.apply(r,t);var i=Function.bind.apply(e,r),o=new i;return n&&(0,a.default)(o,n.prototype),o},o.apply(null,arguments)}},d432:function(e,t,n){"use strict";n.r(t);var a=n("562b"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},d441:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return-1!==Function.toString.call(e).indexOf("[native code]")},n("5ef2"),n("c9b5"),n("bf0f"),n("ab80")},d9d2:function(e,t,n){"use strict";n.r(t);var a=n("506a"),r=n("ad35");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("8746");var o=n("828b"),s=Object(o["a"])(r["default"],a["b"],a["c"],!1,null,"2c5ce6d8",null,!1,a["a"],void 0);t["default"]=s.exports},dbad:function(e,t,n){var a=n("c86c");t=a(!1),t.push([e.i,"uni-view[data-v-2cf78b47], uni-scroll-view[data-v-2cf78b47], uni-swiper-item[data-v-2cf78b47]{display:flex;flex-direction:column;flex-shrink:0;flex-grow:0;flex-basis:auto;align-items:stretch;align-content:flex-start}.u-tag-wrapper[data-v-2cf78b47]{position:relative}.u-tag[data-v-2cf78b47]{display:flex;flex-direction:row;align-items:center;border-style:solid}.u-tag--circle[data-v-2cf78b47]{border-radius:100px}.u-tag--square[data-v-2cf78b47]{border-radius:3px}.u-tag__icon[data-v-2cf78b47]{margin-right:4px}.u-tag__text--mini[data-v-2cf78b47]{font-size:12px;line-height:12px}.u-tag__text--medium[data-v-2cf78b47]{font-size:13px;line-height:13px}.u-tag__text--large[data-v-2cf78b47]{font-size:15px;line-height:15px}.u-tag--mini[data-v-2cf78b47]{height:22px;line-height:22px;padding:0 5px}.u-tag--medium[data-v-2cf78b47]{height:26px;line-height:22px;padding:0 10px}.u-tag--large[data-v-2cf78b47]{height:32px;line-height:32px;padding:0 15px}.u-tag--primary[data-v-2cf78b47]{background-color:#3c9cff;border-width:1px;border-color:#3c9cff}.u-tag--primary--plain[data-v-2cf78b47]{border-width:1px;border-color:#3c9cff}.u-tag--primary--plain--fill[data-v-2cf78b47]{background-color:#ecf5ff}.u-tag__text--primary[data-v-2cf78b47]{color:#fff}.u-tag__text--primary--plain[data-v-2cf78b47]{color:#3c9cff}.u-tag--error[data-v-2cf78b47]{background-color:#f56c6c;border-width:1px;border-color:#f56c6c}.u-tag--error--plain[data-v-2cf78b47]{border-width:1px;border-color:#f56c6c}.u-tag--error--plain--fill[data-v-2cf78b47]{background-color:#fef0f0}.u-tag__text--error[data-v-2cf78b47]{color:#fff}.u-tag__text--error--plain[data-v-2cf78b47]{color:#f56c6c}.u-tag--warning[data-v-2cf78b47]{background-color:#f9ae3d;border-width:1px;border-color:#f9ae3d}.u-tag--warning--plain[data-v-2cf78b47]{border-width:1px;border-color:#f9ae3d}.u-tag--warning--plain--fill[data-v-2cf78b47]{background-color:#fdf6ec}.u-tag__text--warning[data-v-2cf78b47]{color:#fff}.u-tag__text--warning--plain[data-v-2cf78b47]{color:#f9ae3d}.u-tag--success[data-v-2cf78b47]{background-color:#5ac725;border-width:1px;border-color:#5ac725}.u-tag--success--plain[data-v-2cf78b47]{border-width:1px;border-color:#5ac725}.u-tag--success--plain--fill[data-v-2cf78b47]{background-color:#f5fff0}.u-tag__text--success[data-v-2cf78b47]{color:#fff}.u-tag__text--success--plain[data-v-2cf78b47]{color:#5ac725}.u-tag--info[data-v-2cf78b47]{background-color:#909399;border-width:1px;border-color:#909399}.u-tag--info--plain[data-v-2cf78b47]{border-width:1px;border-color:#909399}.u-tag--info--plain--fill[data-v-2cf78b47]{background-color:#f4f4f5}.u-tag__text--info[data-v-2cf78b47]{color:#fff}.u-tag__text--info--plain[data-v-2cf78b47]{color:#909399}.u-tag__close[data-v-2cf78b47]{position:absolute;z-index:999;top:10px;right:10px;border-radius:100px;background-color:#c6c7cb;display:flex;flex-direction:row;align-items:center;justify-content:center;-webkit-transform:scale(.6) translate(80%,-80%);transform:scale(.6) translate(80%,-80%)}.u-tag__close--mini[data-v-2cf78b47]{width:18px;height:18px}.u-tag__close--medium[data-v-2cf78b47]{width:22px;height:22px}.u-tag__close--large[data-v-2cf78b47]{width:25px;height:25px}",""]),e.exports=t},de54:function(e,t,n){"use strict";var a=n("f40a"),r=n.n(a);r.a},dec0:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("64aa");var a={name:"graceDialog",props:{show:{type:Boolean,default:!1},width:{type:String,default:"580rpx"},isCloseBtn:{type:Boolean,default:!0},closeBtnColor:{type:String,default:"#FF0036"},isTitle:{type:Boolean,default:!0},title:{type:String,default:""},titleBg:{type:String,default:""},titleHeight:{type:String,default:"100rpx"},titleWeight:{type:Boolean,default:!0},titleSize:{type:String,default:"28rpx"},titleColor:{type:String,default:"#333333"},isBtns:{type:Boolean,default:!0},background:{type:String,default:"rgba(0, 0, 0, 0.5)"},borderRadius:{type:String,default:"6rpx"},zIndex:{type:Number,default:999},canCloseByShade:{type:Boolean,default:!0}},data:function(){return{showIn:!1}},created:function(){this.showIn=this.show},watch:{show:function(e){e?this.open():this.hide()}},methods:{closeDialogByShade:function(){this.canCloseByShade&&this.closeDialog()},closeDialog:function(){this.$emit("closeDialog")},stopFun:function(){},open:function(){this.showIn=!0},hide:function(){this.showIn=!1}}};t.default=a},e668:function(e,t,n){"use strict";function a(e,n){return t.default=a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,n)}n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=a,n("8a8d")},f0e4:function(e,t,n){"use strict";n("6a54");var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("08eb"),n("18f7"),n("de6c"),n("bf0f"),n("4626"),n("5ac7"),n("aa9c");var r=a(n("2634")),i=a(n("5de6")),o=a(n("2fdc")),s=n("4352"),u=n("7529"),c={data:function(){return{id:0,uploadOptions:{}}},methods:{renderProps:function(e){var t=this,n=e.id,a=e.renderInput,r=e.upload;a&&(this.id=n,this.uploadOptions=r,this.$nextTick((function(){var e,a=document.getElementById("xe-upload-".concat(n));a.addEventListener("change",(function(){t.handleUpload()})),null===a||void 0===a||null===(e=a.click)||void 0===e||e.call(a)})))},handleUpload:function(){var e=this;return(0,o.default)((0,r.default)().mark((function t(){var n,a,o,c,l,d,f,p,h,g,v,m,y,b,w,_,x;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=e.uploadOptions||{},a=n.url,o=n.name,c=n.header,l=void 0===c?{}:c,d=n.formData,f=void 0===d?{}:d,p=document.getElementById("xe-upload-".concat(e.id)),p.files[0]){t.next=4;break}return t.abrupt("return");case 4:h=Array.from(p.files),g=[],v=(0,r.default)().mark((function t(n){var c,d,v,m,y,b,w,_,x,k;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(c=h[n],d="file",c.type.includes("image")&&(d="image"),c.type.includes("video")&&(d="video"),v={size:c.size,name:c.name,type:c.type,fileType:d,tempFilePath:"",base64Url:""},a){t.next=15;break}return t.next=8,(0,u.awaitWrap)((0,u.fileToBase64)(p.files[n]));case 8:return m=t.sent,y=(0,i.default)(m,2),b=y[0],w=y[1],b||(v.base64Url=w),g.push(v),t.abrupt("return","continue");case 15:for(x in _=new FormData,_.append(o,p.files[n],c.name),f)_.append(x,f[x]);k=function(t){if(t.lengthComputable){var a=t.loaded/t.total*100;e.handleRenderEmits({type:"onprogress",data:{progress:Math.floor(a),current:n+1,total:h.length}})}},g.push((0,s.appUploadFile)({url:a,header:l,formData:_},v,k));case 21:case"end":return t.stop()}}),t)})),m=0;case 8:if(!(m<h.length)){t.next=16;break}return t.delegateYield(v(m),"t0",10);case 10:if(y=t.t0,"continue"!==y){t.next=13;break}return t.abrupt("continue",13);case 13:m+=1,t.next=8;break;case 16:if(a){t.next=18;break}return t.abrupt("return",e.handleRenderEmits({type:"choose",data:g}));case 18:return e.handleRenderEmits({type:"onprogress",data:{progress:0,current:1,total:h.length}}),t.next=21,(0,u.awaitWrap)(Promise.all(g));case 21:if(b=t.sent,w=(0,i.default)(b,2),_=w[0],x=w[1],!_){t.next=27;break}return t.abrupt("return",e.handleRenderEmits({type:"warning",data:_}));case 27:e.handleRenderEmits({type:"success",data:x});case 28:case"end":return t.stop()}}),t)})))()},handleRenderEmits:function(e){this.$ownerInstance.callMethod("handleEmits",e)}}};t.default=c},f17e:function(e,t,n){var a=n("f283");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var r=n("967d").default;r("58082b22",a,!0,{sourceMap:!1,shadowMode:!1})},f1f8:function(e,t,n){"use strict";function a(e){return t.default=a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},a(e)}n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=a,n("8a8d"),n("926e")},f283:function(e,t,n){var a=n("c86c");t=a(!1),t.push([e.i,".flex[data-v-2c5ce6d8]{display:flex}.space-between[data-v-2c5ce6d8]{justify-content:space-between}.flex1[data-v-2c5ce6d8]{flex:1}",""]),e.exports=t},f40a:function(e,t,n){var a=n("2f17");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var r=n("967d").default;r("c5f94b6a",a,!0,{sourceMap:!1,shadowMode:!1})},f478:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},n("7a76"),n("c9b5")},f555:function(e,t,n){"use strict";var a=n("85c1"),r=n("ab4a"),i=n("e4ca"),o=n("471d"),s=n("af9e"),u=a.RegExp,c=u.prototype,l=r&&s((function(){var e=!0;try{u(".","d")}catch(l){e=!1}var t={},n="",a=e?"dgimsy":"gimsy",r=function(e,a){Object.defineProperty(t,e,{get:function(){return n+=a,!0}})},i={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var o in e&&(i.hasIndices="d"),i)r(o,i[o]);var s=Object.getOwnPropertyDescriptor(c,"flags").get.call(t);return s!==a||n!==a}));l&&i(c,"flags",{configurable:!0,get:o})}}]);