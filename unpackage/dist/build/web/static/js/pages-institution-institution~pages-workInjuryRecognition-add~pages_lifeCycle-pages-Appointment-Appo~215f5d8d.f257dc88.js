(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-institution-institution~pages-workInjuryRecognition-add~pages_lifeCycle-pages-Appointment-Appo~215f5d8d"],{"1bab":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={appid:"__UNI__5F78BD0"}},"20f3":function(e,t,n){"use strict";var r=n("8bdb"),a=n("5145");r({target:"Array",proto:!0,forced:a!==[].lastIndexOf},{lastIndexOf:a})},4085:function(e,t,n){"use strict";var r=n("8bdb"),a=n("85c1");r({global:!0,forced:a.globalThis!==a},{globalThis:a})},"62b0":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t&&("object"===(0,r.default)(t)||"function"===typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return(0,a.default)(e)},n("7a76"),n("c9b5");var r=i(n("fcf3")),a=i(n("f478"));function i(e){return e&&e.__esModule?e:{default:e}}},"68ef":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=u,n("c1a3"),n("bf0f"),n("18f7"),n("de6c"),n("7a76"),n("c9b5");var r=o(n("f1f8")),a=o(n("e668")),i=o(n("d441")),s=o(n("d2c4"));function o(e){return e&&e.__esModule?e:{default:e}}function u(e){var n="function"===typeof Map?new Map:void 0;return t.default=u=function(e){if(null===e||!(0,i.default)(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof n){if(n.has(e))return n.get(e);n.set(e,t)}function t(){return(0,s.default)(e,arguments,(0,r.default)(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),(0,a.default)(t,e)},u(e)}},"6a88":function(e,t,n){"use strict";var r=n("8bdb"),a=n("6aa6"),i=n("9f9e"),s=n("8598"),o=n("5ee2"),u=n("e7e3"),c=n("1c06"),l=n("e37c"),f=n("af9e"),d=a("Reflect","construct"),h=Object.prototype,p=[].push,g=f((function(){function e(){}return!(d((function(){}),[],e)instanceof e)})),v=!f((function(){d((function(){}))})),m=g||v;r({target:"Reflect",stat:!0,forced:m,sham:m},{construct:function(e,t){o(e),u(t);var n=arguments.length<3?e:o(arguments[2]);if(v&&!g)return d(e,t,n);if(e===n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var r=[null];return i(p,r,t),new(i(s,e,r))}var a=n.prototype,f=l(c(a)?a:h),m=i(e,f,t);return c(m)?m:f}})},"6c31":function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}},n("bf0f"),n("7996"),n("6a88")},7996:function(e,t,n){"use strict";var r=n("8bdb"),a=n("85c1"),i=n("181d");r({global:!0},{Reflect:{}}),i(a.Reflect,"Reflect",!0)},8598:function(e,t,n){"use strict";var r=n("bb80"),a=n("7992"),i=n("1c06"),s=n("338c"),o=n("37ad"),u=n("8f26"),c=Function,l=r([].concat),f=r([].join),d={},h=function(e,t,n){if(!s(d,t)){for(var r=[],a=0;a<t;a++)r[a]="a["+a+"]";d[t]=c("C,a","return new C("+f(r,",")+")")}return d[t](e,n)};e.exports=u?c.bind:function(e){var t=a(this),n=t.prototype,r=o(arguments,1),s=function(){var n=l(r,o(arguments));return this instanceof s?h(t,n.length,n):t.apply(e,n)};return i(n)&&(s.prototype=n),s}},"861b":function(e,t,n){"use strict";(function(e,r){var a=n("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.uniCloud=t.default=t.UniCloudError=void 0;var i=a(n("f478")),s=a(n("5de6")),o=a(n("fcf3")),u=a(n("b7c7")),c=a(n("3471")),l=a(n("2634")),f=a(n("2fdc")),d=a(n("9b1b")),h=a(n("acb1")),p=a(n("cad9")),g=a(n("68ef")),v=a(n("80b1")),m=a(n("efe5"));n("4085"),n("7a76"),n("c9b5"),n("bf0f"),n("ab80"),n("f7a5"),n("aa9c"),n("e966"),n("c223"),n("dd2b"),n("5ef2"),n("2797"),n("dc8a"),n("473f"),n("4626"),n("5ac7"),n("4100"),n("5c47"),n("d4b5"),n("0c26"),n("0506"),n("fd3c"),n("6a54"),n("a1c1"),n("de6c"),n("c1a3"),n("18f7"),n("af8f"),n("64aa"),n("8f71"),n("23f4"),n("7d2f"),n("9c4e"),n("4db2"),n("c976"),n("4d8f"),n("7b97"),n("668a"),n("c5b7"),n("8ff5"),n("2378"),n("641a"),n("64e0"),n("cce3"),n("efba"),n("d009"),n("bd7d"),n("7edd"),n("d798"),n("f547"),n("5e54"),n("b60a"),n("8c18"),n("12973"),n("f991"),n("198e"),n("8557"),n("63b1"),n("1954"),n("1cf1"),n("01a2"),n("e39c"),n("e062"),n("aa77"),n("2c10"),n("f555"),n("dc69"),n("9370"),n("6730"),n("08eb"),n("15d1"),n("d5c6"),n("5a56"),n("f074"),n("20f3");var y=a(n("9572"));function k(e,t,n){return e(n={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&n.path)}},n.exports),n.exports}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof e||"undefined"!=typeof self&&self;var _=k((function(e,t){var n;e.exports=(n=n||function(e,t){var n=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),r={},a=r.lib={},i=a.Base={extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},s=a.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,a=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<a;i++){var s=n[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=s<<24-(r+i)%4*8}else for(i=0;i<a;i+=4)t[r+i>>>2]=n[i>>>2];return this.sigBytes+=a,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n,r=[],a=function(t){var n=987654321,r=4294967295;return function(){var a=((n=36969*(65535&n)+(n>>16)&r)<<16)+(t=18e3*(65535&t)+(t>>16)&r)&r;return a/=4294967296,(a+=.5)*(e.random()>.5?1:-1)}},i=0;i<t;i+=4){var o=a(4294967296*(n||e.random()));n=987654071*o(),r.push(4294967296*o()|0)}return new s.init(r,t)}}),o=r.enc={},u=o.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],a=0;a<n;a++){var i=t[a>>>2]>>>24-a%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new s.init(n,t/2)}},c=o.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],a=0;a<n;a++){var i=t[a>>>2]>>>24-a%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new s.init(n,t)}},l=o.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},f=a.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new s.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,a=n.sigBytes,i=this.blockSize,o=a/(4*i),u=(o=t?e.ceil(o):e.max((0|o)-this._minBufferSize,0))*i,c=e.min(4*u,a);if(u){for(var l=0;l<u;l+=i)this._doProcessBlock(r,l);var f=r.splice(0,u);n.sigBytes-=c}return new s.init(f,c)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});a.Hasher=f.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new d.HMAC.init(e,n).finalize(t)}}});var d=r.algo={};return r}(Math),n)})),w=_,b=(k((function(e,t){var n;e.exports=(n=w,function(e){var t=n,r=t.lib,a=r.WordArray,i=r.Hasher,s=t.algo,o=[];!function(){for(var t=0;t<64;t++)o[t]=4294967296*e.abs(e.sin(t+1))|0}();var u=s.MD5=i.extend({_doReset:function(){this._hash=new a.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,a=e[r];e[r]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}var i=this._hash.words,s=e[t+0],u=e[t+1],h=e[t+2],p=e[t+3],g=e[t+4],v=e[t+5],m=e[t+6],y=e[t+7],k=e[t+8],_=e[t+9],w=e[t+10],b=e[t+11],x=e[t+12],I=e[t+13],S=e[t+14],T=e[t+15],P=i[0],A=i[1],O=i[2],E=i[3];P=c(P,A,O,E,s,7,o[0]),E=c(E,P,A,O,u,12,o[1]),O=c(O,E,P,A,h,17,o[2]),A=c(A,O,E,P,p,22,o[3]),P=c(P,A,O,E,g,7,o[4]),E=c(E,P,A,O,v,12,o[5]),O=c(O,E,P,A,m,17,o[6]),A=c(A,O,E,P,y,22,o[7]),P=c(P,A,O,E,k,7,o[8]),E=c(E,P,A,O,_,12,o[9]),O=c(O,E,P,A,w,17,o[10]),A=c(A,O,E,P,b,22,o[11]),P=c(P,A,O,E,x,7,o[12]),E=c(E,P,A,O,I,12,o[13]),O=c(O,E,P,A,S,17,o[14]),P=l(P,A=c(A,O,E,P,T,22,o[15]),O,E,u,5,o[16]),E=l(E,P,A,O,m,9,o[17]),O=l(O,E,P,A,b,14,o[18]),A=l(A,O,E,P,s,20,o[19]),P=l(P,A,O,E,v,5,o[20]),E=l(E,P,A,O,w,9,o[21]),O=l(O,E,P,A,T,14,o[22]),A=l(A,O,E,P,g,20,o[23]),P=l(P,A,O,E,_,5,o[24]),E=l(E,P,A,O,S,9,o[25]),O=l(O,E,P,A,p,14,o[26]),A=l(A,O,E,P,k,20,o[27]),P=l(P,A,O,E,I,5,o[28]),E=l(E,P,A,O,h,9,o[29]),O=l(O,E,P,A,y,14,o[30]),P=f(P,A=l(A,O,E,P,x,20,o[31]),O,E,v,4,o[32]),E=f(E,P,A,O,k,11,o[33]),O=f(O,E,P,A,b,16,o[34]),A=f(A,O,E,P,S,23,o[35]),P=f(P,A,O,E,u,4,o[36]),E=f(E,P,A,O,g,11,o[37]),O=f(O,E,P,A,y,16,o[38]),A=f(A,O,E,P,w,23,o[39]),P=f(P,A,O,E,I,4,o[40]),E=f(E,P,A,O,s,11,o[41]),O=f(O,E,P,A,p,16,o[42]),A=f(A,O,E,P,m,23,o[43]),P=f(P,A,O,E,_,4,o[44]),E=f(E,P,A,O,x,11,o[45]),O=f(O,E,P,A,T,16,o[46]),P=d(P,A=f(A,O,E,P,h,23,o[47]),O,E,s,6,o[48]),E=d(E,P,A,O,y,10,o[49]),O=d(O,E,P,A,S,15,o[50]),A=d(A,O,E,P,v,21,o[51]),P=d(P,A,O,E,x,6,o[52]),E=d(E,P,A,O,p,10,o[53]),O=d(O,E,P,A,w,15,o[54]),A=d(A,O,E,P,u,21,o[55]),P=d(P,A,O,E,k,6,o[56]),E=d(E,P,A,O,T,10,o[57]),O=d(O,E,P,A,m,15,o[58]),A=d(A,O,E,P,I,21,o[59]),P=d(P,A,O,E,g,6,o[60]),E=d(E,P,A,O,b,10,o[61]),O=d(O,E,P,A,h,15,o[62]),A=d(A,O,E,P,_,21,o[63]),i[0]=i[0]+P|0,i[1]=i[1]+A|0,i[2]=i[2]+O|0,i[3]=i[3]+E|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,a=8*t.sigBytes;n[a>>>5]|=128<<24-a%32;var i=e.floor(r/4294967296),s=r;n[15+(a+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(a+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),t.sigBytes=4*(n.length+1),this._process();for(var o=this._hash,u=o.words,c=0;c<4;c++){var l=u[c];u[c]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return o},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,n,r,a,i,s){var o=e+(t&n|~t&r)+a+s;return(o<<i|o>>>32-i)+t}function l(e,t,n,r,a,i,s){var o=e+(t&r|n&~r)+a+s;return(o<<i|o>>>32-i)+t}function f(e,t,n,r,a,i,s){var o=e+(t^n^r)+a+s;return(o<<i|o>>>32-i)+t}function d(e,t,n,r,a,i,s){var o=e+(n^(t|~r))+a+s;return(o<<i|o>>>32-i)+t}t.MD5=i._createHelper(u),t.HmacMD5=i._createHmacHelper(u)}(Math),n.MD5)})),k((function(e,t){var n;e.exports=(n=w,void function(){var e=n,t=e.lib.Base,r=e.enc.Utf8;e.algo.HMAC=t.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,a=4*n;t.sigBytes>a&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),s=this._iKey=t.clone(),o=i.words,u=s.words,c=0;c<n;c++)o[c]^=1549556828,u[c]^=909522486;i.sigBytes=s.sigBytes=a,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})}())})),k((function(e,t){e.exports=w.HmacMD5}))),x=k((function(e,t){e.exports=w.enc.Utf8})),I=k((function(e,t){var n;e.exports=(n=w,function(){var e=n,t=e.lib.WordArray;function r(e,n,r){for(var a=[],i=0,s=0;s<n;s++)if(s%4){var o=r[e.charCodeAt(s-1)]<<s%4*2,u=r[e.charCodeAt(s)]>>>6-s%4*2;a[i>>>2]|=(o|u)<<24-i%4*8,i++}return t.create(a,i)}e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var a=[],i=0;i<n;i+=3)for(var s=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,o=0;o<4&&i+.75*o<n;o++)a.push(r.charAt(s>>>6*(3-o)&63));var u=r.charAt(64);if(u)for(;a.length%4;)a.push(u);return a.join("")},parse:function(e){var t=e.length,n=this._map,a=this._reverseMap;if(!a){a=this._reverseMap=[];for(var i=0;i<n.length;i++)a[n.charCodeAt(i)]=i}var s=n.charAt(64);if(s){var o=e.indexOf(s);-1!==o&&(t=o)}return r(e,t,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),n.enc.Base64)})),S="uni_id_token",T="uni_id_token_expired",P={DEFAULT:"FUNCTION",FUNCTION:"FUNCTION",OBJECT:"OBJECT",CLIENT_DB:"CLIENT_DB"},A="pending",O="fulfilled",E="rejected";function C(e){return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}function R(e){return"object"===C(e)}function L(e){return"function"==typeof e}function N(e){return function(){try{return e.apply(e,arguments)}catch(e){r.error(e)}}}var U="REJECTED",D="NOT_PENDING",M=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.createPromise,r=t.retryRule,a=void 0===r?U:r;(0,v.default)(this,e),this.createPromise=n,this.status=null,this.promise=null,this.retryRule=a}return(0,m.default)(e,[{key:"needRetry",get:function(){if(!this.status)return!0;switch(this.retryRule){case U:return this.status===E;case D:return this.status!==A}}},{key:"exec",value:function(){var e=this;return this.needRetry?(this.status=A,this.promise=this.createPromise().then((function(t){return e.status=O,Promise.resolve(t)}),(function(t){return e.status=E,Promise.reject(t)})),this.promise):this.promise}}]),e}(),j=function(){function e(){(0,v.default)(this,e),this._callback={}}return(0,m.default)(e,[{key:"addListener",value:function(e,t){this._callback[e]||(this._callback[e]=[]),this._callback[e].push(t)}},{key:"on",value:function(e,t){return this.addListener(e,t)}},{key:"removeListener",value:function(e,t){if(!t)throw new Error('The "listener" argument must be of type function. Received undefined');var n=this._callback[e];if(n){var r=function(e,t){for(var n=e.length-1;n>=0;n--)if(e[n]===t)return n;return-1}(n,t);n.splice(r,1)}}},{key:"off",value:function(e,t){return this.removeListener(e,t)}},{key:"removeAllListener",value:function(e){delete this._callback[e]}},{key:"emit",value:function(e){for(var t=this._callback[e],n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];if(t)for(var i=0;i<t.length;i++)t[i].apply(t,r)}}]),e}();function q(e){return e&&"string"==typeof e?JSON.parse(e):e}var F=q([]),B="web",K=(q(void 0),q([])||[]);try{(n("1bab").default||n("1bab")).appid}catch(_r){}var H,J={};function W(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=J,n=e,Object.prototype.hasOwnProperty.call(t,n)||(J[e]=r),J[e]}"app"===B&&(J=uni._globalUniCloudObj?uni._globalUniCloudObj:uni._globalUniCloudObj={});var V=["invoke","success","fail","complete"],z=W("_globalUniCloudInterceptor");function $(e,t){z[e]||(z[e]={}),R(t)&&Object.keys(t).forEach((function(n){V.indexOf(n)>-1&&function(e,t,n){var r=z[e][t];r||(r=z[e][t]=[]),-1===r.indexOf(n)&&L(n)&&r.push(n)}(e,n,t[n])}))}function G(e,t){z[e]||(z[e]={}),R(t)?Object.keys(t).forEach((function(n){V.indexOf(n)>-1&&function(e,t,n){var r=z[e][t];if(r){var a=r.indexOf(n);a>-1&&r.splice(a,1)}}(e,n,t[n])})):delete z[e]}function Q(e,t){return e&&0!==e.length?e.reduce((function(e,n){return e.then((function(){return n(t)}))}),Promise.resolve()):Promise.resolve()}function Y(e,t){return z[e]&&z[e][t]||[]}function X(e){$("callObject",e)}var Z=W("_globalUniCloudListener"),ee={RESPONSE:"response",NEED_LOGIN:"needLogin",REFRESH_TOKEN:"refreshToken"},te={CLIENT_DB:"clientdb",CLOUD_FUNCTION:"cloudfunction",CLOUD_OBJECT:"cloudobject"};function ne(e){return Z[e]||(Z[e]=[]),Z[e]}function re(e,t){var n=ne(e);n.includes(t)||n.push(t)}function ae(e,t){var n=ne(e),r=n.indexOf(t);-1!==r&&n.splice(r,1)}function ie(e,t){for(var n=ne(e),r=0;r<n.length;r++)(0,n[r])(t)}var se,oe=!1;function ue(){return se||(se=new Promise((function(e){oe&&e(),function t(){if("function"==typeof getCurrentPages){var n=getCurrentPages();n&&n[0]&&(oe=!0,e())}oe||setTimeout((function(){t()}),30)}()})),se)}function ce(e){var t={};for(var n in e){var r=e[n];L(r)&&(t[n]=N(r))}return t}var le=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(e){var r;(0,v.default)(this,n);var a=e.message||e.errMsg||"unknown system error";return r=t.call(this,a),r.errMsg=a,r.code=r.errCode=e.code||e.errCode||"SYSTEM_ERROR",r.errSubject=r.subject=e.subject||e.errSubject,r.cause=e.cause,r.requestId=e.requestId,r}return(0,m.default)(n,[{key:"toJson",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!(e>=10))return e++,{errCode:this.errCode,errMsg:this.errMsg,errSubject:this.errSubject,cause:this.cause&&this.cause.toJson?this.cause.toJson(e):this.cause}}}]),n}((0,g.default)(Error));t.UniCloudError=le;var fe,de,he={request:function(e){return uni.request(e)},uploadFile:function(e){return uni.uploadFile(e)},setStorageSync:function(e,t){return uni.setStorageSync(e,t)},getStorageSync:function(e){return uni.getStorageSync(e)},removeStorageSync:function(e){return uni.removeStorageSync(e)},clearStorageSync:function(){return uni.clearStorageSync()},connectSocket:function(e){return uni.connectSocket(e)}};function pe(){return{token:he.getStorageSync(S)||he.getStorageSync("uniIdToken"),tokenExpired:he.getStorageSync(T)}}function ge(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.token,n=e.tokenExpired;t&&he.setStorageSync(S,t),n&&he.setStorageSync(T,n)}function ve(){return fe||(fe="mp-weixin"===B&&wx.canIUse("getAppBaseInfo")&&wx.canIUse("getDeviceInfo")?(0,d.default)((0,d.default)({},uni.getAppBaseInfo()),uni.getDeviceInfo()):uni.getSystemInfoSync()),fe}var me={};function ye(){var e=uni.getLocale&&uni.getLocale()||"en";if(de)return(0,d.default)((0,d.default)((0,d.default)({},me),de),{},{locale:e,LOCALE:e});var t=ve(),n=t.deviceId,r=t.osName,a=t.uniPlatform,i=t.appId,s=["appId","appLanguage","appName","appVersion","appVersionCode","appWgtVersion","browserName","browserVersion","deviceBrand","deviceId","deviceModel","deviceType","osName","osVersion","romName","romVersion","ua","hostName","hostVersion","uniPlatform","uniRuntimeVersion","uniRuntimeVersionCode","uniCompilerVersion","uniCompilerVersionCode"];for(var o in t)Object.hasOwnProperty.call(t,o)&&-1===s.indexOf(o)&&delete t[o];return de=(0,d.default)((0,d.default)({PLATFORM:a,OS:r,APPID:i,DEVICEID:n},function(){var e,t;try{if(uni.getLaunchOptionsSync){if(uni.getLaunchOptionsSync.toString().indexOf("not yet implemented")>-1)return;var n=uni.getLaunchOptionsSync(),r=n.scene,a=n.channel;e=a,t=r}}catch(e){}return{channel:e,scene:t}}()),t),(0,d.default)((0,d.default)((0,d.default)({},me),de),{},{locale:e,LOCALE:e})}var ke,_e={sign:function(e,t){var n="";return Object.keys(e).sort().forEach((function(t){e[t]&&(n=n+"&"+t+"="+e[t])})),n=n.slice(1),b(n,t).toString()},wrappedRequest:function(e,t){return new Promise((function(n,r){t(Object.assign(e,{complete:function(e){e||(e={});var t=e.data&&e.data.header&&e.data.header["x-serverless-request-id"]||e.header&&e.header["request-id"];if(!e.statusCode||e.statusCode>=400){var a=e.data&&e.data.error&&e.data.error.code||"SYS_ERR",i=e.data&&e.data.error&&e.data.error.message||e.errMsg||"request:fail";return r(new le({code:a,message:i,requestId:t}))}var s=e.data;if(s.error)return r(new le({code:s.error.code,message:s.error.message,requestId:t}));s.result=s.data,s.requestId=t,delete s.data,n(s)}}))}))},toBase64:function(e){return I.stringify(x.parse(e))}},we=function(){function e(t){var n=this;(0,v.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),this.config=Object.assign({},{endpoint:0===t.spaceId.indexOf("mp-")?"https://api.next.bspapp.com":"https://api.bspapp.com"},t),this.config.provider="aliyun",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.config.accessTokenKey="access_token_"+this.config.spaceId,this.adapter=he,this._getAccessTokenPromiseHub=new M({createPromise:function(){return n.requestAuth(n.setupRequest({method:"serverless.auth.user.anonymousAuthorize",params:"{}"},"auth")).then((function(e){if(!e.result||!e.result.accessToken)throw new le({code:"AUTH_FAILED",message:"获取accessToken失败"});n.setAccessToken(e.result.accessToken)}))},retryRule:D})}return(0,m.default)(e,[{key:"hasAccessToken",get:function(){return!!this.accessToken}},{key:"setAccessToken",value:function(e){this.accessToken=e}},{key:"requestWrapped",value:function(e){return _e.wrappedRequest(e,this.adapter.request)}},{key:"requestAuth",value:function(e){return this.requestWrapped(e)}},{key:"request",value:function(e,t){var n=this;return Promise.resolve().then((function(){return n.hasAccessToken?t?n.requestWrapped(e):n.requestWrapped(e).catch((function(t){return new Promise((function(e,n){!t||"GATEWAY_INVALID_TOKEN"!==t.code&&"InvalidParameter.InvalidToken"!==t.code?n(t):e()})).then((function(){return n.getAccessToken()})).then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))})):n.getAccessToken().then((function(){var t=n.rebuildRequest(e);return n.request(t,!0)}))}))}},{key:"rebuildRequest",value:function(e){var t=Object.assign({},e);return t.data.token=this.accessToken,t.header["x-basement-token"]=this.accessToken,t.header["x-serverless-sign"]=_e.sign(t.data,this.config.clientSecret),t}},{key:"setupRequest",value:function(e,t){var n=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),r={"Content-Type":"application/json"};return"auth"!==t&&(n.token=this.accessToken,r["x-basement-token"]=this.accessToken),r["x-serverless-sign"]=_e.sign(n,this.config.clientSecret),{url:this.config.requestUrl,method:"POST",data:n,dataType:"json",header:r}}},{key:"getAccessToken",value:function(){return this._getAccessTokenPromiseHub.exec()}},{key:"authorize",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getAccessToken();case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request((0,d.default)((0,d.default)({},this.setupRequest(t)),{},{timeout:e.timeout}))}},{key:"getOSSUploadOptionsFromPath",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFileToOSS",value:function(e){var t=this,n=e.url,r=e.formData,a=e.name,i=e.filePath,s=e.fileType,o=e.onUploadProgress;return new Promise((function(e,u){var c=t.adapter.uploadFile({url:n,formData:r,name:a,filePath:i,fileType:s,header:{"X-OSS-server-side-encrpytion":"AES256"},success:function(t){t&&t.statusCode<400?e(t):u(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){u(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof o&&c&&"function"==typeof c.onProgressUpdate&&c.onProgressUpdate((function(e){o({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"reportOSSUpload",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(this.setupRequest(t))}},{key:"uploadFile",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,s,o,u,c,f,d,h,p,g,v,m,y,k,_,w,b,x,I;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,a=t.fileType,i=void 0===a?"image":a,s=t.cloudPathAsRealPath,o=void 0!==s&&s,u=t.onUploadProgress,c=t.config,"string"===C(r)){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(r=r.trim()){e.next=5;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(r)){e.next=7;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:if(f=c&&c.envType||this.config.envType,!(o&&("/"!==r[0]&&(r="/"+r),r.indexOf("\\")>-1))){e.next=10;break}throw new le({code:"INVALID_PARAM",message:"使用cloudPath作为路径时，cloudPath不可包含“\\”"});case 10:return e.next=12,this.getOSSUploadOptionsFromPath({env:f,filename:o?r.split("/").pop():r,fileId:o?r:void 0});case 12:return d=e.sent.result,h="https://"+d.cdnDomain+"/"+d.ossPath,p=d.securityToken,g=d.accessKeyId,v=d.signature,m=d.host,y=d.ossPath,k=d.id,_=d.policy,w=d.ossCallbackUrl,b={"Cache-Control":"max-age=2592000","Content-Disposition":"attachment",OSSAccessKeyId:g,Signature:v,host:m,id:k,key:y,policy:_,success_action_status:200},p&&(b["x-oss-security-token"]=p),w&&(x=JSON.stringify({callbackUrl:w,callbackBody:JSON.stringify({fileId:k,spaceId:this.config.spaceId}),callbackBodyType:"application/json"}),b.callback=_e.toBase64(x)),I={url:"https://"+d.host,formData:b,fileName:"file",name:"file",filePath:n,fileType:i},e.next=27,this.uploadFileToOSS(Object.assign({},I,{onUploadProgress:u}));case 27:if(!w){e.next=29;break}return e.abrupt("return",{success:!0,filePath:n,fileID:h});case 29:return e.next=31,this.reportOSSUpload({id:k});case 31:if(!e.sent.success){e.next=33;break}return e.abrupt("return",{success:!0,filePath:n,fileID:h});case 33:throw new le({code:"UPLOAD_FAILED",message:"文件上传失败"});case 34:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.fileList;return new Promise((function(t,r){Array.isArray(n)&&0!==n.length||r(new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"})),e.getFileInfo({fileList:n}).then((function(e){t({fileList:n.map((function(t,n){var r=e.fileList[n];return{fileID:t,tempFileURL:r&&r.url||t}}))})}))}))}},{key:"getFileInfo",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a.length>0&&void 0!==a[0]?a[0]:{},n=t.fileList,Array.isArray(n)&&0!==n.length){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});case 3:return r={method:"serverless.file.resource.info",params:JSON.stringify({id:n.map((function(e){return e.split("?")[0]})).join(",")})},e.next=6,this.request(this.setupRequest(r));case 6:return e.t0=e.sent.result,e.abrupt("return",{fileList:e.t0});case 8:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),be={init:function(e){var t=new we(e),n={signInAnonymously:function(){return t.authorize()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},xe="undefined"!=typeof location&&"http:"===location.protocol?"http:":"https:";!function(e){e.local="local",e.none="none",e.session="session"}(ke||(ke={}));var Ie,Se=function(){},Te=k((function(e,t){var n;e.exports=(n=w,function(e){var t=n,r=t.lib,a=r.WordArray,i=r.Hasher,s=t.algo,o=[],u=[];!function(){function t(t){for(var n=e.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,a=0;a<64;)t(r)&&(a<8&&(o[a]=n(e.pow(r,.5))),u[a]=n(e.pow(r,1/3)),a++),r++}();var c=[],l=s.SHA256=i.extend({_doReset:function(){this._hash=new a.init(o.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],a=n[1],i=n[2],s=n[3],o=n[4],l=n[5],f=n[6],d=n[7],h=0;h<64;h++){if(h<16)c[h]=0|e[t+h];else{var p=c[h-15],g=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,v=c[h-2],m=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;c[h]=g+c[h-7]+m+c[h-16]}var y=r&a^r&i^a&i,k=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),_=d+((o<<26|o>>>6)^(o<<21|o>>>11)^(o<<7|o>>>25))+(o&l^~o&f)+u[h]+c[h];d=f,f=l,l=o,o=s+_|0,s=i,i=a,a=r,r=_+(k+y)|0}n[0]=n[0]+r|0,n[1]=n[1]+a|0,n[2]=n[2]+i|0,n[3]=n[3]+s|0,n[4]=n[4]+o|0,n[5]=n[5]+l|0,n[6]=n[6]+f|0,n[7]=n[7]+d|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,a=8*t.sigBytes;return n[a>>>5]|=128<<24-a%32,n[14+(a+64>>>9<<4)]=e.floor(r/4294967296),n[15+(a+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=i.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=i._createHelper(l),t.HmacSHA256=i._createHmacHelper(l)}(Math),n.SHA256)})),Pe=Te,Ae=k((function(e,t){e.exports=w.HmacSHA256})),Oe=function(){var e;if(!Promise){e=function(){},e.promise={};var t=function(){throw new le({message:'Your Node runtime does support ES6 Promises. Set "global.Promise" to your preferred implementation of promises.'})};return Object.defineProperty(e.promise,"then",{get:t}),Object.defineProperty(e.promise,"catch",{get:t}),e}var n=new Promise((function(t,n){e=function(e,r){return e?n(e):t(r)}}));return e.promise=n,e};function Ee(e){return void 0===e}function Ce(e){return"[object Null]"===Object.prototype.toString.call(e)}function Re(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/([\s\S]+)\s+(请前往云开发AI小助手查看问题：.*)/,"$1")}function Le(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:32,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n="",r=0;r<e;r++)n+=t.charAt(Math.floor(62*Math.random()));return n}!function(e){e.WEB="web",e.WX_MP="wx_mp"}(Ie||(Ie={}));var Ne={adapter:null,runtime:void 0},Ue=["anonymousUuidKey"],De=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){var e;return(0,v.default)(this,n),e=t.call(this),Ne.adapter.root.tcbObject||(Ne.adapter.root.tcbObject={}),e}return(0,m.default)(n,[{key:"setItem",value:function(e,t){Ne.adapter.root.tcbObject[e]=t}},{key:"getItem",value:function(e){return Ne.adapter.root.tcbObject[e]}},{key:"removeItem",value:function(e){delete Ne.adapter.root.tcbObject[e]}},{key:"clear",value:function(){delete Ne.adapter.root.tcbObject}}]),n}(Se);function Me(e,t){switch(e){case"local":return t.localStorage||new De;case"none":return new De;default:return t.sessionStorage||new De}}var je=function(){function e(t){if((0,v.default)(this,e),!this._storage){this._persistence=Ne.adapter.primaryStorage||t.persistence,this._storage=Me(this._persistence,Ne.adapter);var n="access_token_".concat(t.env),r="access_token_expire_".concat(t.env),a="refresh_token_".concat(t.env),i="anonymous_uuid_".concat(t.env),s="login_type_".concat(t.env),o="token_type_".concat(t.env),u="user_info_".concat(t.env);this.keys={accessTokenKey:n,accessTokenExpireKey:r,refreshTokenKey:a,anonymousUuidKey:i,loginTypeKey:s,userInfoKey:u,deviceIdKey:"device_id",tokenTypeKey:o}}}return(0,m.default)(e,[{key:"updatePersistence",value:function(e){if(e!==this._persistence){var t="local"===this._persistence;this._persistence=e;var n=Me(e,Ne.adapter);for(var r in this.keys){var a=this.keys[r];if(!t||!Ue.includes(r)){var i=this._storage.getItem(a);Ee(i)||Ce(i)||(n.setItem(a,i),this._storage.removeItem(a))}}this._storage=n}}},{key:"setStore",value:function(e,t,n){if(this._storage){var r={version:n||"localCachev1",content:t},a=JSON.stringify(r);try{this._storage.setItem(e,a)}catch(e){throw e}}}},{key:"getStore",value:function(e,t){try{if(!this._storage)return}catch(e){return""}t=t||"localCachev1";var n=this._storage.getItem(e);return n&&n.indexOf(t)>=0?JSON.parse(n).content:""}},{key:"removeStore",value:function(e){this._storage.removeItem(e)}}]),e}(),qe={},Fe={};function Be(e){return qe[e]}var Ke=(0,m.default)((function e(t,n){(0,v.default)(this,e),this.data=n||null,this.name=t})),He=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(e,r){var a;return(0,v.default)(this,n),a=t.call(this,"error",{error:e,data:r}),a.error=e,a}return(0,m.default)(n)}(Ke),Je=new(function(){function e(){(0,v.default)(this,e),this._listeners={}}return(0,m.default)(e,[{key:"on",value:function(e,t){return function(e,t,n){n[e]=n[e]||[],n[e].push(t)}(e,t,this._listeners),this}},{key:"off",value:function(e,t){return function(e,t,n){if(n&&n[e]){var r=n[e].indexOf(t);-1!==r&&n[e].splice(r,1)}}(e,t,this._listeners),this}},{key:"fire",value:function(e,t){if(e instanceof He)return r.error(e.error),this;var n="string"==typeof e?new Ke(e,t||{}):e,a=n.name;if(this._listens(a)){n.target=this;var i,s=this._listeners[a]?(0,u.default)(this._listeners[a]):[],o=(0,c.default)(s);try{for(o.s();!(i=o.n()).done;){var l=i.value;l.call(this,n)}}catch(f){o.e(f)}finally{o.f()}}return this}},{key:"_listens",value:function(e){return this._listeners[e]&&this._listeners[e].length>0}}]),e}());function We(e,t){Je.on(e,t)}function Ve(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};Je.fire(e,t)}function ze(e,t){Je.off(e,t)}var $e,Ge="loginStateChanged",Qe="loginStateExpire",Ye="loginTypeChanged",Xe="anonymousConverted",Ze="refreshAccessToken";!function(e){e.ANONYMOUS="ANONYMOUS",e.WECHAT="WECHAT",e.WECHAT_PUBLIC="WECHAT-PUBLIC",e.WECHAT_OPEN="WECHAT-OPEN",e.CUSTOM="CUSTOM",e.EMAIL="EMAIL",e.USERNAME="USERNAME",e.NULL="NULL"}($e||($e={}));var et=function(){function e(){(0,v.default)(this,e),this._fnPromiseMap=new Map}return(0,m.default)(e,[{key:"run",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=this._fnPromiseMap.get(t),e.abrupt("return",(r||(r=new Promise(function(){var e=(0,f.default)((0,l.default)().mark((function e(r,i){var s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,a._runIdlePromise();case 3:return s=n(),e.t0=r,e.next=7,s;case 7:e.t1=e.sent,(0,e.t0)(e.t1),e.next=14;break;case 11:e.prev=11,e.t2=e["catch"](0),i(e.t2);case 14:return e.prev=14,a._fnPromiseMap.delete(t),e.finish(14);case 17:case"end":return e.stop()}}),e,null,[[0,11,14,17]])})));return function(t,n){return e.apply(this,arguments)}}()),this._fnPromiseMap.set(t,r)),r));case 2:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_runIdlePromise",value:function(){return Promise.resolve()}}]),e}(),tt=function(){function e(t){(0,v.default)(this,e),this._singlePromise=new et,this._cache=Be(t.env),this._baseURL="https://".concat(t.env,".ap-shanghai.tcb-api.tencentcloudapi.com"),this._reqClass=new Ne.adapter.reqClass({timeout:t.timeout,timeoutMsg:"请求在".concat(t.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]})}return(0,m.default)(e,[{key:"_getDeviceId",value:function(){if(this._deviceID)return this._deviceID;var e=this._cache.keys.deviceIdKey,t=this._cache.getStore(e);return"string"==typeof t&&t.length>=16&&t.length<=48||(t=Le(),this._cache.setStore(e,t)),this._deviceID=t,t}},{key:"_request",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a,i,s,o,u=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=u.length>2&&void 0!==u[2]?u[2]:{},a={"x-request-id":Le(),"x-device-id":this._getDeviceId()},!r.withAccessToken){e.next=9;break}return i=this._cache.keys.tokenTypeKey,e.next=6,this.getAccessToken();case 6:s=e.sent,o=this._cache.getStore(i),a.authorization="".concat(o," ").concat(s);case 9:return e.abrupt("return",this._reqClass["get"===r.method?"get":"post"]({url:"".concat(this._baseURL).concat(t),data:n,headers:a}));case 10:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"_fetchAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,s,o,u,c,d,h=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.loginTypeKey,r=t.accessTokenKey,a=t.accessTokenExpireKey,i=t.tokenTypeKey,s=this._cache.getStore(n),!s||s===$e.ANONYMOUS){e.next=3;break}throw new le({code:"INVALID_OPERATION",message:"非匿名登录不支持刷新 access token"});case 3:return e.next=5,this._singlePromise.run("fetchAccessToken",(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,h._request("/auth/v1/signin/anonymously",{},{method:"post"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)}))));case 5:return o=e.sent,u=o.access_token,c=o.expires_in,d=o.token_type,e.abrupt("return",(this._cache.setStore(i,d),this._cache.setStore(r,u),this._cache.setStore(a,Date.now()+1e3*c),u));case 10:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"isAccessTokenExpired",value:function(e,t){var n=!0;return e&&t&&(n=t<Date.now()),n}},{key:"getAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=this._cache.getStore(n),i=this._cache.getStore(r),e.abrupt("return",this.isAccessTokenExpired(a,i)?this._fetchAccessToken():a);case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.loginTypeKey,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,$e.ANONYMOUS),this.getAccessToken()));case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getUserInfo",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._singlePromise.run("getUserInfo",(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t._request("/auth/v1/user/me",{},{withAccessToken:!0,method:"get"});case 2:return e.abrupt("return",e.sent.data);case 3:case"end":return e.stop()}}),e)})))));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()}]),e}(),nt=["auth.getJwt","auth.logout","auth.signInWithTicket","auth.signInAnonymously","auth.signIn","auth.fetchAccessTokenWithRefreshToken","auth.signUpWithEmailAndPassword","auth.activateEndUserMail","auth.sendPasswordResetEmail","auth.resetPasswordWithToken","auth.isUsernameRegistered"],rt={"X-SDK-Version":"1.3.5"};function at(e,t,n){var r=e[t];e[t]=function(t){var a={},i={};n.forEach((function(n){var r=n.call(e,t),s=r.data,o=r.headers;Object.assign(a,s),Object.assign(i,o)}));var s=t.data;return s&&function(){var e;if(e=s,"[object FormData]"!==Object.prototype.toString.call(e))t.data=(0,d.default)((0,d.default)({},s),a);else for(var n in a)s.append(n,a[n])}(),t.headers=(0,d.default)((0,d.default)({},t.headers||{}),i),r.call(e,t)}}function it(){var e=Math.random().toString(16).slice(2);return{data:{seqId:e},headers:(0,d.default)((0,d.default)({},rt),{},{"x-seqid":e})}}var st=function(){function e(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,v.default)(this,e),this.config=n,this._reqClass=new Ne.adapter.reqClass({timeout:this.config.timeout,timeoutMsg:"请求在".concat(this.config.timeout/1e3,"s内未完成，已中断"),restrictedMethods:["post"]}),this._cache=Be(this.config.env),this._localCache=(t=this.config.env,Fe[t]),this.oauth=new tt(this.config),at(this._reqClass,"post",[it]),at(this._reqClass,"upload",[it]),at(this._reqClass,"download",[it])}return(0,m.default)(e,[{key:"post",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.post(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"upload",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.upload(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"download",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._reqClass.download(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refreshAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._refreshAccessTokenPromise||(this._refreshAccessTokenPromise=this._refreshAccessToken()),e.prev=1,e.next=4,this._refreshAccessTokenPromise;case 4:t=e.sent,e.next=10;break;case 7:e.prev=7,e.t0=e["catch"](1),n=e.t0;case 10:if(this._refreshAccessTokenPromise=null,this._shouldRefreshAccessTokenHook=null,!n){e.next=12;break}throw n;case 12:return e.abrupt("return",t);case 13:case"end":return e.stop()}}),e,this,[[1,7]])})));return function(){return e.apply(this,arguments)}}()},{key:"_refreshAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,s,o,u,c,f,d,h,p;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey,i=t.loginTypeKey,s=t.anonymousUuidKey,this._cache.removeStore(n),this._cache.removeStore(r),o=this._cache.getStore(a),o){e.next=5;break}throw new le({message:"未登录CloudBase"});case 5:return u={refresh_token:o},e.next=8,this.request("auth.fetchAccessTokenWithRefreshToken",u);case 8:if(c=e.sent,!c.data.code){e.next=21;break}if(f=c.data.code,"SIGN_PARAM_INVALID"!==f&&"REFRESH_TOKEN_EXPIRED"!==f&&"INVALID_REFRESH_TOKEN"!==f){e.next=20;break}if(this._cache.getStore(i)!==$e.ANONYMOUS||"INVALID_REFRESH_TOKEN"!==f){e.next=19;break}return d=this._cache.getStore(s),h=this._cache.getStore(a),e.next=17,this.send("auth.signInAnonymously",{anonymous_uuid:d,refresh_token:h});case 17:return p=e.sent,e.abrupt("return",(this.setRefreshToken(p.refresh_token),this._refreshAccessToken()));case 19:Ve(Qe),this._cache.removeStore(a);case 20:throw new le({code:c.data.code,message:"刷新access token失败：".concat(c.data.code)});case 21:if(!c.data.access_token){e.next=23;break}return e.abrupt("return",(Ve(Ze),this._cache.setStore(n,c.data.access_token),this._cache.setStore(r,c.data.access_token_expire+Date.now()),{accessToken:c.data.access_token,accessTokenExpire:c.data.access_token_expire}));case 23:c.data.refresh_token&&(this._cache.removeStore(a),this._cache.setStore(a,c.data.refresh_token),this._refreshAccessToken());case 24:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"getAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,s,o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey,this._cache.getStore(a)){e.next=3;break}throw new le({message:"refresh token不存在，登录状态异常"});case 3:if(i=this._cache.getStore(n),s=this._cache.getStore(r),o=!0,e.t0=this._shouldRefreshAccessTokenHook,!e.t0){e.next=9;break}return e.next=8,this._shouldRefreshAccessTokenHook(i,s);case 8:e.t0=!e.sent;case 9:if(e.t1=e.t0,!e.t1){e.next=12;break}o=!1;case 12:return e.abrupt("return",(!i||!s||s<Date.now())&&o?this.refreshAccessToken():{accessToken:i,accessTokenExpire:s});case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"request",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n,r){var a,i,s,o,u,c,f,h,p,g,v,m,y,k,_;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a="x-tcb-trace_".concat(this.config.env),i="application/x-www-form-urlencoded",s=(0,d.default)({action:t,env:this.config.env,dataVersion:"2019-08-16"},n),e.t0=-1===nt.indexOf(t),!e.t0){e.next=9;break}return this._cache.keys,e.next=8,this.oauth.getAccessToken();case 8:s.access_token=e.sent;case 9:if("storage.uploadFile"!==t){e.next=15;break}for(u in o=new FormData,o)o.hasOwnProperty(u)&&void 0!==o[u]&&o.append(u,s[u]);i="multipart/form-data",e.next=17;break;case 15:for(c in i="application/json",o={},s)void 0!==s[c]&&(o[c]=s[c]);case 17:return f={headers:{"content-type":i}},r&&r.timeout&&(f.timeout=r.timeout),r&&r.onUploadProgress&&(f.onUploadProgress=r.onUploadProgress),h=this._localCache.getStore(a),h&&(f.headers["X-TCB-Trace"]=h),p=n.parse,g=n.inQuery,v=n.search,m={env:this.config.env},p&&(m.parse=!0),g&&(m=(0,d.default)((0,d.default)({},g),m)),y=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=/\?/.test(t),a="";for(var i in n)""===a?!r&&(t+="?"):a+="&",a+="".concat(i,"=").concat(encodeURIComponent(n[i]));return/^http(s)?\:\/\//.test(t+=a)?t:"".concat(e).concat(t)}(xe,"//tcb-api.tencentcloudapi.com/web",m),v&&(y+=v),e.next=28,this.post((0,d.default)({url:y,data:o},f));case 28:if(k=e.sent,_=k.header&&k.header["x-tcb-trace"],_&&this._localCache.setStore(a,_),(200===Number(k.status)||200===Number(k.statusCode))&&k.data){e.next=32;break}throw new le({code:"NETWORK_ERROR",message:"network request error"});case 32:return e.abrupt("return",k);case 33:case"end":return e.stop()}}),e,this)})));return function(t,n,r){return e.apply(this,arguments)}}()},{key:"send",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,s=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=s.length>1&&void 0!==s[1]?s[1]:{},r=s.length>2&&void 0!==s[2]?s[2]:{},e.next=4,this.request(t,n,(0,d.default)((0,d.default)({},r),{},{onUploadProgress:n.onUploadProgress}));case 4:if(a=e.sent,"ACCESS_TOKEN_DISABLED"!==a.data.code&&"ACCESS_TOKEN_EXPIRED"!==a.data.code||-1!==nt.indexOf(t)){e.next=14;break}return e.next=8,this.oauth.refreshAccessToken();case 8:return e.next=10,this.request(t,n,(0,d.default)((0,d.default)({},r),{},{onUploadProgress:n.onUploadProgress}));case 10:if(i=e.sent,!i.data.code){e.next=13;break}throw new le({code:i.data.code,message:Re(i.data.message)});case 13:return e.abrupt("return",i.data);case 14:if(!a.data.code){e.next=16;break}throw new le({code:a.data.code,message:Re(a.data.message)});case 16:return e.abrupt("return",a.data);case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,e)}}]),e}(),ot={};function ut(e){return ot[e]}var ct=function(){function e(t){(0,v.default)(this,e),this.config=t,this._cache=Be(t.env),this._request=ut(t.env)}return(0,m.default)(e,[{key:"setRefreshToken",value:function(e){var t=this._cache.keys,n=t.accessTokenKey,r=t.accessTokenExpireKey,a=t.refreshTokenKey;this._cache.removeStore(n),this._cache.removeStore(r),this._cache.setStore(a,e)}},{key:"setAccessToken",value:function(e,t){var n=this._cache.keys,r=n.accessTokenKey,a=n.accessTokenExpireKey;this._cache.setStore(r,e),this._cache.setStore(a,t)}},{key:"refreshUserInfo",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getUserInfo",{});case 2:return t=e.sent,n=t.data,e.abrupt("return",(this.setLocalUserInfo(n),n));case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e)}}]),e}(),lt=function(){function e(t){if((0,v.default)(this,e),!t)throw new le({code:"PARAM_ERROR",message:"envId is not defined"});this._envId=t,this._cache=Be(this._envId),this._request=ut(this._envId),this.setUserInfo()}return(0,m.default)(e,[{key:"linkWithTicket",value:function(e){if("string"!=typeof e)throw new le({code:"PARAM_ERROR",message:"ticket must be string"});return this._request.send("auth.linkWithTicket",{ticket:e})}},{key:"linkWithRedirect",value:function(e){e.signInWithRedirect()}},{key:"updatePassword",value:function(e,t){return this._request.send("auth.updatePassword",{oldPassword:t,newPassword:e})}},{key:"updateEmail",value:function(e){return this._request.send("auth.updateEmail",{newEmail:e})}},{key:"updateUsername",value:function(e){if("string"!=typeof e)throw new le({code:"PARAM_ERROR",message:"username must be a string"});return this._request.send("auth.updateUsername",{username:e})}},{key:"getLinkedUidList",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.send("auth.getLinkedUidList",{});case 2:return t=e.sent,n=t.data,r=!1,a=n.users,e.abrupt("return",(a.forEach((function(e){e.wxOpenId&&e.wxPublicId&&(r=!0)})),{users:a,hasPrimaryUid:r}));case 7:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setPrimaryUid",value:function(e){return this._request.send("auth.setPrimaryUid",{uid:e})}},{key:"unlink",value:function(e){return this._request.send("auth.unlink",{platform:e})}},{key:"update",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,s,o,u,c;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.nickName,r=t.gender,a=t.avatarUrl,i=t.province,s=t.country,o=t.city,e.next=8,this._request.send("auth.updateUserInfo",{nickName:n,gender:r,avatarUrl:a,province:i,country:s,city:o});case 8:u=e.sent,c=u.data,this.setLocalUserInfo(c);case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"refresh",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.oauth.getUserInfo();case 2:return t=e.sent,e.abrupt("return",(this.setLocalUserInfo(t),t));case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"setUserInfo",value:function(){var e=this,t=this._cache.keys.userInfoKey,n=this._cache.getStore(t);["uid","loginType","openid","wxOpenId","wxPublicId","unionId","qqMiniOpenId","email","hasPassword","customUserId","nickName","gender","avatarUrl"].forEach((function(t){e[t]=n[t]})),this.location={country:n.country,province:n.province,city:n.city}}},{key:"setLocalUserInfo",value:function(e){var t=this._cache.keys.userInfoKey;this._cache.setStore(t,e),this.setUserInfo()}}]),e}(),ft=function(){function e(t){if((0,v.default)(this,e),!t)throw new le({code:"PARAM_ERROR",message:"envId is not defined"});this._cache=Be(t);var n=this._cache.keys,r=n.refreshTokenKey,a=n.accessTokenKey,i=n.accessTokenExpireKey,s=this._cache.getStore(r),o=this._cache.getStore(a),u=this._cache.getStore(i);this.credential={refreshToken:s,accessToken:o,accessTokenExpire:u},this.user=new lt(t)}return(0,m.default)(e,[{key:"isAnonymousAuth",get:function(){return this.loginType===$e.ANONYMOUS}},{key:"isCustomAuth",get:function(){return this.loginType===$e.CUSTOM}},{key:"isWeixinAuth",get:function(){return this.loginType===$e.WECHAT||this.loginType===$e.WECHAT_OPEN||this.loginType===$e.WECHAT_PUBLIC}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}}]),e}(),dt=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._cache.updatePersistence("local"),e.next=3,this._request.oauth.getAccessToken();case 3:return Ve(Ge),Ve(Ye,{env:this.config.env,loginType:$e.ANONYMOUS,persistence:"local"}),t=new ft(this.config.env),e.next=8,t.user.refresh();case 8:return e.abrupt("return",t);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,s,o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=this._cache.keys,r=n.anonymousUuidKey,a=n.refreshTokenKey,i=this._cache.getStore(r),s=this._cache.getStore(a),e.next=7,this._request.send("auth.linkAndRetrieveDataWithTicket",{anonymous_uuid:i,refresh_token:s,ticket:t});case 7:if(o=e.sent,!o.refresh_token){e.next=16;break}return this._clearAnonymousUUID(),this.setRefreshToken(o.refresh_token),e.next=13,this._request.refreshAccessToken();case 13:return Ve(Xe,{env:this.config.env}),Ve(Ye,{loginType:$e.CUSTOM,persistence:"local"}),e.abrupt("return",{credential:{refreshToken:o.refresh_token}});case 16:throw new le({message:"匿名转化失败"});case 17:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"_setAnonymousUUID",value:function(e){var t=this._cache.keys,n=t.anonymousUuidKey,r=t.loginTypeKey;this._cache.removeStore(n),this._cache.setStore(n,e),this._cache.setStore(r,$e.ANONYMOUS)}},{key:"_clearAnonymousUUID",value:function(){this._cache.removeStore(this._cache.keys.anonymousUuidKey)}}]),n}(ct),ht=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"ticket must be a string"});case 2:return n=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signInWithTicket",{ticket:t,refresh_token:this._cache.getStore(n)||""});case 5:if(r=e.sent,!r.refresh_token){e.next=15;break}return this.setRefreshToken(r.refresh_token),e.next=10,this._request.refreshAccessToken();case 10:return Ve(Ge),Ve(Ye,{env:this.config.env,loginType:$e.CUSTOM,persistence:this.config.persistence}),e.next=14,this.refreshUserInfo();case 14:return e.abrupt("return",new ft(this.config.env));case 15:throw new le({message:"自定义登录失败"});case 16:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),n}(ct),pt=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a,i,s,o;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"email must be a string"});case 2:return r=this._cache.keys.refreshTokenKey,e.next=5,this._request.send("auth.signIn",{loginType:"EMAIL",email:t,password:n,refresh_token:this._cache.getStore(r)||""});case 5:if(a=e.sent,i=a.refresh_token,s=a.access_token,o=a.access_token_expire,!i){e.next=22;break}if(this.setRefreshToken(i),!s||!o){e.next=15;break}this.setAccessToken(s,o),e.next=17;break;case 15:return e.next=17,this._request.refreshAccessToken();case 17:return e.next=19,this.refreshUserInfo();case 19:return Ve(Ge),Ve(Ye,{env:this.config.env,loginType:$e.EMAIL,persistence:this.config.persistence}),e.abrupt("return",new ft(this.config.env));case 22:throw a.code?new le({code:a.code,message:"邮箱登录失败: ".concat(a.message)}):new le({message:"邮箱登录失败"});case 23:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"activate",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.activateEndUserMail",{token:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"resetPasswordWithToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.resetPasswordWithToken",{token:t,newPassword:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(ct),gt=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"signIn",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var a,i,s,o,u;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"username must be a string"});case 2:return"string"!=typeof n&&(n="",r.warn("password is empty")),a=this._cache.keys.refreshTokenKey,e.next=6,this._request.send("auth.signIn",{loginType:$e.USERNAME,username:t,password:n,refresh_token:this._cache.getStore(a)||""});case 6:if(i=e.sent,s=i.refresh_token,o=i.access_token_expire,u=i.access_token,!s){e.next=23;break}if(this.setRefreshToken(s),!u||!o){e.next=16;break}this.setAccessToken(u,o),e.next=18;break;case 16:return e.next=18,this._request.refreshAccessToken();case 18:return e.next=20,this.refreshUserInfo();case 20:return Ve(Ge),Ve(Ye,{env:this.config.env,loginType:$e.USERNAME,persistence:this.config.persistence}),e.abrupt("return",new ft(this.config.env));case 23:throw i.code?new le({code:i.code,message:"用户名密码登录失败: ".concat(i.message)}):new le({message:"用户名密码登录失败"});case 24:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()}]),n}(ct),vt=function(){function e(t){(0,v.default)(this,e),this.config=t,this._cache=Be(t.env),this._request=ut(t.env),this._onAnonymousConverted=this._onAnonymousConverted.bind(this),this._onLoginTypeChanged=this._onLoginTypeChanged.bind(this),We(Ye,this._onLoginTypeChanged)}return(0,m.default)(e,[{key:"currentUser",get:function(){var e=this.hasLoginState();return e&&e.user||null}},{key:"loginType",get:function(){return this._cache.getStore(this._cache.keys.loginTypeKey)}},{key:"anonymousAuthProvider",value:function(){return new dt(this.config)}},{key:"customAuthProvider",value:function(){return new ht(this.config)}},{key:"emailAuthProvider",value:function(){return new pt(this.config)}},{key:"usernameAuthProvider",value:function(){return new gt(this.config)}},{key:"signInAnonymously",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new dt(this.config).signIn());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signInWithEmailAndPassword",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new pt(this.config).signIn(t,n));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"signInWithUsernameAndPassword",value:function(e,t){return new gt(this.config).signIn(e,t)}},{key:"linkAndRetrieveDataWithTicket",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this._anonymousAuthProvider||(this._anonymousAuthProvider=new dt(this.config)),We(Xe,this._onAnonymousConverted),e.next=3,this._anonymousAuthProvider.linkAndRetrieveDataWithTicket(t);case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"signOut",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.loginType!==$e.ANONYMOUS){e.next=2;break}throw new le({message:"匿名用户不支持登出操作"});case 2:if(t=this._cache.keys,n=t.refreshTokenKey,r=t.accessTokenKey,a=t.accessTokenExpireKey,i=this._cache.getStore(n),i){e.next=5;break}return e.abrupt("return");case 5:return e.next=7,this._request.send("auth.logout",{refresh_token:i});case 7:return s=e.sent,e.abrupt("return",(this._cache.removeStore(n),this._cache.removeStore(r),this._cache.removeStore(a),Ve(Ge),Ve(Ye,{env:this.config.env,loginType:$e.NULL,persistence:this.config.persistence}),s));case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"signUpWithEmailAndPassword",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.signUpWithEmailAndPassword",{email:t,password:n}));case 1:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"sendPasswordResetEmail",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this._request.send("auth.sendPasswordResetEmail",{email:t}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"onLoginStateChanged",value:function(e){var t=this;We(Ge,(function(){var n=t.hasLoginState();e.call(t,n)}));var n=this.hasLoginState();e.call(this,n)}},{key:"onLoginStateExpired",value:function(e){We(Qe,e.bind(this))}},{key:"onAccessTokenRefreshed",value:function(e){We(Ze,e.bind(this))}},{key:"onAnonymousConverted",value:function(e){We(Xe,e.bind(this))}},{key:"onLoginTypeChanged",value:function(e){var t=this;We(Ye,(function(){var n=t.hasLoginState();e.call(t,n)}))}},{key:"getAccessToken",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this._request.getAccessToken();case 2:return e.t0=e.sent.accessToken,e.t1=this.config.env,e.abrupt("return",{accessToken:e.t0,env:e.t1});case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"hasLoginState",value:function(){var e=this._cache.keys,t=e.accessTokenKey,n=e.accessTokenExpireKey,r=this._cache.getStore(t),a=this._cache.getStore(n);return this._request.oauth.isAccessTokenExpired(r,a)?null:new ft(this.config.env)}},{key:"isUsernameRegistered",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if("string"==typeof t){e.next=2;break}throw new le({code:"PARAM_ERROR",message:"username must be a string"});case 2:return e.next=4,this._request.send("auth.isUsernameRegistered",{username:t});case 4:return n=e.sent,r=n.data,e.abrupt("return",r&&r.isRegistered);case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getLoginState",value:function(){return Promise.resolve(this.hasLoginState())}},{key:"signInWithTicket",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",new ht(this.config).signIn(t));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"shouldRefreshAccessToken",value:function(e){this._request._shouldRefreshAccessTokenHook=e.bind(this)}},{key:"getUserInfo",value:function(){return this._request.send("auth.getUserInfo",{}).then((function(e){return e.code?e:(0,d.default)((0,d.default)({},e.data),{},{requestId:e.seqId})}))}},{key:"getAuthHeader",value:function(){var e=this._cache.keys,t=e.refreshTokenKey,n=e.accessTokenKey,r=this._cache.getStore(t);return{"x-cloudbase-credentials":this._cache.getStore(n)+"/@@/"+r}}},{key:"_onAnonymousConverted",value:function(e){var t=e.data.env;t===this.config.env&&this._cache.updatePersistence(this.config.persistence)}},{key:"_onLoginTypeChanged",value:function(e){var t=e.data,n=t.loginType,r=t.persistence,a=t.env;a===this.config.env&&(this._cache.updatePersistence(r),this._cache.setStore(this._cache.keys.loginTypeKey,n))}}]),e}(),mt=function(e,t){t=t||Oe();var n=ut(this.config.env),r=e.cloudPath,a=e.filePath,i=e.onUploadProgress,s=e.fileType,o=void 0===s?"image":s;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){var s=e.data,u=s.url,c=s.authorization,l=s.token,f=s.fileId,d=s.cosFileId,h=e.requestId,p={key:r,signature:c,"x-cos-meta-fileid":d,success_action_status:"201","x-cos-security-token":l};n.upload({url:u,data:p,file:a,name:r,fileType:o,onUploadProgress:i}).then((function(e){201===e.statusCode?t(null,{fileID:f,requestId:h}):t(new le({code:"STORAGE_REQUEST_FAIL",message:"STORAGE_REQUEST_FAIL: ".concat(e.data)}))})).catch((function(e){t(e)}))})).catch((function(e){t(e)})),t.promise},yt=function(e,t){t=t||Oe();var n=ut(this.config.env),r=e.cloudPath;return n.send("storage.getUploadMetadata",{path:r}).then((function(e){t(null,e)})).catch((function(e){t(e)})),t.promise},kt=function(e,t){var n=e.fileList;if(t=t||Oe(),!n||!Array.isArray(n))return{code:"INVALID_PARAM",message:"fileList必须是非空的数组"};var r,a=(0,c.default)(n);try{for(a.s();!(r=a.n()).done;){var i=r.value;if(!i||"string"!=typeof i)return{code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"}}}catch(o){a.e(o)}finally{a.f()}var s={fileid_list:n};return ut(this.config.env).send("storage.batchDeleteFile",s).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.delete_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},_t=function(e,t){var n=e.fileList;t=t||Oe(),n&&Array.isArray(n)||t(null,{code:"INVALID_PARAM",message:"fileList必须是非空的数组"});var r,a=[],i=(0,c.default)(n);try{for(i.s();!(r=i.n()).done;){var s=r.value;"object"==(0,o.default)(s)?(s.hasOwnProperty("fileID")&&s.hasOwnProperty("maxAge")||t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是包含fileID和maxAge的对象"}),a.push({fileid:s.fileID,max_age:s.maxAge})):"string"==typeof s?a.push({fileid:s}):t(null,{code:"INVALID_PARAM",message:"fileList的元素必须是字符串"})}}catch(l){i.e(l)}finally{i.f()}var u={file_list:a};return ut(this.config.env).send("storage.batchGetDownloadUrl",u).then((function(e){e.code?t(null,e):t(null,{fileList:e.data.download_list,requestId:e.requestId})})).catch((function(e){t(e)})),t.promise},wt=function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a,i,s;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r=t.fileID,e.next=3,_t.call(this,{fileList:[{fileID:r,maxAge:600}]});case 3:if(a=e.sent.fileList[0],"SUCCESS"===a.code){e.next=6;break}return e.abrupt("return",n?n(a):new Promise((function(e){e(a)})));case 6:if(i=ut(this.config.env),s=a.download_url,s=encodeURI(s),n){e.next=10;break}return e.abrupt("return",i.download({url:s}));case 10:return e.t0=n,e.next=13,i.download({url:s});case 13:e.t1=e.sent,(0,e.t0)(e.t1);case 15:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),bt=function(e,t){var n,r=e.name,a=e.data,i=e.query,s=e.parse,o=e.search,u=e.timeout,c=t||Oe();try{n=a?JSON.stringify(a):""}catch(r){return Promise.reject(r)}if(!r)return Promise.reject(new le({code:"PARAM_ERROR",message:"函数名不能为空"}));var l={inQuery:i,parse:s,search:o,function_name:r,request_data:n};return ut(this.config.env).send("functions.invokeFunction",l,{timeout:u}).then((function(e){if(e.code)c(null,e);else{var t=e.data.response_data;if(s)c(null,{result:t,requestId:e.requestId});else try{t=JSON.parse(e.data.response_data),c(null,{result:t,requestId:e.requestId})}catch(e){c(new le({message:"response data must be json"}))}}return c.promise})).catch((function(e){c(e)})),c.promise},xt={timeout:15e3,persistence:"session"},It={},St=function(){function e(t){(0,v.default)(this,e),this.config=t||this.config,this.authObj=void 0}return(0,m.default)(e,[{key:"init",value:function(t){switch(Ne.adapter||(this.requestClient=new Ne.adapter.reqClass({timeout:t.timeout||5e3,timeoutMsg:"请求在".concat((t.timeout||5e3)/1e3,"s内未完成，已中断")})),this.config=(0,d.default)((0,d.default)({},xt),t),!0){case this.config.timeout>6e5:r.warn("timeout大于可配置上限[10分钟]，已重置为上限数值"),this.config.timeout=6e5;break;case this.config.timeout<100:r.warn("timeout小于可配置下限[100ms]，已重置为下限数值"),this.config.timeout=100}return new e(this.config)}},{key:"auth",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.persistence;if(this.authObj)return this.authObj;var n,r=t||Ne.adapter.primaryStorage||xt.persistence;return r!==this.config.persistence&&(this.config.persistence=r),function(e){var t=e.env;qe[t]=new je(e),Fe[t]=new je((0,d.default)((0,d.default)({},e),{},{persistence:"local"}))}(this.config),n=this.config,ot[n.env]=new st(n),this.authObj=new vt(this.config),this.authObj}},{key:"on",value:function(e,t){return We.apply(this,[e,t])}},{key:"off",value:function(e,t){return ze.apply(this,[e,t])}},{key:"callFunction",value:function(e,t){return bt.apply(this,[e,t])}},{key:"deleteFile",value:function(e,t){return kt.apply(this,[e,t])}},{key:"getTempFileURL",value:function(e,t){return _t.apply(this,[e,t])}},{key:"downloadFile",value:function(e,t){return wt.apply(this,[e,t])}},{key:"uploadFile",value:function(e,t){return mt.apply(this,[e,t])}},{key:"getUploadMetadata",value:function(e,t){return yt.apply(this,[e,t])}},{key:"registerExtension",value:function(e){It[e.name]=e}},{key:"invokeExtension",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t,n){var r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(r=It[t],r){e.next=3;break}throw new le({message:"扩展".concat(t," 必须先注册")});case 3:return e.next=5,r.invoke(n,this);case 5:return e.abrupt("return",e.sent);case 6:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}()},{key:"useAdapters",value:function(e){var t=function(e){var t,n,r=(t=e,"[object Array]"===Object.prototype.toString.call(t)?e:[e]),a=(0,c.default)(r);try{for(a.s();!(n=a.n()).done;){var i=n.value,s=i.isMatch,o=i.genAdapter,u=i.runtime;if(s())return{adapter:o(),runtime:u}}}catch(l){a.e(l)}finally{a.f()}}(e)||{},n=t.adapter,r=t.runtime;n&&(Ne.adapter=n),r&&(Ne.runtime=r)}}]),e}(),Tt=new St;function Pt(e,t,n){void 0===n&&(n={});var r=/\?/.test(t),a="";for(var i in n)""===a?!r&&(t+="?"):a+="&",a+=i+"="+encodeURIComponent(n[i]);return/^http(s)?:\/\//.test(t+=a)?t:""+e+t}var At=function(){function e(){(0,v.default)(this,e)}return(0,m.default)(e,[{key:"get",value:function(e){var t=e.url,n=e.data,r=e.headers,a=e.timeout;return new Promise((function(e,i){he.request({url:Pt("https:",t),data:n,method:"GET",header:r,timeout:a,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"post",value:function(e){var t=e.url,n=e.data,r=e.headers,a=e.timeout;return new Promise((function(e,i){he.request({url:Pt("https:",t),data:n,method:"POST",header:r,timeout:a,success:function(t){e(t)},fail:function(e){i(e)}})}))}},{key:"upload",value:function(e){return new Promise((function(t,n){var r=e.url,a=e.file,i=e.data,s=e.headers,o=e.fileType,u=he.uploadFile({url:Pt("https:",r),name:"file",formData:Object.assign({},i),filePath:a,fileType:o,header:s,success:function(e){var n={statusCode:e.statusCode,data:e.data||{}};200===e.statusCode&&i.success_action_status&&(n.statusCode=parseInt(i.success_action_status,10)),t(n)},fail:function(e){n(new Error(e.errMsg||"uploadFile:fail"))}});"function"==typeof e.onUploadProgress&&u&&"function"==typeof u.onProgressUpdate&&u.onProgressUpdate((function(t){e.onUploadProgress({loaded:t.totalBytesSent,total:t.totalBytesExpectedToSend})}))}))}}]),e}(),Ot={setItem:function(e,t){he.setStorageSync(e,t)},getItem:function(e){return he.getStorageSync(e)},removeItem:function(e){he.removeStorageSync(e)},clear:function(){he.clearStorageSync()}},Et={genAdapter:function(){return{root:{},reqClass:At,localStorage:Ot,primaryStorage:"local"}},isMatch:function(){return!0},runtime:"uni_app"};Tt.useAdapters(Et);var Ct=Tt,Rt=Ct.init;Ct.init=function(e){e.env=e.spaceId;var t=Rt.call(this,e);t.config.provider="tencent",t.config.spaceId=e.spaceId;var n=t.auth;return t.auth=function(e){var t=n.call(this,e);return["linkAndRetrieveDataWithTicket","signInAnonymously","signOut","getAccessToken","getLoginState","signInWithTicket","getUserInfo"].forEach((function(e){var n;t[e]=(n=t[e],function(e){e=e||{};var t=ce(e),r=t.success,a=t.fail,i=t.complete;if(!(r||a||i))return n.call(this,e);n.call(this,e).then((function(e){r&&r(e),i&&i(e)}),(function(e){a&&a(e),i&&i(e)}))}).bind(t)})),t},t.customAuth=t.auth,t};var Lt=Ct;function Nt(e,t){return Ut.apply(this,arguments)}function Ut(){return Ut=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r="http://".concat(t,":").concat(n,"/system/ping"),e.prev=1,e.next=4,i={url:r,timeout:500},new Promise((function(e,t){he.request((0,d.default)((0,d.default)({},i),{},{success:function(t){e(t)},fail:function(e){t(e)}}))}));case 4:return a=e.sent,e.abrupt("return",!(!a.data||0!==a.data.code));case 8:return e.prev=8,e.t0=e["catch"](1),e.abrupt("return",!1);case 11:case"end":return e.stop()}}),e,null,[[1,8]])}))),Ut.apply(this,arguments)}function Dt(e,t){return Mt.apply(this,arguments)}function Mt(){return Mt=(0,f.default)((0,l.default)().mark((function e(t,n){var r,a,i;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=0;case 1:if(!(a<t.length)){e.next=11;break}return i=t[a],e.next=5,Nt(i,n);case 5:if(!e.sent){e.next=8;break}return r=i,e.abrupt("break",11);case 8:a++,e.next=1;break;case 11:return e.abrupt("return",{address:r,port:n});case 12:case"end":return e.stop()}}),e)}))),Mt.apply(this,arguments)}var jt={"serverless.file.resource.generateProximalSign":"storage/generate-proximal-sign","serverless.file.resource.report":"storage/report","serverless.file.resource.delete":"storage/delete","serverless.file.resource.getTempFileURL":"storage/get-temp-file-url"},qt=function(){function e(t){if((0,v.default)(this,e),["spaceId","clientSecret"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),!t.endpoint)throw new Error("集群空间未配置ApiEndpoint，配置后需要重新关联服务空间后生效");this.config=Object.assign({},t),this.config.provider="dcloud",this.config.requestUrl=this.config.endpoint+"/client",this.config.envType=this.config.envType||"public",this.adapter=he}return(0,m.default)(e,[{key:"request",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r=this,a=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(a.length>1&&void 0!==a[1])||a[1],n=!1,!n){e.next=8;break}return e.next=5,this.setupLocalRequest(t);case 5:e.t0=e.sent,e.next=9;break;case 8:e.t0=this.setupRequest(t);case 9:return t=e.t0,e.abrupt("return",Promise.resolve().then((function(){return n?r.requestLocal(t):_e.wrappedRequest(t,r.adapter.request)})));case 11:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"requestLocal",value:function(e){var t=this;return new Promise((function(n,r){t.adapter.request(Object.assign(e,{complete:function(e){if(e||(e={}),!e.statusCode||e.statusCode>=400){var t=e.data&&e.data.code||"SYS_ERR",a=e.data&&e.data.message||"request:fail";return r(new le({code:t,message:a}))}n({success:!0,result:e.data})}}))}))}},{key:"setupRequest",value:function(e){var t=Object.assign({},e,{spaceId:this.config.spaceId,timestamp:Date.now()}),n={"Content-Type":"application/json"};n["x-serverless-sign"]=_e.sign(t,this.config.clientSecret);var r=ye();n["x-client-info"]=encodeURIComponent(JSON.stringify(r));var a=pe(),i=a.token;return n["x-client-token"]=i,{url:this.config.requestUrl,method:"POST",data:t,dataType:"json",header:JSON.parse(JSON.stringify(n))}}},{key:"setupLocalRequest",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,s,o,u,c,f;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=ye(),r=pe(),a=r.token,i=Object.assign({},t,{spaceId:this.config.spaceId,timestamp:Date.now(),clientInfo:n,token:a}),s=this.__dev__&&this.__dev__.debugInfo||{},o=s.address,u=s.servePort,e.next=9,Dt(o,u);case 9:return c=e.sent,f=c.address,e.abrupt("return",{url:"http://".concat(f,":").concat(u,"/").concat(jt[t.method]),method:"POST",data:i,dataType:"json",header:JSON.parse(JSON.stringify({"Content-Type":"application/json"}))});case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"callFunction",value:function(e){var t={method:"serverless.function.runtime.invoke",params:JSON.stringify({functionTarget:e.name,functionArgs:e.data||{}})};return this.request(t,!1)}},{key:"getUploadFileOptions",value:function(e){var t={method:"serverless.file.resource.generateProximalSign",params:JSON.stringify(e)};return this.request(t)}},{key:"reportUploadFile",value:function(e){var t={method:"serverless.file.resource.report",params:JSON.stringify(e)};return this.request(t)}},{key:"uploadFile",value:function(e){var t,n=this,r=e.filePath,a=e.cloudPath,i=e.fileType,s=void 0===i?"image":i,o=e.onUploadProgress;if(!a)throw new le({code:"CLOUDPATH_REQUIRED",message:"cloudPath不可为空"});return this.getUploadFileOptions({cloudPath:a}).then((function(e){var a=e.result,i=a.url,u=a.formData,c=a.name;return t=e.result.fileUrl,new Promise((function(e,t){var a=n.adapter.uploadFile({url:i,formData:u,name:c,filePath:r,fileType:s,success:function(n){n&&n.statusCode<400?e(n):t(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){t(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof o&&a&&"function"==typeof a.onProgressUpdate&&a.onProgressUpdate((function(e){o({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))})).then((function(){return n.reportUploadFile({cloudPath:a})})).then((function(e){return new Promise((function(n,a){e.success?n({success:!0,filePath:r,fileID:t}):a(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))}))}))}},{key:"deleteFile",value:function(e){var t=e.fileList,n={method:"serverless.file.resource.delete",params:JSON.stringify({fileList:t})};return this.request(n).then((function(e){if(e.success)return e.result;throw new le({code:"DELETE_FILE_FAILED",message:"删除文件失败"})}))}},{key:"getTempFileURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fileList,n=e.maxAge;if(!Array.isArray(t)||0===t.length)throw new le({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});var r={method:"serverless.file.resource.getTempFileURL",params:JSON.stringify({fileList:t,maxAge:n})};return this.request(r).then((function(e){if(e.success)return{fileList:e.result.fileList.map((function(e){return{fileID:e.fileID,tempFileURL:e.tempFileURL}}))};throw new le({code:"GET_TEMP_FILE_URL_FAILED",message:"获取临时文件链接失败"})}))}}]),e}(),Ft={init:function(e){var t=new qt(e),n={signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!1)}};return t.auth=function(){return n},t.customAuth=t.auth,t}},Bt=k((function(e,t){e.exports=w.enc.Hex}));function Kt(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}function Ht(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.data,r=t.functionName,a=t.method,i=t.headers,o=t.signHeaderKeys,u=void 0===o?[]:o,c=t.config,l=String(Date.now()),f=Kt(),d=Object.assign({},i,{"x-from-app-id":c.spaceAppId,"x-from-env-id":c.spaceId,"x-to-env-id":c.spaceId,"x-from-instance-id":l,"x-from-function-name":r,"x-client-timestamp":l,"x-alipay-source":"client","x-request-id":f,"x-alipay-callid":f,"x-trace-id":f}),h=["x-from-app-id","x-from-env-id","x-to-env-id","x-from-instance-id","x-from-function-name","x-client-timestamp"].concat(u),p=e.split("?")||[],g=(0,s.default)(p,2),v=g[0],m=void 0===v?"":v,y=g[1],k=void 0===y?"":y,_=function(e){var t="HMAC-SHA256",n=e.signedHeaders.join(";"),r=e.signedHeaders.map((function(t){return"".concat(t.toLowerCase(),":").concat(e.headers[t],"\n")})).join(""),a=Pe(e.body).toString(Bt),i="".concat(e.method.toUpperCase(),"\n").concat(e.path,"\n").concat(e.query,"\n").concat(r,"\n").concat(n,"\n").concat(a,"\n"),s=Pe(i).toString(Bt),o="".concat(t,"\n").concat(e.timestamp,"\n").concat(s,"\n"),u=Ae(o,e.secretKey).toString(Bt);return"".concat(t," Credential=").concat(e.secretId,", SignedHeaders=").concat(n,", Signature=").concat(u)}({path:m,query:k,method:a,headers:d,timestamp:l,body:JSON.stringify(n),secretId:c.accessKey,secretKey:c.secretKey,signedHeaders:h.sort()});return{url:"".concat(c.endpoint).concat(e),headers:Object.assign({},d,{Authorization:_})}}function Jt(e){var t=e.url,n=e.data,r=e.method,a=void 0===r?"POST":r,i=e.headers,s=void 0===i?{}:i,u=e.timeout;return new Promise((function(e,r){he.request({url:t,method:a,data:"object"==(0,o.default)(n)?JSON.stringify(n):n,header:s,dataType:"json",timeout:u,complete:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=s["x-trace-id"]||"";if(!t.statusCode||t.statusCode>=400){var a=t.data||{},i=a.message,o=a.errMsg,u=a.trace_id;return r(new le({code:"SYS_ERR",message:i||o||"request:fail",requestId:u||n}))}e({status:t.statusCode,data:t.data,headers:t.header,requestId:n})}})}))}function Wt(e,t){var n=e.path,r=e.data,a=e.method,i=void 0===a?"GET":a,s=Ht(n,{functionName:"",data:r,method:i,headers:{"x-alipay-cloud-mode":"oss","x-data-api-type":"oss","x-expire-timestamp":Date.now()+6e4},signHeaderKeys:["x-data-api-type","x-expire-timestamp"],config:t}),o=s.url,u=s.headers;return Jt({url:o,data:r,method:i,headers:u}).then((function(e){var t=e.data||{};if(!t.success)throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId});return t.data||{}})).catch((function(e){throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}function Vt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.trim().replace(/^cloud:\/\//,""),n=t.indexOf("/");if(n<=0)throw new le({code:"INVALID_PARAM",message:"fileID不合法"});var a=t.substring(0,n),i=t.substring(n+1);return a!==this.config.spaceId&&r.warn("file ".concat(e," does not belong to env ").concat(this.config.spaceId)),i}function zt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"cloud://".concat(this.config.spaceId,"/").concat(e.replace(/^\/+/,""))}var $t=function(){function e(t){(0,v.default)(this,e),this.config=t}return(0,m.default)(e,[{key:"signedURL",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="/ws/function/".concat(e),r=this.config.wsEndpoint.replace(/^ws(s)?:\/\//,""),a=Object.assign({},t,{accessKeyId:this.config.accessKey,signatureNonce:Kt(),timestamp:""+Date.now()}),i=[n,["accessKeyId","authorization","signatureNonce","timestamp"].sort().map((function(e){return a[e]?"".concat(e,"=").concat(a[e]):null})).filter(Boolean).join("&"),"host:".concat(r)].join("\n"),s=["HMAC-SHA256",Pe(i).toString(Bt)].join("\n"),o=Ae(s,this.config.secretKey).toString(Bt),u=Object.keys(a).map((function(e){return"".concat(e,"=").concat(encodeURIComponent(a[e]))})).join("&");return"".concat(this.config.wsEndpoint).concat(n,"?").concat(u,"&signature=").concat(o)}}]),e}(),Gt=function(){function e(t){if((0,v.default)(this,e),["spaceId","spaceAppId","accessKey","secretKey"].forEach((function(e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new Error("".concat(e," required"))})),t.endpoint){if("string"!=typeof t.endpoint)throw new Error("endpoint must be string");if(!/^https:\/\//.test(t.endpoint))throw new Error("endpoint must start with https://");t.endpoint=t.endpoint.replace(/\/$/,"")}this.config=Object.assign({},t,{endpoint:t.endpoint||"https://".concat(t.spaceId,".api-hz.cloudbasefunction.cn"),wsEndpoint:t.wsEndpoint||"wss://".concat(t.spaceId,".api-hz.cloudbasefunction.cn")}),this._websocket=new $t(this.config)}return(0,m.default)(e,[{key:"callFunction",value:function(e){return function(e,t){var n=e.name,r=e.data,a=e.async,i=void 0!==a&&a,s=e.timeout,o="POST",u={"x-to-function-name":n};i&&(u["x-function-invoke-type"]="async");var c=Ht("/functions/invokeFunction",{functionName:n,data:r,method:o,headers:u,signHeaderKeys:["x-to-function-name"],config:t}),l=c.url,f=c.headers;return Jt({url:l,data:r,method:o,headers:f,timeout:s}).then((function(e){var t=0;if(i){var n=e.data||{};t="200"===n.errCode?0:n.errCode,e.data=n.data||{},e.errMsg=n.errMsg}if(0!==t)throw new le({code:t,message:e.errMsg,requestId:e.requestId});return{errCode:t,success:0===t,requestId:e.requestId,result:e.data}})).catch((function(e){throw new le({code:e.errCode,message:e.errMsg,requestId:e.requestId})}))}(e,this.config)}},{key:"uploadFileToOSS",value:function(e){var t=e.url,n=e.filePath,r=e.fileType,a=e.formData,i=e.onUploadProgress;return new Promise((function(e,s){var o=he.uploadFile({url:t,filePath:n,fileType:r,formData:a,name:"file",success:function(t){t&&t.statusCode<400?e(t):s(new le({code:"UPLOAD_FAILED",message:"文件上传失败"}))},fail:function(e){s(new le({code:e.code||"UPLOAD_FAILED",message:e.message||e.errMsg||"文件上传失败"}))}});"function"==typeof i&&o&&"function"==typeof o.onProgressUpdate&&o.onProgressUpdate((function(e){i({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend})}))}))}},{key:"uploadFile",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r,a,i,s,o,u,c,f,d,h;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t.filePath,r=t.cloudPath,a=void 0===r?"":r,i=t.fileType,s=void 0===i?"image":i,o=t.onUploadProgress,"string"===C(a)){e.next=3;break}throw new le({code:"INVALID_PARAM",message:"cloudPath必须为字符串类型"});case 3:if(a=a.trim()){e.next=5;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不可为空"});case 5:if(!/:\/\//.test(a)){e.next=7;break}throw new le({code:"INVALID_PARAM",message:"cloudPath不合法"});case 7:return e.next=9,Wt({path:"/".concat(a.replace(/^\//,""),"?post_url")},this.config);case 9:return u=e.sent,c=u.file_id,f=u.upload_url,d=u.form_data,h=d&&d.reduce((function(e,t){return e[t.key]=t.value,e}),{}),e.abrupt("return",this.uploadFileToOSS({url:f,filePath:n,fileType:s,formData:h,onUploadProgress:o}).then((function(){return{fileID:c}})));case 15:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"getTempFileURL",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,a=this;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.fileList,e.abrupt("return",new Promise((function(e,t){(!n||n.length<0)&&e({code:"INVALID_PARAM",message:"fileList不能为空数组"}),n.length>50&&e({code:"INVALID_PARAM",message:"fileList数组长度不能超过50"});var i,s=[],o=(0,c.default)(n);try{for(o.s();!(i=o.n()).done;){var u=i.value,l=void 0;"string"!==C(u)&&e({code:"INVALID_PARAM",message:"fileList的元素必须是非空的字符串"});try{l=Vt.call(a,u)}catch(e){r.warn(e.errCode,e.errMsg),l=u}s.push({file_id:l,expire:600})}}catch(f){o.e(f)}finally{o.f()}Wt({path:"/?download_url",data:{file_list:s},method:"POST"},a.config).then((function(t){var n=t.file_list,r=void 0===n?[]:n;e({fileList:r.map((function(e){return{fileID:zt.call(a,e.file_id),tempFileURL:e.download_url}}))})})).catch((function(e){return t(e)}))})));case 2:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},{key:"connectWebSocket",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(t){var n,r;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.name,r=t.query,e.abrupt("return",he.connectSocket({url:this._websocket.signedURL(n,r),complete:function(){}}));case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()}]),e}(),Qt={init:function(e){e.provider="alipay";var t=new Gt(e);return t.auth=function(){return{signInAnonymously:function(){return Promise.resolve()},getLoginState:function(){return Promise.resolve(!0)}}},t}};function Yt(e){var t,n=e.data;t=ye();var r=JSON.parse(JSON.stringify(n||{}));if(Object.assign(r,{clientInfo:t}),!r.uniIdToken){var a=pe(),i=a.token;i&&(r.uniIdToken=i)}return r}var Xt=[{rule:/fc_function_not_found|FUNCTION_NOT_FOUND/,content:"，云函数[{functionName}]在云端不存在，请检查此云函数名称是否正确以及该云函数是否已上传到服务空间",mode:"append"}],Zt=/[\\^$.*+?()[\]{}|]/g,en=RegExp(Zt.source);function tn(e,t,n){return e.replace(new RegExp((r=t)&&en.test(r)?r.replace(Zt,"\\$&"):r,"g"),n);var r}var nn={NONE:"none",REQUEST:"request",RESPONSE:"response",BOTH:"both"},rn="_globalUniCloudStatus",an="_globalUniCloudSecureNetworkCache__{spaceId}",sn="uni-secure-network",on={SYSTEM_ERROR:{code:2e4,message:"System error"},APP_INFO_INVALID:{code:20101,message:"Invalid client"},GET_ENCRYPT_KEY_FAILED:{code:20102,message:"Get encrypt key failed"}};function un(e){var t=e||{},n=t.errSubject,r=t.subject,a=t.errCode,i=t.errMsg,s=t.code,o=t.message,u=t.cause;return new le({subject:n||r||sn,code:a||s||on.SYSTEM_ERROR.code,message:i||o,cause:u})}var cn;cn="0123456789abcdef";var ln;function fn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.secretType;return t===nn.REQUEST||t===nn.RESPONSE||t===nn.BOTH}function dn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name,n=e.data,r=void 0===n?{}:n;return"app"===B&&"DCloud-clientDB"===t&&"encryption"===r.redirectTo&&"getAppClientKey"===r.action}function hn(e){e.functionName,e.result,e.logPvd}function pn(e){var t=e.callFunction,n=function(n){var r=this,a=n.name;n.data=Yt.call(e,{data:n.data});var i={aliyun:"aliyun",tencent:"tcb",tcb:"tcb",alipay:"alipay",dcloud:"dcloud"}[this.config.provider],s=fn(n),o=dn(n),u=s||o;return t.call(this,n).then((function(e){return e.errCode=0,!u&&hn.call(r,{functionName:a,result:e,logPvd:i}),Promise.resolve(e)}),(function(e){return!u&&hn.call(r,{functionName:a,result:e,logPvd:i}),e&&e.message&&(e.message=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.message,n=void 0===t?"":t,r=e.extraInfo,a=void 0===r?{}:r,i=e.formatter,s=void 0===i?[]:i,o=0;o<s.length;o++){var u=s[o],c=u.rule,l=u.content,f=u.mode,d=n.match(c);if(d){for(var h=l,p=1;p<d.length;p++)h=tn(h,"{$".concat(p,"}"),d[p]);for(var g in a)h=tn(h,"{".concat(g,"}"),a[g]);return"replace"===f?h:n+h}}return n}({message:"[".concat(n.name,"]: ").concat(e.message),formatter:Xt,extraInfo:{functionName:a}})),Promise.reject(e)}))};e.callFunction=function(t){var a,i,s=e.config,o=s.provider,u=s.spaceId,c=t.name;return t.data=t.data||{},a=n,a=a.bind(e),i=dn(t)?n.call(e,t):function(e){var t=e.name,n=e.data,r=void 0===n?{}:n;return"mp-weixin"===B&&"uni-id-co"===t&&"secureNetworkHandshakeByWeixin"===r.method}(t)?a.call(e,t):fn(t)?new ln({secretType:t.secretType,uniCloudIns:e}).wrapEncryptDataCallFunction(n.bind(e))(t):function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,a=e.functionName,i=ve(),s=i.appId,o=i.uniPlatform,u=i.osName,c=o;"app"===o&&(c=u);var l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.provider,n=e.spaceId,r=F;if(!r)return{};t=function(e){return"tencent"===e?"tcb":e}(t);var a=r.find((function(e){return e.provider===t&&e.spaceId===n}));return a&&a.config}({provider:t,spaceId:n});if(!l||!l.accessControl||!l.accessControl.enable)return!1;var f=l.accessControl.function||{},d=Object.keys(f);if(0===d.length)return!0;var h=function(e,t){for(var n,r,a,i=0;i<e.length;i++){var s=e[i];s!==t?"*"!==s?s.split(",").map((function(e){return e.trim()})).indexOf(t)>-1&&(r=s):a=s:n=s}return n||r||a}(d,a);if(!h)return!1;if((f[h]||[]).find((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.appId===s&&(e.platform||"").toLowerCase()===c.toLowerCase()})))return!0;throw r.error("此应用[appId: ".concat(s,", platform: ").concat(c,"]不在云端配置的允许访问的应用列表内，参考：https://uniapp.dcloud.net.cn/uniCloud/secure-network.html#verify-client")),un(on.APP_INFO_INVALID)}({provider:o,spaceId:u,functionName:c})?new ln({secretType:t.secretType,uniCloudIns:e}).wrapVerifyClientCallFunction(n.bind(e))(t):a(t),Object.defineProperty(i,"result",{get:function(){return r.warn("当前返回结果为Promise类型，不可直接访问其result属性，详情请参考：https://uniapp.dcloud.net.cn/uniCloud/faq?id=promise"),{}}}),i.then((function(e){return e}))}}ln="mp-weixin"!==B&&"app"!==B?function(){return(0,m.default)((function e(){throw(0,v.default)(this,e),un({message:"Platform ".concat(B," is not supported by secure network")})}))}():function(){return(0,m.default)((function e(){throw(0,v.default)(this,e),un({message:"Platform ".concat(B," is not enabled, please check whether secure network module is enabled in your manifest.json")})}))}();var gn=Symbol("CLIENT_DB_INTERNAL");function vn(e,t){return e.then="DoNotReturnProxyWithAFunctionNamedThen",e._internalType=gn,e.inspect=null,e.__ob__=void 0,new Proxy(e,{get:function(e,n,r){if("_uniClient"===n)return null;if("symbol"==(0,o.default)(n))return e[n];if(n in e||"string"!=typeof n){var a=e[n];return"function"==typeof a?a.bind(e):a}return t.get(e,n,r)}})}function mn(e){return{on:function(t,n){e[t]=e[t]||[],e[t].indexOf(n)>-1||e[t].push(n)},off:function(t,n){e[t]=e[t]||[];var r=e[t].indexOf(n);-1!==r&&e[t].splice(r,1)}}}var yn=["db.Geo","db.command","command.aggregate"];function kn(e,t){return yn.indexOf("".concat(e,".").concat(t))>-1}function _n(e){switch(C(e)){case"array":return e.map((function(e){return _n(e)}));case"object":return e._internalType===gn||Object.keys(e).forEach((function(t){e[t]=_n(e[t])})),e;case"regexp":return{$regexp:{source:e.source,flags:e.flags}};case"date":return{$date:e.toISOString()};default:return e}}function wn(e){return e&&e.content&&e.content.$method}var bn=function(){function e(t,n,r){(0,v.default)(this,e),this.content=t,this.prevStage=n||null,this.udb=null,this._database=r}return(0,m.default)(e,[{key:"toJSON",value:function(){for(var e=this,t=[e.content];e.prevStage;)e=e.prevStage,t.push(e.content);return{$db:t.reverse().map((function(e){return{$method:e.$method,$param:_n(e.$param)}}))}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}},{key:"getAction",value:function(){var e=this.toJSON().$db.find((function(e){return"action"===e.$method}));return e&&e.$param&&e.$param[0]}},{key:"getCommand",value:function(){return{$db:this.toJSON().$db.filter((function(e){return"action"!==e.$method}))}}},{key:"isAggregate",get:function(){for(var e=this;e;){var t=wn(e),n=wn(e.prevStage);if("aggregate"===t&&"collection"===n||"pipeline"===t)return!0;e=e.prevStage}return!1}},{key:"isCommand",get:function(){for(var e=this;e;){if("command"===wn(e))return!0;e=e.prevStage}return!1}},{key:"isAggregateCommand",get:function(){for(var e=this;e;){var t=wn(e),n=wn(e.prevStage);if("aggregate"===t&&"command"===n)return!0;e=e.prevStage}return!1}},{key:"getNextStageFn",value:function(e){var t=this;return function(){return xn({$method:e,$param:_n(Array.from(arguments))},t,t._database)}}},{key:"count",get:function(){return this.isAggregate?this.getNextStageFn("count"):function(){return this._send("count",Array.from(arguments))}}},{key:"remove",get:function(){return this.isCommand?this.getNextStageFn("remove"):function(){return this._send("remove",Array.from(arguments))}}},{key:"get",value:function(){return this._send("get",Array.from(arguments))}},{key:"add",get:function(){return this.isCommand?this.getNextStageFn("add"):function(){return this._send("add",Array.from(arguments))}}},{key:"update",value:function(){return this._send("update",Array.from(arguments))}},{key:"end",value:function(){return this._send("end",Array.from(arguments))}},{key:"set",get:function(){return this.isCommand?this.getNextStageFn("set"):function(){throw new Error("JQL禁止使用set方法")}}},{key:"_send",value:function(e,t){var n=this.getAction(),r=this.getCommand();return r.$db.push({$method:e,$param:_n(t)}),this._database._callCloudFunction({action:n,command:r})}}]),e}();function xn(e,t,n){return vn(new bn(e,t,n),{get:function(e,t){var r="db";return e&&e.content&&(r=e.content.$method),kn(r,t)?xn({$method:t},e,n):function(){return xn({$method:t,$param:_n(Array.from(arguments))},e,n)}}})}function In(e){var t=e.path,n=e.method;return function(){function e(){(0,v.default)(this,e),this.param=Array.from(arguments)}return(0,m.default)(e,[{key:"toJSON",value:function(){return{$newDb:[].concat((0,u.default)(t.map((function(e){return{$method:e}}))),[{$method:n,$param:this.param}])}}},{key:"toString",value:function(){return JSON.stringify(this.toJSON())}}]),e}()}var Sn=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.uniClient,r=void 0===n?{}:n,a=t.isJQL,i=void 0!==a&&a;(0,v.default)(this,e),this._uniClient=r,this._authCallBacks={},this._dbCallBacks={},r._isDefault&&(this._dbCallBacks=W("_globalUniCloudDatabaseCallback")),i||(this.auth=mn(this._authCallBacks)),this._isJQL=i,Object.assign(this,mn(this._dbCallBacks)),this.env=vn({},{get:function(e,t){return{$env:t}}}),this.Geo=vn({},{get:function(e,t){return In({path:["Geo"],method:t})}}),this.serverDate=In({path:[],method:"serverDate"}),this.RegExp=In({path:[],method:"RegExp"})}return(0,m.default)(e,[{key:"getCloudEnv",value:function(e){if("string"!=typeof e||!e.trim())throw new Error("getCloudEnv参数错误");return{$env:e.replace("$cloudEnv_","")}}},{key:"_callback",value:function(e,t){var n=this._dbCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,u.default)(t))}))}},{key:"_callbackAuth",value:function(e,t){var n=this._authCallBacks;n[e]&&n[e].forEach((function(e){e.apply(void 0,(0,u.default)(t))}))}},{key:"multiSend",value:function(){var e=Array.from(arguments),t=e.map((function(e){var t=e.getAction(),n=e.getCommand();if("getTemp"!==n.$db[n.$db.length-1].$method)throw new Error("multiSend只支持子命令内使用getTemp");return{action:t,command:n}}));return this._callCloudFunction({multiCommand:t,queryList:e})}}]),e}();function Tn(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return vn(new e(t),{get:function(e,t){return kn("db",t)?xn({$method:t},null,e):function(){return xn({$method:t,$param:_n(Array.from(arguments))},null,e)}}})}var Pn=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){return(0,v.default)(this,n),t.apply(this,arguments)}return(0,m.default)(n,[{key:"_parseResult",value:function(e){return this._isJQL?e.result:e}},{key:"_callCloudFunction",value:function(e){var t=this,n=e.action,a=e.command,i=e.multiCommand,s=e.queryList;function o(e,t){if(i&&s)for(var n=0;n<s.length;n++){var r=s[n];r.udb&&"function"==typeof r.udb.setResult&&(t?r.udb.setResult(t):r.udb.setResult(e.result.dataList[n]))}}var u=this,c=this._isJQL?"databaseForJQL":"database";function l(e){return u._callback("error",[e]),Q(Y(c,"fail"),e).then((function(){return Q(Y(c,"complete"),e)})).then((function(){return o(null,e),ie(ee.RESPONSE,{type:te.CLIENT_DB,content:e}),Promise.reject(e)}))}var f=Q(Y(c,"invoke")),d=this._uniClient;return f.then((function(){return d.callFunction({name:"DCloud-clientDB",type:P.CLIENT_DB,data:{action:n,command:a,multiCommand:i}})})).then((function(e){var n=e.result,a=n.code,i=n.message,s=n.token,f=n.tokenExpired,d=n.systemInfo,h=void 0===d?[]:d;if(h)for(var p=0;p<h.length;p++){var g=h[p],v=g.level,m=g.message,y=g.detail,k="[System Info]"+m;y&&(k="".concat(k,"\n详细信息：").concat(y)),(r["app"===B&&"warn"===v?"error":v]||r.log)(k)}if(a)return l(new le({code:a,message:i,requestId:e.requestId}));e.result.errCode=e.result.errCode||e.result.code,e.result.errMsg=e.result.errMsg||e.result.message,s&&f&&(ge({token:s,tokenExpired:f}),t._callbackAuth("refreshToken",[{token:s,tokenExpired:f}]),t._callback("refreshToken",[{token:s,tokenExpired:f}]),ie(ee.REFRESH_TOKEN,{token:s,tokenExpired:f}));for(var _=[{prop:"affectedDocs",tips:"affectedDocs不再推荐使用，请使用inserted/deleted/updated/data.length替代"},{prop:"code",tips:"code不再推荐使用，请使用errCode替代"},{prop:"message",tips:"message不再推荐使用，请使用errMsg替代"}],w=function(t){var n=_[t],a=n.prop,i=n.tips;if(a in e.result){var s=e.result[a];Object.defineProperty(e.result,a,{get:function(){return r.warn(i),s}})}},b=0;b<_.length;b++)w(b);return function(e){return Q(Y(c,"success"),e).then((function(){return Q(Y(c,"complete"),e)})).then((function(){o(e,null);var t=u._parseResult(e);return ie(ee.RESPONSE,{type:te.CLIENT_DB,content:t}),Promise.resolve(t)}))}(e)}),(function(e){return/fc_function_not_found|FUNCTION_NOT_FOUND/g.test(e.message)&&r.warn("clientDB未初始化，请在web控制台保存一次schema以开启clientDB"),l(new le({code:e.code||"SYSTEM_ERROR",message:e.message,requestId:e.requestId}))}))}}]),n}(Sn),An="token无效，跳转登录页面",On="token过期，跳转登录页面",En={TOKEN_INVALID_TOKEN_EXPIRED:On,TOKEN_INVALID_INVALID_CLIENTID:An,TOKEN_INVALID:An,TOKEN_INVALID_WRONG_TOKEN:An,TOKEN_INVALID_ANONYMOUS_USER:An},Cn={"uni-id-token-expired":On,"uni-id-check-token-failed":An,"uni-id-token-not-exist":An,"uni-id-check-device-feature-failed":An},Rn=(0,d.default)((0,d.default)((0,d.default)({},En),Cn),{},{default:"用户未登录或登录状态过期，自动跳转登录页面"});function Ln(e,t){var n="";return n=e?"".concat(e,"/").concat(t):t,n.replace(/^\//,"")}function Nn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=[],r=[];return e.forEach((function(e){!0===e.needLogin?n.push(Ln(t,e.path)):!1===e.needLogin&&r.push(Ln(t,e.path))})),{needLoginPage:n,notNeedLoginPage:r}}function Un(e){return e.split("?")[0].replace(/^\//,"")}function Dn(){return function(e){var t=e&&e.$page&&e.$page.fullPath;return t?("/"!==t.charAt(0)&&(t="/"+t),t):""}(function(){var e=getCurrentPages();return e[e.length-1]}())}function Mn(){return Un(Dn())}function jn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return!1;if(!(t&&t.list&&t.list.length))return!1;var n=t.list,r=Un(e);return n.some((function(e){return e.pagePath===r}))}var qn,Fn=!!y.default.uniIdRouter,Bn=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y.default,t=e.pages,n=void 0===t?[]:t,r=e.subPackages,a=void 0===r?[]:r,i=e.uniIdRouter,s=void 0===i?{}:i,o=e.tabBar,c=void 0===o?{}:o,l=s.loginPage,f=s.needLogin,d=void 0===f?[]:f,h=s.resToLogin,p=void 0===h||h,g=Nn(n),v=g.needLoginPage,m=g.notNeedLoginPage,k=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[],n=[];return e.forEach((function(e){var r=e.root,a=e.pages,i=void 0===a?[]:a,s=Nn(i,r),o=s.needLoginPage,c=s.notNeedLoginPage;t.push.apply(t,(0,u.default)(o)),n.push.apply(n,(0,u.default)(c))})),{needLoginPage:t,notNeedLoginPage:n}}(a),_=k.needLoginPage,w=k.notNeedLoginPage;return{loginPage:l,routerNeedLogin:d,resToLogin:p,needLoginPage:[].concat((0,u.default)(v),(0,u.default)(_)),notNeedLoginPage:[].concat((0,u.default)(m),(0,u.default)(w)),loginPageInTabBar:jn(l,c)}}(),Kn=Bn.loginPage,Hn=Bn.routerNeedLogin,Jn=Bn.resToLogin,Wn=Bn.needLoginPage,Vn=Bn.notNeedLoginPage,zn=Bn.loginPageInTabBar;if(Wn.indexOf(Kn)>-1)throw new Error("Login page [".concat(Kn,'] should not be "needLogin", please check your pages.json'));function $n(e){var t=Mn();if("/"===e.charAt(0))return e;var n=e.split("?"),r=(0,s.default)(n,2),a=r[0],i=r[1],o=a.replace(/^\//,"").split("/"),u=t.split("/");u.pop();for(var c=0;c<o.length;c++){var l=o[c];".."===l?u.pop():"."!==l&&u.push(l)}return""===u[0]&&u.shift(),"/"+u.join("/")+(i?"?"+i:"")}function Gn(e){var t=Un($n(e));return!(Vn.indexOf(t)>-1)&&(Wn.indexOf(t)>-1||Hn.some((function(t){return function(e,t){return new RegExp(t).test(e)}(e,t)})))}function Qn(e){var t=e.redirect,n=Un(t),r=Un(Kn);return Mn()!==r&&n!==r}function Yn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.api,n=e.redirect;if(n&&Qn({redirect:n})){var r=function(e,t){return"/"!==e.charAt(0)&&(e="/"+e),t?e.indexOf("?")>-1?e+"&uniIdRedirectUrl=".concat(encodeURIComponent(t)):e+"?uniIdRedirectUrl=".concat(encodeURIComponent(t)):e}(Kn,n);zn?"navigateTo"!==t&&"redirectTo"!==t||(t="switchTab"):"switchTab"===t&&(t="navigateTo");var a={navigateTo:uni.navigateTo,redirectTo:uni.redirectTo,switchTab:uni.switchTab,reLaunch:uni.reLaunch};setTimeout((function(){a[t]({url:r})}),0)}}function Xn(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.url,n={abortLoginPageJump:!1,autoToLoginPage:!1},r=function(){var e,t=pe(),n=t.token,r=t.tokenExpired;if(n){if(r<Date.now()){var a="uni-id-token-expired";e={errCode:a,errMsg:Rn[a]}}}else{var i="uni-id-check-token-failed";e={errCode:i,errMsg:Rn[i]}}return e}();if(Gn(t)&&r){if(r.uniIdRedirectUrl=t,ne(ee.NEED_LOGIN).length>0)return setTimeout((function(){ie(ee.NEED_LOGIN,r)}),0),n.abortLoginPageJump=!0,n;n.autoToLoginPage=!0}return n}function Zn(){!function(){var e=Dn(),t=Xn({url:e}),n=t.abortLoginPageJump,r=t.autoToLoginPage;n||r&&Yn({api:"redirectTo",redirect:e})}();for(var e=["navigateTo","redirectTo","reLaunch","switchTab"],t=function(t){var n=e[t];uni.addInterceptor(n,{invoke:function(e){var t=Xn({url:e.url}),r=t.abortLoginPageJump,a=t.autoToLoginPage;return r?e:a?(Yn({api:n,redirect:$n(e.url)}),!1):e}})},n=0;n<e.length;n++)t(n)}function er(){this.onResponse((function(e){var t=e.type,n=e.content,r=!1;switch(t){case"cloudobject":r=function(e){if("object"!=(0,o.default)(e))return!1;var t=e||{},n=t.errCode;return n in Rn}(n);break;case"clientdb":r=function(e){if("object"!=(0,o.default)(e))return!1;var t=e||{},n=t.errCode;return n in En}(n)}r&&function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=ne(ee.NEED_LOGIN);ue().then((function(){var n=Dn();if(n&&Qn({redirect:n}))return t.length>0?ie(ee.NEED_LOGIN,Object.assign({uniIdRedirectUrl:n},e)):void(Kn&&Yn({api:"navigateTo",redirect:n}))}))}(n)}))}function tr(e){!function(e){e.onResponse=function(e){re(ee.RESPONSE,e)},e.offResponse=function(e){ae(ee.RESPONSE,e)}}(e),function(e){e.onNeedLogin=function(e){re(ee.NEED_LOGIN,e)},e.offNeedLogin=function(e){ae(ee.NEED_LOGIN,e)},Fn&&(W(rn).needLoginInit||(W(rn).needLoginInit=!0,ue().then((function(){Zn.call(e)})),Jn&&er.call(e)))}(e),function(e){e.onRefreshToken=function(e){re(ee.REFRESH_TOKEN,e)},e.offRefreshToken=function(e){ae(ee.REFRESH_TOKEN,e)}}(e)}var nr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",rr=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function ar(){var e,t,n=pe().token||"",r=n.split(".");if(!n||3!==r.length)return{uid:null,role:[],permission:[],tokenExpired:0};try{e=JSON.parse((t=r[1],decodeURIComponent(qn(t).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(n){throw new Error("获取当前用户信息出错，详细错误信息为："+n.message)}return e.tokenExpired=1e3*e.exp,delete e.exp,delete e.iat,e}qn="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!rr.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,r,a="",i=0;i<e.length;)t=nr.indexOf(e.charAt(i++))<<18|nr.indexOf(e.charAt(i++))<<12|(n=nr.indexOf(e.charAt(i++)))<<6|(r=nr.indexOf(e.charAt(i++))),a+=64===n?String.fromCharCode(t>>16&255):64===r?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return a}:atob;var ir=k((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n="chooseAndUploadFile:ok",r="chooseAndUploadFile:fail";function a(e,t){return e.tempFiles.forEach((function(e,n){e.name||(e.name=e.path.substring(e.path.lastIndexOf("/")+1)),t&&(e.fileType=t),e.cloudPath=Date.now()+"_"+n+e.name.substring(e.name.lastIndexOf("."))})),e.tempFilePaths||(e.tempFilePaths=e.tempFiles.map((function(e){return e.path}))),e}function i(e,t,r){var a=r.onChooseFile,i=r.onUploadProgress;return t.then((function(e){if(a){var t=a(e);if(void 0!==t)return Promise.resolve(t).then((function(t){return void 0===t?e:t}))}return e})).then((function(t){return!1===t?{errMsg:n,tempFilePaths:[],tempFiles:[]}:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,a=arguments.length>3?arguments[3]:void 0;(t=Object.assign({},t)).errMsg=n;var i=t.tempFiles,s=i.length,o=0;return new Promise((function(n){for(;o<r;)u();function u(){var r=o++;if(r>=s)!i.find((function(e){return!e.url&&!e.errMsg}))&&n(t);else{var c=i[r];e.uploadFile({provider:c.provider,filePath:c.path,cloudPath:c.cloudPath,fileType:c.fileType,cloudPathAsRealPath:c.cloudPathAsRealPath,onUploadProgress:function(e){e.index=r,e.tempFile=c,e.tempFilePath=c.path,a&&a(e)}}).then((function(e){c.url=e.fileID,r<s&&u()})).catch((function(e){c.errMsg=e.errMsg||e.message,r<s&&u()}))}}}))}(e,t,5,i)}))}t.initChooseAndUploadFile=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{type:"all"};return"image"===t.type?i(e,function(e){var t=e.count,n=e.sizeType,i=e.sourceType,s=void 0===i?["album","camera"]:i,o=e.extension;return new Promise((function(e,i){uni.chooseImage({count:t,sizeType:n,sourceType:s,extension:o,success:function(t){e(a(t,"image"))},fail:function(e){i({errMsg:e.errMsg.replace("chooseImage:fail",r)})}})}))}(t),t):"video"===t.type?i(e,function(e){var t=e.camera,n=e.compressed,i=e.maxDuration,s=e.sourceType,o=void 0===s?["album","camera"]:s,u=e.extension;return new Promise((function(e,s){uni.chooseVideo({camera:t,compressed:n,maxDuration:i,sourceType:o,extension:u,success:function(t){var n=t.tempFilePath,r=t.duration,i=t.size,s=t.height,o=t.width;e(a({errMsg:"chooseVideo:ok",tempFilePaths:[n],tempFiles:[{name:t.tempFile&&t.tempFile.name||"",path:n,size:i,type:t.tempFile&&t.tempFile.type||"",width:o,height:s,duration:r,fileType:"video",cloudPath:""}]},"video"))},fail:function(e){s({errMsg:e.errMsg.replace("chooseVideo:fail",r)})}})}))}(t),t):i(e,function(e){var t=e.count,n=e.extension;return new Promise((function(e,i){var s=uni.chooseFile;if("undefined"!=typeof wx&&"function"==typeof wx.chooseMessageFile&&(s=wx.chooseMessageFile),"function"!=typeof s)return i({errMsg:r+" 请指定 type 类型，该平台仅支持选择 image 或 video。"});s({type:"all",count:t,extension:n,success:function(t){e(a(t))},fail:function(e){i({errMsg:e.errMsg.replace("chooseFile:fail",r)})}})}))}(t),t)}}})),sr=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(ir),or={auto:"auto",onready:"onready",manual:"manual"};function ur(e){return{props:{localdata:{type:Array,default:function(){return[]}},options:{type:[Object,Array],default:function(){return{}}},spaceInfo:{type:Object,default:function(){return{}}},collection:{type:[String,Array],default:""},action:{type:String,default:""},field:{type:String,default:""},orderby:{type:String,default:""},where:{type:[String,Object],default:""},pageData:{type:String,default:"add"},pageCurrent:{type:Number,default:1},pageSize:{type:Number,default:20},getcount:{type:[Boolean,String],default:!1},gettree:{type:[Boolean,String],default:!1},gettreepath:{type:[Boolean,String],default:!1},startwith:{type:String,default:""},limitlevel:{type:Number,default:10},groupby:{type:String,default:""},groupField:{type:String,default:""},distinct:{type:[Boolean,String],default:!1},foreignKey:{type:String,default:""},loadtime:{type:String,default:"auto"},manual:{type:Boolean,default:!1}},data:function(){return{mixinDatacomLoading:!1,mixinDatacomHasMore:!1,mixinDatacomResData:[],mixinDatacomErrorMessage:"",mixinDatacomPage:{},mixinDatacomError:null}},created:function(){var e=this;this.mixinDatacomPage={current:this.pageCurrent,size:this.pageSize,count:0},this.$watch((function(){var t=[];return["pageCurrent","pageSize","localdata","collection","action","field","orderby","where","getont","getcount","gettree","groupby","groupField","distinct"].forEach((function(n){t.push(e[n])})),t}),(function(t,n){if(e.loadtime!==or.manual){for(var r=!1,a=[],i=2;i<t.length;i++)t[i]!==n[i]&&(a.push(t[i]),r=!0);t[0]!==n[0]&&(e.mixinDatacomPage.current=e.pageCurrent),e.mixinDatacomPage.size=e.pageSize,e.onMixinDatacomPropsChange(r,a)}}))},methods:{onMixinDatacomPropsChange:function(e,t){},mixinDatacomEasyGet:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.getone,r=void 0!==n&&n,a=t.success,i=t.fail;this.mixinDatacomLoading||(this.mixinDatacomLoading=!0,this.mixinDatacomErrorMessage="",this.mixinDatacomError=null,this.mixinDatacomGet().then((function(t){e.mixinDatacomLoading=!1;var n=t.result,i=n.data,s=n.count;e.getcount&&(e.mixinDatacomPage.count=s),e.mixinDatacomHasMore=i.length<e.pageSize;var o=r?i.length?i[0]:void 0:i;e.mixinDatacomResData=o,a&&a(o)})).catch((function(t){e.mixinDatacomLoading=!1,e.mixinDatacomErrorMessage=t,e.mixinDatacomError=t,i&&i(t)})))},mixinDatacomGet:function(){var t,n,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};r=r||{},n="undefined"!=typeof __uniX&&__uniX?e.databaseForJQL(this.spaceInfo):e.database(this.spaceInfo);var a=r.action||this.action;a&&(n=n.action(a));var i=r.collection||this.collection;n=Array.isArray(i)?(t=n).collection.apply(t,(0,u.default)(i)):n.collection(i);var s=r.where||this.where;s&&Object.keys(s).length&&(n=n.where(s));var o=r.field||this.field;o&&(n=n.field(o));var c=r.foreignKey||this.foreignKey;c&&(n=n.foreignKey(c));var l=r.groupby||this.groupby;l&&(n=n.groupBy(l));var f=r.groupField||this.groupField;f&&(n=n.groupField(f)),!0===(void 0!==r.distinct?r.distinct:this.distinct)&&(n=n.distinct());var d=r.orderby||this.orderby;d&&(n=n.orderBy(d));var h=void 0!==r.pageCurrent?r.pageCurrent:this.mixinDatacomPage.current,p=void 0!==r.pageSize?r.pageSize:this.mixinDatacomPage.size,g=void 0!==r.getcount?r.getcount:this.getcount,v=void 0!==r.gettree?r.gettree:this.gettree,m=void 0!==r.gettreepath?r.gettreepath:this.gettreepath,y={getCount:g},k={limitLevel:void 0!==r.limitlevel?r.limitlevel:this.limitlevel,startWith:void 0!==r.startwith?r.startwith:this.startwith};return v&&(y.getTree=k),m&&(y.getTreePath=k),n=n.skip(p*(h-1)).limit(p).get(y),n}}}}function cr(e){return W(an.replace("{spaceId}",e.config.spaceId))}function lr(){return fr.apply(this,arguments)}function fr(){return fr=(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,s,o,u=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=u.length>0&&void 0!==u[0]?u[0]:{},n=t.openid,r=t.callLoginByWeixin,a=void 0!==r&&r,i=cr(this),"mp-weixin"===B){e.next=4;break}throw new Error("[SecureNetwork] API `initSecureNetworkByWeixin` is not supported on platform `".concat(B,"`"));case 4:if(!n||!a){e.next=6;break}throw new Error("[SecureNetwork] openid and callLoginByWeixin cannot be passed at the same time");case 6:if(!n){e.next=8;break}return e.abrupt("return",(i.mpWeixinOpenid=n,{}));case 8:return e.next=10,new Promise((function(e,t){uni.login({success:function(t){e(t.code)},fail:function(e){t(new Error(e.errMsg))}})}));case 10:return s=e.sent,o=this.importObject("uni-id-co",{customUI:!0}),e.next=14,o.secureNetworkHandshakeByWeixin({code:s,callLoginByWeixin:a});case 14:return i.mpWeixinCode=s,e.abrupt("return",{code:s});case 16:case"end":return e.stop()}}),e,this)}))),fr.apply(this,arguments)}function dr(e){return hr.apply(this,arguments)}function hr(){return hr=(0,f.default)((0,l.default)().mark((function e(t){var n;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=cr(this),e.abrupt("return",(n.initPromise||(n.initPromise=lr.call(this,t).then((function(e){return e})).catch((function(e){throw delete n.initPromise,e}))),n.initPromise));case 2:case"end":return e.stop()}}),e,this)}))),hr.apply(this,arguments)}function pr(e){!function(e){me=e}(e)}function gr(e){var t="mp-weixin"===B&&wx.canIUse("getAppBaseInfo"),n={getAppBaseInfo:t?uni.getAppBaseInfo:uni.getSystemInfo,getPushClientId:uni.getPushClientId};return function(r){return new Promise((function(a,i){t&&"getAppBaseInfo"===e?a(n[e]()):n[e]((0,d.default)((0,d.default)({},r),{},{success:function(e){a(e)},fail:function(e){i(e)}}))}))}}var vr=function(e){(0,h.default)(n,e);var t=(0,p.default)(n);function n(){var e;return(0,v.default)(this,n),e=t.call(this),e._uniPushMessageCallback=e._receivePushMessage.bind((0,i.default)(e)),e._currentMessageId=-1,e._payloadQueue=[],e}return(0,m.default)(n,[{key:"init",value:function(){var e=this;return Promise.all([gr("getAppBaseInfo")(),gr("getPushClientId")()]).then((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=(0,s.default)(t,2),r=n[0];r=void 0===r?{}:r;var a=r.appId,i=n[1];i=void 0===i?{}:i;var o=i.cid;if(!a)throw new Error("Invalid appId, please check the manifest.json file");if(!o)throw new Error("Invalid push client id");e._appId=a,e._pushClientId=o,e._seqId=Date.now()+"-"+Math.floor(9e5*Math.random()+1e5),e.emit("open"),e._initMessageListener()}),(function(t){throw e.emit("error",t),e.close(),t}))}},{key:"open",value:function(){var e=(0,f.default)((0,l.default)().mark((function e(){return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.abrupt("return",this.init());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"_isUniCloudSSE",value:function(e){if("receive"!==e.type)return!1;var t=e&&e.data&&e.data.payload;return!(!t||"UNI_CLOUD_SSE"!==t.channel||t.seqId!==this._seqId)}},{key:"_receivePushMessage",value:function(e){if(this._isUniCloudSSE(e)){var t=e&&e.data&&e.data.payload,n=t.action,r=t.messageId,a=t.message;this._payloadQueue.push({action:n,messageId:r,message:a}),this._consumMessage()}}},{key:"_consumMessage",value:function(){for(var e=this;;){var t=this._payloadQueue.find((function(t){return t.messageId===e._currentMessageId+1}));if(!t)break;this._currentMessageId++,this._parseMessagePayload(t)}}},{key:"_parseMessagePayload",value:function(e){var t=e.action,n=e.messageId,r=e.message;"end"===t?this._end({messageId:n,message:r}):"message"===t&&this._appendMessage({messageId:n,message:r})}},{key:"_appendMessage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("message",t)}},{key:"_end",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(e.messageId,e.message);this.emit("end",t),this.close()}},{key:"_initMessageListener",value:function(){uni.onPushMessage(this._uniPushMessageCallback)}},{key:"_destroy",value:function(){uni.offPushMessage(this._uniPushMessageCallback)}},{key:"toJSON",value:function(){return{appId:this._appId,pushClientId:this._pushClientId,seqId:this._seqId}}},{key:"close",value:function(){this._destroy(),this.emit("close")}}]),n}(j);var mr={tcb:Lt,tencent:Lt,aliyun:be,private:Ft,dcloud:Ft,alipay:Qt},yr=new(function(){function e(){(0,v.default)(this,e)}return(0,m.default)(e,[{key:"init",value:function(e){var t={},n=mr[e.provider];if(!n)throw new Error("未提供正确的provider参数");return t=n.init(e),function(e){e._initPromiseHub||(e._initPromiseHub=new M({createPromise:function(){var t=Promise.resolve();t=new Promise((function(e){setTimeout((function(){e()}),1)}));var n=e.auth();return t.then((function(){return n.getLoginState()})).then((function(e){return e?Promise.resolve():n.signInAnonymously()}))}}))}(t),pn(t),function(e){var t=e.uploadFile;e.uploadFile=function(e){return t.call(this,e)}}(t),function(e){e.database=function(t){if(t&&Object.keys(t).length>0)return e.init(t).database();if(this._database)return this._database;var n=Tn(Pn,{uniClient:e});return this._database=n,n},e.databaseForJQL=function(t){if(t&&Object.keys(t).length>0)return e.init(t).databaseForJQL();if(this._databaseForJQL)return this._databaseForJQL;var n=Tn(Pn,{uniClient:e,isJQL:!0});return this._databaseForJQL=n,n}}(t),function(e){e.getCurrentUserInfo=ar,e.chooseAndUploadFile=sr.initChooseAndUploadFile(e),Object.assign(e,{get mixinDatacom(){return ur(e)}}),e.SSEChannel=vr,e.initSecureNetworkByWeixin=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.openid,r=t.callLoginByWeixin,a=void 0!==r&&r;return dr.call(e,{openid:n,callLoginByWeixin:a})}}(e),e.setCustomClientInfo=pr,e.importObject=function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.customUI=t.customUI||e.customUI,e.parseSystemError=t.parseSystemError||e.parseSystemError,Object.assign(e.loadingOptions,t.loadingOptions),Object.assign(e.errorOptions,t.errorOptions),"object"==(0,o.default)(t.secretMethods)&&(e.secretMethods=t.secretMethods),e}({customUI:!1,loadingOptions:{title:"加载中...",mask:!0},errorOptions:{type:"modal",retry:!1}},n);var r=n,a=r.customUI,i=r.loadingOptions,s=r.errorOptions,u=r.parseSystemError,c=!a;return new Proxy({},{get:function(r,a){switch(a){case"toString":return"[object UniCloudObject]";case"toJSON":return{}}return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.fn,n=e.interceptorName,r=e.getCallbackArgs;return(0,f.default)((0,l.default)().mark((function e(){var a,i,s,o,u,c,f=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(a=f.length,i=new Array(a),s=0;s<a;s++)i[s]=f[s];return o=r?r({params:i}):{},e.prev=2,e.next=5,Q(Y(n,"invoke"),(0,d.default)({},o));case 5:return e.next=7,t.apply(void 0,i);case 7:return u=e.sent,e.next=10,Q(Y(n,"success"),(0,d.default)((0,d.default)({},o),{},{result:u}));case 10:return e.abrupt("return",u);case 13:return e.prev=13,e.t0=e["catch"](2),c=e.t0,e.next=18,Q(Y(n,"fail"),(0,d.default)((0,d.default)({},o),{},{error:c}));case 18:throw c;case 19:return e.prev=19,e.next=22,Q(Y(n,"complete"),c?(0,d.default)((0,d.default)({},o),{},{error:c}):(0,d.default)((0,d.default)({},o),{},{result:u}));case 22:return e.finish(19);case 23:case"end":return e.stop()}}),e,null,[[2,13,19,23]])})))}({fn:function(){var r=(0,f.default)((0,l.default)().mark((function r(){var p,g,v,m,y,k,_,w,b,x,I,S,T,A,O,E=arguments;return(0,l.default)().wrap((function(r){while(1)switch(r.prev=r.next){case 0:for(c&&uni.showLoading({title:i.title,mask:i.mask}),g=E.length,v=new Array(g),m=0;m<g;m++)v[m]=E[m];return y={name:t,type:P.OBJECT,data:{method:a,params:v}},"object"==(0,o.default)(n.secretMethods)&&function(e,t){var n=t.data.method,r=e.secretMethods||{},a=r[n]||r["*"];a&&(t.secretType=a)}(n,y),k=!1,r.prev=5,r.next=8,e.callFunction(y);case 8:p=r.sent,r.next=14;break;case 11:r.prev=11,r.t0=r["catch"](5),k=!0,p={result:new le(r.t0)};case 14:if(_=p.result||{},w=_.errSubject,b=_.errCode,x=_.errMsg,I=_.newToken,c&&uni.hideLoading(),I&&I.token&&I.tokenExpired&&(ge(I),ie(ee.REFRESH_TOKEN,(0,d.default)({},I))),!b){r.next=39;break}if(S=x,!k||!u){r.next=24;break}return r.next=20,u({objectName:t,methodName:a,params:v,errSubject:w,errCode:b,errMsg:x});case 20:if(r.t1=r.sent.errMsg,r.t1){r.next=23;break}r.t1=x;case 23:S=r.t1;case 24:if(!c){r.next=37;break}if("toast"!==s.type){r.next=29;break}uni.showToast({title:S,icon:"none"}),r.next=37;break;case 29:if("modal"===s.type){r.next=31;break}throw new Error("Invalid errorOptions.type: ".concat(s.type));case 31:return r.next=33,(0,f.default)((0,l.default)().mark((function e(){var t,n,r,a,i,s,o=arguments;return(0,l.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=o.length>0&&void 0!==o[0]?o[0]:{},n=t.title,r=t.content,a=t.showCancel,i=t.cancelText,s=t.confirmText,e.abrupt("return",new Promise((function(e,t){uni.showModal({title:n,content:r,showCancel:a,cancelText:i,confirmText:s,success:function(t){e(t)},fail:function(){e({confirm:!1,cancel:!0})}})})));case 2:case"end":return e.stop()}}),e)})))({title:"提示",content:S,showCancel:s.retry,cancelText:"取消",confirmText:s.retry?"重试":"确定"});case 33:if(T=r.sent,A=T.confirm,!s.retry||!A){r.next=37;break}return r.abrupt("return",h.apply(void 0,v));case 37:throw O=new le({subject:w,code:b,message:x,requestId:p.requestId}),O.detail=p.result,ie(ee.RESPONSE,{type:te.CLOUD_OBJECT,content:O}),O;case 39:return r.abrupt("return",(ie(ee.RESPONSE,{type:te.CLOUD_OBJECT,content:p.result}),p.result));case 40:case"end":return r.stop()}}),r,null,[[5,11]])})));function h(){return r.apply(this,arguments)}return h}(),interceptorName:"callObject",getCallbackArgs:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.params;return{objectName:t,methodName:a,params:n}}})}})}}(e)}(t),["callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile","chooseAndUploadFile"].forEach((function(e){if(t[e]){var n=t[e];t[e]=function(){return n.apply(t,Array.from(arguments))},t[e]=function(e,t){return function(n){var r=this,a=!1;if("callFunction"===t){var i=n&&n.type||P.DEFAULT;a=i!==P.DEFAULT}var s="callFunction"===t&&!a,o=this._initPromiseHub.exec();n=n||{};var u=ce(n),c=u.success,l=u.fail,f=u.complete,d=o.then((function(){return a?Promise.resolve():Q(Y(t,"invoke"),n)})).then((function(){return e.call(r,n)})).then((function(e){return a?Promise.resolve(e):Q(Y(t,"success"),e).then((function(){return Q(Y(t,"complete"),e)})).then((function(){return s&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e}),Promise.resolve(e)}))}),(function(e){return a?Promise.reject(e):Q(Y(t,"fail"),e).then((function(){return Q(Y(t,"complete"),e)})).then((function(){return ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e}),Promise.reject(e)}))}));if(!(c||l||f))return d;d.then((function(e){c&&c(e),f&&f(e),s&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e})}),(function(e){l&&l(e),f&&f(e),s&&ie(ee.RESPONSE,{type:te.CLOUD_FUNCTION,content:e})}))}}(t[e],e).bind(t)}})),t.init=this.init,t}}]),e}());t.uniCloud=yr,function(){var e=K,n={};if(e&&1===e.length)n=e[0],t.uniCloud=yr=yr.init(n),yr._isDefault=!0;else{var a,i=["database","getCurrentUserInfo","importObject"];a=e&&e.length>0?"应用有多个服务空间，请通过uniCloud.init方法指定要使用的服务空间":"应用未关联服务空间，请在uniCloud目录右键关联服务空间",[].concat(["auth","callFunction","uploadFile","deleteFile","getTempFileURL","downloadFile"],i).forEach((function(e){yr[e]=function(){if(r.error(a),-1===i.indexOf(e))return Promise.reject(new le({code:"SYS_ERR",message:a}));r.error(a)}}))}if(Object.assign(yr,{get mixinDatacom(){return ur(yr)}}),tr(yr),yr.addInterceptor=$,yr.removeInterceptor=G,yr.interceptObject=X,"app"===B&&(uni.__uniCloud=yr),"app"===B||"web"===B){var s=function(){return H||(H=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;function e(){return this}return void 0!==e()?e():new Function("return this")()}(),H)}();s.uniCloud=yr,s.UniCloudError=le}}();var kr=yr;t.default=kr}).call(this,n("0ee4"),n("ba7c")["default"])},9572:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={pages:[{path:"pages/login/login"},{path:"pages/index/index",style:{navigationBarTitleText:"职业健康",titleNView:!1,navigationStyle:"custom"}},{path:"pages/login/bindPhoneNum"},{path:"pages/login/bindWxInfo"},{path:"pages/index/signature"},{path:"pages/login/zfbAutho",style:{navigationBarTitleText:"支付宝授权"}},{path:"pages/index/search"},{path:"pages/reorientation/reorientation",style:{navigationBarTitleText:"",enablePullDownRefresh:!1}},{path:"pages/institution/index"},{path:"pages/institution/tjRecord"},{path:"pages/institution/tjResult"},{path:"pages/institution/tjBooking"},{path:"pages/institution/tjAppoint"},{path:"pages/institution/tjMessage"},{path:"pages/institution/tjAuth"},{path:"pages/institution/institution"},{path:"pages/institution/jgDetail"},{path:"pages/institution/jgForm"},{path:"pages/institution/zdResult"},{path:"pages/institution/ysReport"},{path:"pages/institution/addInformation"},{path:"pages/lifeCycle/lifeCycle",style:{navigationBarTitleText:"生命周期管理"}},{path:"pages/workInjuryRecognition/index"},{path:"pages/workInjuryRecognition/add"},{path:"pages/workInjuryRecognition/detail"},{path:"pages/institution/zdProcessFile"}],subPackages:[{root:"pages_train",pages:[{path:"pages/training/faceInput"},{path:"pages/training/faceValid"},{path:"pages/training/courses/courses"},{path:"pages/training/courses/course"},{path:"pages/training/courses/courseWithoutPersonal"},{path:"pages/training/courses/document"},{path:"pages/training/courses/search"},{path:"pages/training/myTraining",style:{enablePullDownRefresh:!0}},{path:"pages/training/detail",style:{enablePullDownRefresh:!0}},{path:"pages/training/test"},{path:"pages/training/myCourses",style:{enablePullDownRefresh:!0}},{path:"pages/training/publicCourses",style:{enablePullDownRefresh:!0}},{path:"pages/training/testAnswer"},{path:"pages/training/testResult"},{path:"pages/training/certificate"},{path:"pages/training/publicCourseDetail"}]},{root:"pages_learning",pages:[{path:"pages/learning/artInfo"},{path:"pages/learning/industryNews"}]},{root:"pages_remote",pages:[{path:"pages/remote/list"},{path:"pages/remote/meeting"},{path:"pages/remote/remoteClinicList"}]},{root:"pages_user",pages:[{path:"pages/user/boundEnterprise"},{path:"pages/user/h5login"},{path:"pages/user/ppe"},{path:"pages/user/disease"},{path:"pages/user/blacklist"},{path:"pages/user/info"},{path:"pages/user/wjdc/list"},{path:"pages/user/wjdc/add"},{path:"pages/user/wjdc/detail"},{path:"pages/user/modify"},{path:"pages/user/modifyPhone"},{path:"pages/user/comment"},{path:"pages/user/myTraining"},{path:"pages/identify/index"},{path:"pages/identify/apply"},{path:"pages/identify/jgForm"},{path:"pages/identify/jgDetail"},{path:"pages/identify/jdResult"},{path:"pages/identify/jdProcessFile"},{path:"pages/user/complaints/list",style:{softinputMode:"adjustResize",enablePullDownRefresh:!0}},{path:"pages/user/signImg",style:{enablePullDownRefresh:!1,navigationStyle:"custom",pageOrientation:"landscape"}},{path:"pages/user/signature",style:{enablePullDownRefresh:!1,navigationStyle:"custom",pageOrientation:"landscape"}},{path:"pages/user/checkDetail",style:{enablePullDownRefresh:!1,navigationStyle:"custom"}},{path:"pages/user/ppeSign",style:{enablePullDownRefresh:!1,navigationStyle:"custom",pageOrientation:"landscape"}},{path:"pages/user/complaints/detail",style:{softinputMode:"adjustResize"}},{path:"pages/user/complaints/scoring"},{path:"pages/user/physicalExamination"},{path:"pages/user/occupationalHistory"},{path:"pages/user/physicalAndCheckResult"},{path:"pages/user/diseasesOverhaul"},{path:"pages/user/diseasesOverhaulDetail"},{path:"pages/user/message"},{path:"pages/user/violationInfo"},{path:"pages/user/questionnaire"},{path:"pages/user/questionnaireDetail"},{path:"pages/user/tjAppointmentInfo"},{path:"pages/user/tjAppointmentDetailInfo"},{path:"pages/user/employeeList"},{path:"pages/user/addItems"},{path:"pages/user/indicatorsTrend"},{path:"pages/eHealthRecord/index"},{path:"pages/eHealthRecord/auth"},{path:"pages/eHealthRecord/complaint"},{path:"pages/eHealthRecord/basicInfo"},{path:"pages/eHealthRecord/exam"},{path:"pages/eHealthRecord/diagnose"},{path:"pages/eHealthRecord/injury"},{path:"pages/eHealthRecord/healthManage"},{path:"pages/eHealthRecord/servic"},{path:"pages/eHealthRecord/report"},{path:"pages/eHealthRecord/warning"},{path:"pages/eHealthRecord/harmFactor"},{path:"pages/workInjury/query"}]},{root:"pages_lifeCycle",pages:[{path:"/pages/followUp/followUp",style:{navigationBarTitleText:"随访记录"}},{path:"/pages/Appointment/Appointment",style:{navigationBarTitleText:"就诊预约"}},{path:"/pages/Appointment/AppointmentRecord",style:{navigationBarTitleText:"预约记录"}},{path:"/pages/MedicationServices/MedicationGuidance",style:{navigationBarTitleText:"用药服务"}},{path:"/pages/MedicationServices/ApplyMedicationGuidance",style:{navigationBarTitleText:"申请用药指导"}},{path:"/pages/MedicationServices/MedicationGuidanceDetail",style:{navigationBarTitleText:"用药指导详情"}},{path:"/pages/MedicationServices/MedicationScheme",style:{navigationBarTitleText:"用药方案"}},{path:"/pages/MedicationServices/MedicationGuidanceInfo",style:{navigationBarTitleText:"用药指导资讯"}},{path:"/pages/MedicationServices/MedicationGuidanceInfoDetail",style:{navigationBarTitleText:"用药指导资讯详情"}},{path:"/pages/treatmentService/treatmentService",style:{navigationBarTitleText:"诊疗服务"}},{path:"/pages/treatmentService/treatmentServiceInfo",style:{navigationBarTitleText:"诊疗详情"}},{path:"/pages/recoveredServices/recoveredServices",style:{navigationBarTitleText:"康复指导服务"}},{path:"/pages/recoveredServices/addrecoveredServices",style:{navigationBarTitleText:"申请康复指导"}},{path:"/pages/recoveredServices/recoveredServicesDetail",style:{navigationBarTitleText:"康复指导申请详情"}},{path:"/pages/recoveredServices/downRecoveredServices",style:{navigationBarTitleText:"下载康复指导记录"}},{path:"/pages/recoveredServices/recoveredServicesRecord",style:{navigationBarTitleText:"服务记录"}}]}],globalStyle:{"mp-360":{navigationStyle:"custom"},"mp-alipay":{transparentTitle:"always",allowsBounceVertical:"NO"},navigationStyle:"custom",navigationBarTextStyle:"black","app-plus":{scrollIndicator:"none",bounce:"none"}},usingComponts:!0,easycom:{custom:{autoscan:!1,"grace(.*)":"@/graceUI/components/grace$1.vue"}},condition:{current:0,list:[{name:"",path:"",query:""}]}}},acb1:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&(0,r.default)(e,t)},n("7a76"),n("c9b5"),n("6a54");var r=function(e){return e&&e.__esModule?e:{default:e}}(n("e668"))},cad9:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=(0,a.default)();return function(){var n,a=(0,r.default)(e);if(t){var s=(0,r.default)(this).constructor;n=Reflect.construct(a,arguments,s)}else n=a.apply(this,arguments);return(0,i.default)(this,n)}},n("6a88"),n("bf0f"),n("7996");var r=s(n("f1f8")),a=s(n("6c31")),i=s(n("62b0"));function s(e){return e&&e.__esModule?e:{default:e}}},d2c4:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=s,n("6a88"),n("bf0f"),n("7996"),n("aa9c");var r=i(n("e668")),a=i(n("6c31"));function i(e){return e&&e.__esModule?e:{default:e}}function s(e,n,i){return(0,a.default)()?t.default=s=Reflect.construct.bind():t.default=s=function(e,t,n){var a=[null];a.push.apply(a,t);var i=Function.bind.apply(e,a),s=new i;return n&&(0,r.default)(s,n.prototype),s},s.apply(null,arguments)}},d441:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return-1!==Function.toString.call(e).indexOf("[native code]")},n("5ef2"),n("c9b5"),n("bf0f"),n("ab80")},e668:function(e,t,n){"use strict";function r(e,n){return t.default=r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},r(e,n)}n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,n("8a8d")},f1f8:function(e,t,n){"use strict";function r(e){return t.default=r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},r(e)}n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=r,n("8a8d"),n("926e")},f478:function(e,t,n){"use strict";n("6a54"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},n("7a76"),n("c9b5")},f555:function(e,t,n){"use strict";var r=n("85c1"),a=n("ab4a"),i=n("e4ca"),s=n("471d"),o=n("af9e"),u=r.RegExp,c=u.prototype,l=a&&o((function(){var e=!0;try{u(".","d")}catch(l){e=!1}var t={},n="",r=e?"dgimsy":"gimsy",a=function(e,r){Object.defineProperty(t,e,{get:function(){return n+=r,!0}})},i={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var s in e&&(i.hasIndices="d"),i)a(s,i[s]);var o=Object.getOwnPropertyDescriptor(c,"flags").get.call(t);return o!==r||n!==r}));l&&i(c,"flags",{configurable:!0,get:s})}}]);