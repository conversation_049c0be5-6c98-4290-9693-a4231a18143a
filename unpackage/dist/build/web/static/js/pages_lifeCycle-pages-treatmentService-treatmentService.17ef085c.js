(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_lifeCycle-pages-treatmentService-treatmentService"],{"4c12":function(e,t,a){"use strict";var i=a("f917"),n=a.n(i);n.a},6001:function(e,t,a){"use strict";a.r(t);var i=a("62f71"),n=a.n(i);for(var s in i)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return i[e]}))}(s);t["default"]=n.a},"62f71":function(e,t,a){"use strict";(function(e){a("6a54");var i=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i(a("2634")),s=i(a("9b1b")),r=i(a("2fdc")),o=i(a("2246")),c={data:function(){return{formDate:{range:"",mechanism:"",physician:"",result:"",reservationRange:"",reservationMechanism:"",reservationPhysician:""},pageParams:{pageNum:1,pageSize:9999,isAsc:"desc",orderBy:"createTime"},recordsList:[]}},onLoad:function(){this.getRecordsList()},methods:{getRecordsList:function(){var t=this;return(0,r.default)((0,n.default)().mark((function a(){var i,r,c,l,u,v;return(0,n.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,u=(0,s.default)((0,s.default)({},t.pageParams),{},{physician:t.formDate.physician||void 0,mechanism:t.formDate.mechanism||void 0,result:t.formDate.result||void 0,fromTreatmentTime:(null===(i=t.formDate.range)||void 0===i?void 0:i[0])||void 0,toTreatmentTime:(null===(r=t.formDate.range)||void 0===r?void 0:r[1])||void 0,reservationPhysician:t.formDate.reservationPhysician||void 0,reservationMechanism:t.formDate.reservationMechanism||void 0,fromReservationTime:(null===(c=t.formDate.reservationRange)||void 0===c?void 0:c[0])||void 0,toReservationTime:(null===(l=t.formDate.reservationRange)||void 0===l?void 0:l[1])||void 0}),a.next=4,o.default.treatmentInformationList(u);case 4:v=a.sent,200===v.status&&(t.recordsList=v.data.list),a.next=11;break;case 8:a.prev=8,a.t0=a["catch"](0),e.error("获取诊疗记录列表失败:",a.t0);case 11:case"end":return a.stop()}}),a,null,[[0,8]])})))()},gotoTreatmentServiceInfo:function(t){e.log("查看诊疗记录：",t),uni.navigateTo({url:"/pages_lifeCycle/pages/treatmentService/treatmentServiceInfo?id=".concat(t)})},change:function(t){e.log(t)},reset:function(){this.formDate={range:"",mechanism:"",physician:"",result:"",reservationRange:"",reservationMechanism:"",reservationPhysician:""},this.pageParams={pageNum:1,pageSize:9999,isAsc:"desc",orderBy:"createTime"},this.getRecordsList()},search:function(){this.pageParams.pageNum=1,this.getRecordsList(),this.$refs.popup.close()},openSearchPopup:function(){this.$refs.popup.open("right")}}};t.default=c}).call(this,a("ba7c")["default"])},"9c83":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){return i}));var i={gracePage:a("1367").default,uniIcons:a("67fa").default,uniPopup:a("7ddc").default,uniDatetimePicker:a("ca85").default,uniEasyinput:a("e7ae").default},n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF"}},[a("my-header",{attrs:{slot:"gHeader",title:"诊疗服务记录"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body container",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"search"},[a("v-uni-view",{staticClass:"searchInfo",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.openSearchPopup.apply(void 0,arguments)}}},[a("uni-icons",{attrs:{type:"search",size:"20",color:"dodgerblue"}}),a("v-uni-text",[e._v("查询")])],1)],1),a("v-uni-view",{staticClass:"mechanism"},e._l(e.recordsList,(function(t){return a("v-uni-view",{key:t.id,staticClass:"mechanismList"},[a("v-uni-view",{staticClass:"left"},[a("v-uni-view",{staticClass:"text"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗时间:")]),a("v-uni-view",{staticClass:"content"},[e._v(e._s(t.treatmentDate))])],1),a("v-uni-view",{staticClass:"text"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗机构:")]),a("v-uni-view",{staticClass:"content"},[e._v(e._s(t.stationName))])],1),a("v-uni-view",{staticClass:"text"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗医师:")]),a("v-uni-view",{staticClass:"content"},[e._v(e._s(t.doctorName))])],1),a("v-uni-view",{staticClass:"text"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗结果:")]),a("v-uni-view",{staticClass:"content"},[e._v(e._s(t.treatmentResult))])],1)],1),a("v-uni-view",{staticClass:"right"},[a("v-uni-view",{staticClass:"tag tag_green",on:{click:function(a){arguments[0]=a=e.$handleEvent(a),e.gotoTreatmentServiceInfo(t.id)}}},[e._v("查看")])],1)],1)})),1),a("uni-popup",{ref:"popup",staticStyle:{"z-index":"90"},attrs:{"background-color":"#fff","mask-background-color":"#0000000"},on:{change:function(t){arguments[0]=t=e.$handleEvent(t),e.change.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"popup-content"},[a("v-uni-view",{staticClass:"forms"},[a("v-uni-view",{staticClass:"forms_item"},[a("v-uni-view",{staticClass:"label"},[e._v("就诊时间")]),a("uni-datetime-picker",{attrs:{type:"daterange",rangeSeparator:"至"},model:{value:e.formDate.range,callback:function(t){e.$set(e.formDate,"range",t)},expression:"formDate.range"}})],1),a("v-uni-view",{staticClass:"forms_item"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗机构")]),a("uni-easyinput",{staticClass:"uni-mt-5",attrs:{trim:"all",placeholder:"请输入诊疗机构"},model:{value:e.formDate.mechanism,callback:function(t){e.$set(e.formDate,"mechanism",t)},expression:"formDate.mechanism"}})],1),a("v-uni-view",{staticClass:"forms_item"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗医师")]),a("uni-easyinput",{staticClass:"uni-mt-5",attrs:{trim:"all",placeholder:"请输入诊疗医师"},model:{value:e.formDate.physician,callback:function(t){e.$set(e.formDate,"physician",t)},expression:"formDate.physician"}})],1),a("v-uni-view",{staticClass:"forms_item"},[a("v-uni-view",{staticClass:"label"},[e._v("诊疗结果")]),a("uni-easyinput",{staticClass:"uni-mt-5",attrs:{trim:"all",placeholder:"请输入诊疗结果"},model:{value:e.formDate.result,callback:function(t){e.$set(e.formDate,"result",t)},expression:"formDate.result"}})],1),a("v-uni-view",{staticClass:"forms_item"},[a("v-uni-view",{staticClass:"label"},[e._v("预约就诊时间")]),a("uni-datetime-picker",{attrs:{type:"daterange",rangeSeparator:"至"},model:{value:e.formDate.reservationRange,callback:function(t){e.$set(e.formDate,"reservationRange",t)},expression:"formDate.reservationRange"}})],1),a("v-uni-view",{staticClass:"forms_item"},[a("v-uni-view",{staticClass:"label"},[e._v("预约诊疗机构")]),a("uni-easyinput",{staticClass:"uni-mt-5",attrs:{trim:"all",placeholder:"请输入预约诊疗机构"},model:{value:e.formDate.reservationMechanism,callback:function(t){e.$set(e.formDate,"reservationMechanism",t)},expression:"formDate.reservationMechanism"}})],1),a("v-uni-view",{staticClass:"forms_item"},[a("v-uni-view",{staticClass:"label"},[e._v("预约诊疗医师")]),a("uni-easyinput",{staticClass:"uni-mt-5",attrs:{trim:"all",placeholder:"请输入预约诊疗医师"},model:{value:e.formDate.reservationPhysician,callback:function(t){e.$set(e.formDate,"reservationPhysician",t)},expression:"formDate.reservationPhysician"}})],1),a("v-uni-view",{staticClass:"forms_btn"},[a("v-uni-button",{staticClass:"reset_btn",attrs:{type:"default",plain:"true"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.reset.apply(void 0,arguments)}}},[e._v("重置")]),a("v-uni-button",{staticClass:"search_btn",attrs:{type:"primary"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.search.apply(void 0,arguments)}}},[e._v("查询")])],1)],1)],1)],1)],1)],1)},s=[]},b942:function(e,t,a){var i=a("c86c");t=i(!1),t.push([e.i,"@media screen and (min-width:960px){[data-v-87b7949a] .uni-date-range--x{background-color:#fff;position:absolute;top:-5rem!important;z-index:999;border:1px solid #ebeef5;box-shadow:0 2px 12px 0 rgba(0,0,0,.1);border-radius:4px}[data-v-87b7949a] .uni-popper__arrow{display:none}}.tag[data-v-87b7949a]{padding:%?10?% %?30?%;border-radius:%?10?%;font-size:%?24?%}.tag_green[data-v-87b7949a]{background-color:rgba(33,189,159,.18823529411764706);color:#21bd9f;border:1px solid #21bd9f}.tag_black[data-v-87b7949a]{background-color:rgba(91,91,91,.18823529411764706);color:#5b5b5b;border:1px solid #5b5b5b}.grace-body[data-v-87b7949a]{min-height:calc(100vh - %?120?%);padding-top:%?30?%;background-color:#f6f6f6}.grace-body .search[data-v-87b7949a]{display:flex;justify-content:center;align-items:flex-end;flex-direction:column;color:#169bd5;margin-bottom:%?30?%}.grace-body .search .searchInfo[data-v-87b7949a]{display:flex;align-items:center}.mechanismList[data-v-87b7949a]{background-color:#fff;border-radius:%?8?%;padding:%?30?% %?30?%;margin-bottom:%?20?%;display:flex;align-items:center;justify-content:space-between}.mechanismList .left[data-v-87b7949a]{flex:1;margin-right:%?20?%;overflow:hidden;\n  /* 隐藏超出部分 */white-space:nowrap;\n  /* 禁止换行 */text-overflow:ellipsis\n  /* 使用省略号表示超出部分 */}.mechanismList .right[data-v-87b7949a]{color:#1e90ff;font-size:%?28?%}.mechanismList .left .text[data-v-87b7949a]{display:flex;margin-bottom:%?10?%;align-items:center}.mechanismList .left .text .label[data-v-87b7949a]{font-size:%?28?%;margin-right:%?10?%}.mechanismList .left .text .content[data-v-87b7949a]{font-size:%?28?%;overflow:hidden;\n  /* 隐藏超出部分 */white-space:nowrap;\n  /* 禁止换行 */text-overflow:ellipsis\n  /* 使用省略号表示超出部分 */}.popup-content[data-v-87b7949a]{position:relative;width:70vw;height:88vh;padding:%?40?%;padding-top:%?120?%}.forms_item[data-v-87b7949a]{margin-bottom:%?20?%}.forms_item .label[data-v-87b7949a]{font-size:%?28?%;margin-bottom:%?20?%}.forms_item uni-input[data-v-87b7949a]{border:1px solid #f6f6f6;border-radius:%?5?%}.forms_btn[data-v-87b7949a]{position:absolute;bottom:5%;width:100%;left:0;display:flex;align-items:center;justify-content:space-around}.forms_btn uni-button[data-v-87b7949a]{margin:0;font-size:%?28?%;padding:0 %?80?%}.reset_btn[data-v-87b7949a]{background-color:#5b5b5b}.search_btn[data-v-87b7949a]{background-color:#169bd5}",""]),e.exports=t},e901:function(e,t,a){"use strict";a.r(t);var i=a("9c83"),n=a("6001");for(var s in n)["default"].indexOf(s)<0&&function(e){a.d(t,e,(function(){return n[e]}))}(s);a("4c12");var r=a("828b"),o=Object(r["a"])(n["default"],i["b"],i["c"],!1,null,"87b7949a",null,!1,i["a"],void 0);t["default"]=o.exports},f917:function(e,t,a){var i=a("b942");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[e.i,i,""]]),i.locals&&(e.exports=i.locals);var n=a("967d").default;n("2193bd84",i,!0,{sourceMap:!1,shadowMode:!1})}}]);