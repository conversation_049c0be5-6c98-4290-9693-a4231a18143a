(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_train-pages-training-faceValid"],{"319c":function(n,t,a){"use strict";a.r(t);var e=a("9ec6"),i=a.n(e);for(var o in e)["default"].indexOf(o)<0&&function(n){a.d(t,n,(function(){return e[n]}))}(o);t["default"]=i.a},3586:function(n,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return e}));var e={gracePage:a("1367").default},i=function(){var n=this,t=n.$createElement,a=n._self._c||t;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[a("my-header",{attrs:{slot:"gHeader",title:"验证"},slot:"gHeader"}),a("v-uni-view",{staticStyle:{height:"200px",width:"auto",margin:"auto"},attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-button",{on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.faceValid.apply(void 0,arguments)}}},[n._v("人员验证")])],1)],1)},o=[]},9555:function(n,t,a){"use strict";a.r(t);var e=a("3586"),i=a("319c");for(var o in i)["default"].indexOf(o)<0&&function(n){a.d(t,n,(function(){return i[n]}))}(o);var r=a("828b"),c=Object(r["a"])(i["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);t["default"]=c.exports},"9ec6":function(n,t,a){"use strict";(function(n){a("6a54");var e=a("f5bd").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,a("c223");var i=e(a("2634")),o=e(a("2fdc")),r=e(a("7703")),c={data:function(){return{option:{},hasImage:!1}},onLoad:function(n){this.option=n},methods:{faceValid:function(){try{var t=this;uni.chooseMedia({count:1,sizeType:["original"],mediaType:["image"],sourceType:["camera"],camera:"front",success:function(a){var e=a.tempFiles[0].tempFilePath;t.hasImage=!0,t.option.companyId instanceof Array&&(t.option.companyId=t.option.companyId[0]),uni.uploadFile({url:r.default.apiServer+"app/user/ExmaVerifyImage",filePath:e,name:"file",formData:{EnterpriseID:t.option.companyId,imageUserId:t.option._id,personalTrainingId:t.option.personalTrainingId},fail:function(t){n.log("上传文件错误",t)},success:function(){var n=(0,o.default)((0,i.default)().mark((function n(a){var e;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:e=JSON.parse(a.data).data,200===a.statusCode&&(e?(uni.showToast({title:"验证成功，开始考试",duration:800}),uni.navigateTo({url:"/pages_train/pages/training/test?personalTrainingId=".concat(t.option.personalTrainingId,"&companyId=").concat(t.option.companyId,"&id=").concat(t.option._id,"&bigTest=1")})):uni.showModal({confirmText:"确认",title:"验证失败",content:"重新拍照，再次进行验证",confirmColor:"#3B8BFF",cancelColor:"#222222",success:function(n){n.confirm?t.faceValid():n.cancel&&uni.navigateBack()}}));case 2:case"end":return n.stop()}}),n)})));return function(t){return n.apply(this,arguments)}}()})},fail:function(n){},complete:function(n){}})}catch(a){}}}};t.default=c}).call(this,a("ba7c")["default"])}}]);