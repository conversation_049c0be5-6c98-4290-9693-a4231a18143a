(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages_user-pages-user-tjAppointmentInfo"],{"1dbd":function(t,e,a){"use strict";var n=a("ac5f"),i=a("1fc1"),r=a("41c7"),o=a("ae5c"),c=function(t,e,a,s,l,d,u,f){var p,h,v=l,m=0,g=!!u&&o(u,f);while(m<s)m in a&&(p=g?g(a[m],m,e):a[m],d>0&&n(p)?(h=i(p),v=c(t,e,p,h,v,d-1)-1):(r(v+1),t[v]=p),v++),m++;return v};t.exports=c},3748:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return n}));var n={gracePage:a("1367").default,uniPopup:a("7ddc").default,graceDate:a("a75a").default,graceDialog:a("6fb7").default},i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("gracePage",{attrs:{headerBG:"#008AFF",statusBarBG:"#008AFF",bounding:!1}},[a("my-header",{attrs:{slot:"gHeader",title:"套餐信息"},slot:"gHeader"}),a("v-uni-view",{staticClass:"grace-body",attrs:{slot:"gBody"},slot:"gBody"},[a("v-uni-view",{staticClass:"infoCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[t._v("基本信息")])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",[a("v-uni-text",[t._v("姓名："+t._s(t.userInfo.name))])],1),a("v-uni-view",[a("v-uni-text",[t._v("年龄："+t._s(t.tjPlan.age))])],1),a("v-uni-view",[a("v-uni-text",[t._v("性别："+t._s("0"===t.tjPlan.gender?"男":"女"))])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",[a("v-uni-text",[t._v("手机："+t._s(t.userInfo.phoneNum))])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",[a("v-uni-text",[t._v("证件号："+t._s(t.userInfo.idNo))])],1)],1),a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",[a("v-uni-text",[t._v("选检额度："+t._s(t.selectionQuota))])],1)],1)],1),a("v-uni-view",{staticClass:"itemsCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[t._v("套餐项目信息")])],1)],1),t._l(t.requiredItems,(function(e,n){return a("v-uni-view",{key:n},[a("v-uni-view",{class:n>5&&t.contract?"none":""},[a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticClass:"itemLabel"},[a("v-uni-text",[t._v(t._s(e.name))])],1),a("v-uni-view",{staticClass:"itemContent",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.openDialog(e.comments)}}},[a("v-uni-text",[t._v("详情")])],1)],1),a("v-uni-view",{staticClass:"splitLine"})],1)],1)})),a("v-uni-view",{staticClass:"cardBtns"},[a("v-uni-view",[a("v-uni-button",{staticClass:"cancelBtn",attrs:{size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.contract=!t.contract}}},[t._v(t._s(t.contract?"展开更多":"收起"))])],1)],1)],2),a("v-uni-view",{staticClass:"itemsCard grace-box-shadow",staticStyle:{"padding-bottom":"calc(15px - 12px)"}},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[t._v("职业病体检")])],1)],1),t._l(t.occupationalItems,(function(e,n){return a("v-uni-view",{key:n},[a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticClass:"itemLabel"},[a("v-uni-text",[t._v(t._s(e.name))])],1),a("v-uni-view",{staticClass:"itemContent",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.openDialog(e.comments)}}},[a("v-uni-text",[t._v("详情")])],1)],1),a("v-uni-view",{staticClass:"splitLine"})],1)})),0===t.occupationalItems.length?a("v-uni-view",{staticClass:"cardItem desc",staticStyle:{"margin-bottom":"12px"}},[a("v-uni-view",[t._v("无需检查")])],1):t._e()],2),a("v-uni-view",{staticClass:"itemsCard grace-box-shadow"},[a("v-uni-view",{staticClass:"cardTitle"},[a("v-uni-view",{staticClass:"titlePoint"}),a("v-uni-view",{staticClass:"titleText"},[a("v-uni-text",[t._v("选检项目")])],1)],1),t._l(t.optionalItemsSelected,(function(e,n){return a("v-uni-view",{key:n},[a("v-uni-view",{staticClass:"cardItem"},[a("v-uni-view",{staticClass:"itemLabel"},[a("v-uni-text",[t._v(t._s(e.name))])],1),a("v-uni-view",{staticClass:"itemContent",on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.openDialog(e.comments)}}},[a("v-uni-text",[t._v("详情")])],1)],1),a("v-uni-view",{staticClass:"splitLine"})],1)})),a("v-uni-view",{staticClass:"cardItem desc"},[a("v-uni-view",[t._v("加项项目为套餐外项目，超过单位付费额")]),a("v-uni-view",[t._v("度需额外收费，体检时到缴费处结清即可")])],1),a("v-uni-view",{staticClass:"cardBtns"},[a("v-uni-view",[a("v-uni-button",{staticClass:"updateBtn",attrs:{size:"mini"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.gotoAddItems.apply(void 0,arguments)}}},[t._v("添加选检")])],1)],1)],2),a("v-uni-view",{staticStyle:{display:"flex","flex-direction":"column","justify-content":"center","align-items":"center","margin-bottom":"130rpx"}}),a("uni-popup",{ref:"popup",staticClass:"popBox",attrs:{type:"bottom","background-color":"#fff"}},[a("v-uni-view",{staticClass:"popHeader"},[a("v-uni-text",{staticStyle:{opacity:"0.5"}},[t._v("请选择体检日期")])],1),a("v-uni-view",{staticClass:"popBody"},[a("graceDate",{ref:"graceDate",attrs:{appointmentInfo:t.appointmentInfo,show:t.showGraceDate,borderRadius:"100rpx",isLunar:!1,bgColor:"#FFFFFF"},on:{changeDate:function(e){arguments[0]=e=t.$handleEvent(e),t.changeDate.apply(void 0,arguments)},closeDate:function(e){arguments[0]=e=t.$handleEvent(e),t.closeDate.apply(void 0,arguments)}}})],1)],1),a("graceDialog",{ref:"detailDialog",staticClass:"itemDialog",attrs:{title:"检查项目名称",titleSize:"34rpx",show:t.showDialog,isCloseBtn:!1},on:{closeDialog:function(e){arguments[0]=e=t.$handleEvent(e),t.closeDialog.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"dialogContent",attrs:{slot:"content"},slot:"content"},t._l(t.dialogContent,(function(e){return a("v-uni-view",{key:e},[a("v-uni-text",[t._v(t._s(e))])],1)})),1),a("v-uni-view",{staticClass:"dialogBtns",attrs:{slot:"btns"},slot:"btns"},[a("v-uni-text",{staticClass:"grace-dialog-buttons IKnowBtn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.closeDialog.apply(void 0,arguments)}}},[t._v("我知道了")])],1)],1)],1),a("v-uni-view",{staticClass:"grace-footer grace-nowrap grace-box-shadow",attrs:{slot:"gFooter"},slot:"gFooter"},[a("v-uni-view",{staticClass:"totalCount"},[a("v-uni-view",{staticStyle:{display:"flex","flex-direction":"column","align-items":"flex-end"}},[a("v-uni-view",{staticStyle:{display:"flex"}},[a("v-uni-text",[t._v("共计：")]),a("v-uni-text",{staticStyle:{color:"#EA5C3F"}},[t._v("¥"+t._s(t.totalPrice))]),a("v-uni-text",{staticStyle:{"margin-left":"24rpx"}},[t._v("自费：")]),a("v-uni-text",{staticStyle:{"font-size":"24rpx",color:"#EA5C3F"}},[t._v("¥")]),a("v-uni-text",{staticStyle:{"font-size":"32rpx",color:"#EA5C3F"}},[t._v(t._s(t.selfPay))])],1),a("v-uni-view",[a("v-uni-view",{staticStyle:{"font-size":"22rpx",color:"#EA5C3F"}},[t._v("单位付费¥"+t._s(t.enterprisePay))])],1)],1),a("v-uni-view",{staticStyle:{"margin-left":"37rpx"}},[a("v-uni-button",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.openPop.apply(void 0,arguments)}}},[t._v("立即预约")])],1)],1)],1)],1)},r=[]},"3cef":function(t,e,a){"use strict";a.r(e);var n=a("3748"),i=a("441a");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("9e24");var o=a("828b"),c=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"0a78185d",null,!1,n["a"],void 0);e["default"]=c.exports},"3d47e":function(t,e,a){"use strict";a("6a54"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("e966"),a("64aa"),a("23f4"),a("7d2f"),a("5c47"),a("9c4e"),a("ab80"),a("0506");var n,i,r,o=new Array(100),c=new Array(12);function s(t,e){return t>>e&1}function l(t,e,a){return t<1921?"":(e=parseInt(e)>0?e-1:11,function(){var t,e,a,l;r=3!=arguments.length?new Date:new Date(arguments[0],arguments[1],arguments[2]);var d=!1,u=r.getYear();for(u<1900&&(u+=1900),t=365*(u-1921)+Math.floor((u-1921)/4)+c[r.getMonth()]+r.getDate()-38,r.getYear()%4==0&&r.getMonth()>1&&t++,e=0;;e++){for(l=o[e]<4095?11:12,a=l;a>=0;a--){if(t<=29+s(o[e],a)){d=!0;break}t=t-29-s(o[e],a)}if(d)break}1921+e,n=l-a+1,i=t,12==l&&(n==Math.floor(o[e]/65536)+1&&(n=1-n),n>Math.floor(o[e]/65536)+1&&n--)}(t,e,a),function(){var t="";return t+=i<11?"初":i<20?"十":i<30?"廿":"三十",i%10==0&&10!=i||(t+="一二三四五六七八九十".charAt((i-1)%10)),t}())}o=new Array(2635,333387,1701,1748,267701,694,2391,133423,1175,396438,3402,3749,331177,1453,694,201326,2350,465197,3221,3402,400202,2901,1386,267611,605,2349,137515,2709,464533,1738,2901,330421,1242,2651,199255,1323,529706,3733,1706,398762,2741,1206,267438,2647,1318,204070,3477,461653,1386,2413,330077,1197,2637,268877,3365,531109,2900,2922,398042,2395,1179,267415,2635,661067,1701,1748,398772,2742,2391,330031,1175,1611,200010,3749,527717,1452,2742,332397,2350,3222,268949,3402,3493,133973,1386,464219,605,2349,334123,2709,2890,267946,2773,592565,1210,2651,395863,1323,2707,265877),c[0]=0,c[1]=31,c[2]=59,c[3]=90,c[4]=120,c[5]=151,c[6]=181,c[7]=212,c[8]=243,c[9]=273,c[10]=304,c[11]=334;var d={name:"graceCountd",props:{show:{type:Boolean,default:!1},currentDate:{type:String,default:""},isTime:{type:Boolean,default:!0},top:{type:String,default:"44px"},zIndex:{type:String,default:"1"},bgColor:{type:String,default:"#F6F7F8"},activeBgColor:{type:String,default:"#3688FF"},borderRadius:{type:String,default:"6rpx"},isLunar:{type:Boolean,default:!0}},created:function(){this.initTime(),this.realshow=this.show},watch:{currentDate:function(){this.initTime()},show:function(t){t?this.open():this.hide()}},data:function(){return{weeks:["一","二","三","四","五","六","日"],cYear:2016,cMonth:6,cMonthStr:"06",cDay:"01",days:"",currentDayIn:"",currentTimeIn:"",realshow:!1}},methods:{stopfun:function(){},timechange:function(t){this.currentTimeIn=t.detail.value},getDaysInOneMonth:function(){var t=new Date(this.cYear,this.cMonth,0);return t.getDate()},getDay:function(){var t=new Date(this.cYear,this.cMonth-1,0);return t.getDay()},prevYear:function(){this.cYear=this.cYear-1,this.changeMonth()},prevMonth:function(){this.cMonth=this.cMonth-1,this.cMonth<1&&(this.cMonth=12,this.cYear=this.cYear-1),this.cMonthStr=this.cMonth<10?"0"+this.cMonth:this.cMonth,this.changeMonth()},nextMonth:function(){this.cMonth=this.cMonth+1,this.cMonth>12&&(this.cMonth=1,this.cYear=this.cYear+1),this.cMonthStr=this.cMonth<10?"0"+this.cMonth:this.cMonth,this.changeMonth()},nextYear:function(){this.cYear=this.cYear+1,this.changeMonth()},changeMonth:function(){for(var t=[],e=this.getDaysInOneMonth(),a=this.getDay(),n=0,i=0-a;i<e;i++)i>=0?(t[n]={date:i>=9?i+1:"0"+(i+1),nl:""},t[n].nl=l(this.cYear,this.cMonth,i+1)):t[n]="",n++;this.days=t},chooseDate:function(t,e){e&&(this.currentDayIn=t,this.isTime||this.$emit("changeDate",t))},submit:function(){this.isTime?this.$emit("changeDate",this.currentDayIn+" "+this.currentTimeIn):this.$emit("changeDate",this.currentDayIn)},close:function(){this.$emit("closeDate")},initTime:function(){if(""==this.currentDate){var t=new Date;this.cYear=t.getFullYear(),this.cMonth=t.getMonth()+1,this.cMonthStr=this.cMonth<10?"0"+this.cMonth:this.cMonth,this.cDay=t.getDate(),this.cDay=this.cDay<10?"0"+this.cDay:this.cDay,this.currentDayIn=this.cYear+"-"+this.cMonthStr+"-"+this.cDay,this.currentTimeIn="00:00",this.changeMonth()}else{var e=this.currentDate.split(" ");e[1]||(e[1]="");var a=e[0].split("-");this.cYear=Number(a[0]),this.cMonth=a[1],this.cDay=a[2];var n=new RegExp("^0[0-9]+$");n.test(this.cMonth)&&(this.cMonth=this.cMonth.substr(1,1)),this.cMonth=Number(this.cMonth),this.cMonthStr=this.cMonth<10?"0"+this.cMonth:this.cMonth,this.currentDayIn=e[0],this.currentTimeIn=e[1],this.changeMonth()}},open:function(){this.realshow=!0},hide:function(){this.realshow=!1}}};e.default=d},"441a":function(t,e,a){"use strict";a.r(e);var n=a("9bb2"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"45b35":function(t,e,a){"use strict";var n=a("8bdb"),i=a("1dbd"),r=a("7992"),o=a("1099"),c=a("1fc1"),s=a("3242");n({target:"Array",proto:!0},{flatMap:function(t){var e,a=o(this),n=c(a);return r(t),e=s(a,0),e.length=i(e,a,a,n,0,1,t,arguments.length>1?arguments[1]:void 0),e}})},"6a00":function(t,e,a){var n=a("f9a3");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("3f5d768e",n,!0,{sourceMap:!1,shadowMode:!1})},"6c4e":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("v-uni-view",{staticClass:"grace-date",style:{top:t.top,zIndex:t.zIndex,left:t.realshow?"0":"-2000px"},on:{touchmove:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.stopfun.apply(void 0,arguments)},click:function(e){e.stopPropagation(),arguments[0]=e=t.$handleEvent(e),t.stopfun.apply(void 0,arguments)}}},[a("v-uni-view",{staticClass:"grace-date-header"},[a("v-uni-text",{staticClass:"grace-date-header-btn grace-icons",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.prevYear.apply(void 0,arguments)}}},[t._v("")]),a("v-uni-text",{staticClass:"grace-date-header-btn grace-icons",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.prevMonth.apply(void 0,arguments)}}},[t._v("")]),a("v-uni-text",{staticClass:"grace-date-header-date grace-icons"},[t._v(t._s(t.cYear)+" 年 "+t._s(t.cMonth)+" 月")]),a("v-uni-text",{staticClass:"grace-date-header-btn grace-icons",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.nextMonth.apply(void 0,arguments)}}},[t._v("")]),a("v-uni-text",{staticClass:"grace-date-header-btn grace-icons",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.nextYear.apply(void 0,arguments)}}},[t._v("")])],1),a("v-uni-view",{staticClass:"grace-date-week"},t._l(t.weeks,(function(e,n){return a("v-uni-text",{key:n,staticClass:"grace-date-weeks"},[t._v(t._s(e))])})),1),a("v-uni-view",{staticClass:"grace-date-days"},t._l(t.days,(function(e,n){return a("v-uni-view",{key:n,class:["grace-date-ditems",t.currentDayIn==t.cYear+"-"+t.cMonthStr+"-"+e.date?"grace-d-current":""],style:{background:t.currentDayIn==t.cYear+"-"+t.cMonthStr+"-"+e.date?t.activeBgColor:t.bgColor,borderRadius:t.borderRadius},on:{click:function(a){arguments[0]=a=t.$handleEvent(a),t.chooseDate(t.cYear+"-"+t.cMonthStr+"-"+e.date,e.date)}}},[a("v-uni-text",{staticClass:"grace-date-day",class:[t.currentDayIn==t.cYear+"-"+t.cMonthStr+"-"+e.date?"grace-d-current-txt":""]},[t._v(t._s(e.date))]),t.isLunar?a("v-uni-text",{staticClass:"grace-date-nl",class:[t.currentDayIn==t.cYear+"-"+t.cMonthStr+"-"+e.date?"grace-d-current-txt":""]},[t._v(t._s(e.nl))]):t._e()],1)})),1),t.isTime?a("v-uni-view",{staticClass:"grace-nowrap grace-flex-center",staticStyle:{"margin-top":"50rpx"}},[a("v-uni-picker",{staticClass:"grace-date-time",attrs:{mode:"time",value:t.currentTimeIn},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.timechange.apply(void 0,arguments)}}},[a("v-uni-text",[t._v("时间 : "+t._s(t.currentTimeIn))])],1)],1):t._e(),t.isTime?a("v-uni-view",{staticClass:"grace-date-btns"},[a("v-uni-text",{staticClass:"grace-date-btns-text",staticStyle:{color:"#888888"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.close.apply(void 0,arguments)}}},[t._v("关闭")]),a("v-uni-text",{staticClass:"grace-date-btns-text",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.submit.apply(void 0,arguments)}}},[t._v("确定")])],1):t._e()],1)},i=[]},"6f1c":function(t,e,a){"use strict";a.r(e);var n=a("3d47e"),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},"7eae":function(t,e,a){var n=a("9414");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var i=a("967d").default;i("53993401",n,!0,{sourceMap:!1,shadowMode:!1})},9414:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,".infoCard[data-v-0a78185d]{padding:15px;border-radius:10px;margin-top:12.5px;position:relative}.infoCard .cardTitle[data-v-0a78185d]{margin-bottom:16px}.infoCard .cardTitle .titleText[data-v-0a78185d]{font-weight:600;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555}.infoCard .cardTitle .titlePoint[data-v-0a78185d]{position:absolute;left:0;top:13px;width:6px;height:20px;border-radius:3px 3px 0 3px;opacity:1;background:#3e73fe}.infoCard .cardItem[data-v-0a78185d]{display:flex;margin-bottom:12px;justify-content:space-between;font-size:%?24?%;font-weight:400;line-height:%?28?%;letter-spacing:0;color:#555}.infoCard .cardItem[data-v-0a78185d]:last-child{margin-bottom:0}.itemsCard[data-v-0a78185d]{padding:15px;border-radius:10px;margin-top:12.5px;position:relative}.itemsCard .cardTitle[data-v-0a78185d]{margin-bottom:16px}.itemsCard .cardTitle .titleText[data-v-0a78185d]{font-weight:600;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555}.itemsCard .cardTitle .titlePoint[data-v-0a78185d]{position:absolute;left:0;top:13px;width:6px;height:20px;border-radius:3px 3px 0 3px;opacity:1;background:#3e73fe}.itemsCard .cardItem[data-v-0a78185d]{display:flex;margin-bottom:12px;justify-content:space-between}.itemsCard .cardItem[data-v-0a78185d]:last-child{margin-bottom:0}.itemsCard .cardItem .itemLabel[data-v-0a78185d]{font-weight:400;font-size:%?28?%;line-height:%?28?%;letter-spacing:0;color:#555}.itemsCard .cardItem .itemContent[data-v-0a78185d]{font-weight:400;font-size:%?24?%;line-height:%?28?%;letter-spacing:0;color:#2a91fc}.itemsCard .cardBtns[data-v-0a78185d]{display:flex;justify-content:flex-end}.itemsCard .cardBtns .cancelBtn[data-v-0a78185d]{color:#3e73fe;background-color:#fff;border:1px solid #3e73fe;border-radius:4px}.itemsCard .cardBtns .updateBtn[data-v-0a78185d]{color:#fff;background-color:#3e73fe;border:1px solid #3e73fe;border-radius:4px}.itemDialog .dialogContent[data-v-0a78185d]{padding:0 %?52?%;font-size:%?32?%;letter-spacing:0;color:#666;margin-bottom:%?32?%;min-height:120px;height:auto!important}.itemDialog .dialogBtns[data-v-0a78185d]{border-top:1px solid #e9e9e9}.itemDialog .dialogBtns .IKnowBtn[data-v-0a78185d]{width:auto;font-size:%?34?%;font-weight:500;letter-spacing:%?2?%;color:#8c8c8c}.splitLine[data-v-0a78185d]{margin-bottom:12px;width:100%;height:0;opacity:1;border-top:1px solid #e9e9e9}.desc[data-v-0a78185d]{display:flex;flex-direction:column;justify-content:center;align-items:center;font-size:%?24?%;line-height:%?28?%;color:#999}.totalCount[data-v-0a78185d]{width:100%;height:%?66?%;padding:0 %?20?%;margin:%?20?% %?30?% %?32?% %?30?%;display:flex;flex-wrap:nowrap;align-items:center;justify-content:flex-end}.totalCount uni-button[data-v-0a78185d]{color:#fff;background-color:#3e73fe;border:1px solid #3e73fe;border-radius:4px}.popHeader[data-v-0a78185d]{height:%?140?%;line-height:7vh;width:100%;position:fixed;top:%?-100?%;color:#000;background-color:#fff;font-size:18px;font-weight:700;text-align:center;border-radius:12px 12px 0 0}.popBody[data-v-0a78185d]{height:%?750?%;overflow:auto}.none[data-v-0a78185d]{display:none}",""]),t.exports=e},"9bb2":function(t,e,a){"use strict";(function(t){a("6a54");var n=a("f5bd").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,a("473f"),a("bf0f"),a("c223"),a("fd3c"),a("aa9c"),a("8f71"),a("c1a3"),a("18f7"),a("de6c"),a("4626"),a("5ac7"),a("aa77"),a("2797"),a("d4b5"),a("f3f7"),a("45b35"),a("dd62"),a("64aa");var i=n(a("2634")),r=n(a("2fdc")),o=n(a("b7c7")),c=n(a("9b1b")),s=n(a("265c")),l=n(a("6fb7")),d=n(a("d544")),u=n(a("0510")),f=a("8f59"),p={name:"TjAppointmentInfo",components:{graceDate:s.default,graceDialog:l.default,graceHeaderAlert:d.default},data:function(){return{contract:!1,showDialog:!1,dialogContent:[],showGraceDate:!1,selectedDate:"",userInfo:{},tjPlan:{},userId:"",tjPlanId:"",adminUserId:"",requiredItems:[],occupationalItems:[],requiredAndOccupationalItems:[],selectionQuota:0,maxPrice:0}},computed:(0,c.default)((0,c.default)({},(0,f.mapGetters)(["tjPlanInfo","appointmentInfo","optionalItemsSelected","optionalItems"])),{},{checkItems:function(){return[].concat((0,o.default)(this.requiredAndOccupationalItems),(0,o.default)(this.optionalItemsSelected)).reduce((function(e,a){t.log("🍊cur",a.type);var n={checkItemId:a._id,name:a.name,comments:a.comments,price:a.price,payType:a.payType,type:a.type};return e.map.has(n.checkItemId)?"1"===e.map.get(n.checkItemId)&&(e.array=e.array.filter((function(t){return t.checkItemId!==n.checkItemId})),e.array.push(n)):(e.map.set(n.checkItemId,n.payType),e.array.push(n)),e}),{map:new Map,array:[]}).array},totalPrice:function(){return this.checkItems.reduce((function(t,e,a,n){var i=e.comments.map((function(t){return t.cnCode})),r=n.some((function(t,e){if(e===a)return!1;var n=t.comments.map((function(t){return t.cnCode}));return i.every((function(t){return n.includes(t)}))}));return r?t:t+e.price}),0)},selfPay:function(){var t=this.selectionQuota,e=0,a=this.requiredAndOccupationalItems.filter((function(t){return"1"===t.payType})),n=this.optionalItemsSelected.filter((function(t){return"1"===t.payType}));if(a.length){var i=(0,o.default)(n),r=a.filter((function(a){var r=a.comments.map((function(t){return t.cnCode})),o=n.find((function(t){var e=t.comments.map((function(t){return t.cnCode}));return r.every((function(t){return e.includes(t)}))}));if(o){var c=o.price-a.price;return c>t?(e+=c-t,t=0):t-=c,i=i.filter((function(t){return t!==o})),!1}return!0})),c=r.reduce((function(t,e){return t+e.price}),0);i.forEach((function(a){t>0?a.price>t?(e+=a.price-t,t=0):t-=a.price:e+=a.price}));var s=c+e;return s}var l=this.totalPrice>this.maxPrice?this.totalPrice-this.maxPrice:0;return l},enterprisePay:function(){return this.totalPrice-this.selfPay}}),onLoad:function(e){var a=this;return(0,r.default)((0,i.default)().mark((function n(){var r,c;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.log("🍊option",e),a.userId=e.userId,a.tjPlanId=e.tjPlanId,!a.userId||!a.tjPlanId){n.next=8;break}return n.next=6,a.proxyReserve({userId:a.userId,tjPlanId:a.tjPlanId});case 6:n.next=11;break;case 8:a.tjPlan=a.tjPlanInfo,a.userInfo=a.$store.state.user.userInfo,a.userId=a.$store.state.user.userInfo._id;case 11:return n.next=13,a.getRequiredCheckItemList();case 13:return n.next=15,a.getOccupationalHealth({harmFactors:JSON.stringify(a.tjPlan.harmFactors||[])});case 15:return n.next=17,a.getOptionalCheckItemList();case 17:r=new Set(a.requiredItems.flatMap((function(t){return t.comments.map((function(t){return t.cnCode}))}))),c=a.occupationalItems.filter((function(t){return!t.comments.some((function(t){return r.has(t.cnCode)}))})),a.requiredAndOccupationalItems=[].concat((0,o.default)(a.requiredItems),(0,o.default)(c)),a.maxPrice=a.requiredAndOccupationalItems.filter((function(t){return"0"===t.payType})).reduce((function(t,e){return t+e.price}),0)+a.selectionQuota,t.log("🍊requiredAndOccupationalItems",a.requiredAndOccupationalItems);case 22:case"end":return n.stop()}}),n)})))()},methods:{selectQuota:function(e){return e=Number(e),t.log("🍊age",e),e>=40?this.tjPlan.selectionQuota||0:this.tjPlan.selectionQuota2||0},getOptionalCheckItemList:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var a,n,r,o,s;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={organizationId:t.tjPlan.physicalExaminationOrgID,userId:t.userId,tjPlanId:t.tjPlanInfo._id},t.appointmentInfo&&t.appointmentInfo._id&&(a.appointmentId=t.appointmentInfo._id),e.next=4,u.default.getOptionalCheckItemList(a);case 4:n=e.sent,r=[],o=[],t.appointmentInfo&&t.appointmentInfo.checkItems&&(o=t.appointmentInfo.checkItems.map((function(t){return t.checkItemId}))),n.data.forEach((function(t){if(!["乳腺彩超检查","阴道分泌物检查","妇科彩超检查"].includes(t.name)){var e=!1;o.length&&o.includes(t._id)&&(e=!0),r.push((0,c.default)((0,c.default)({},t),{},{checked:e,price:t.oldPrice>=0?t.oldPrice:t.price}))}})),t.$store.commit("setOptionalItems",r),s=r.filter((function(t){return t.checked})),t.$store.commit("setOptionalItemsSelected",s);case 12:case"end":return e.stop()}}),e)})))()},getRequiredCheckItemList:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var a,n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={organizationId:t.tjPlan.physicalExaminationOrgID,userId:t.userId,tjPlanId:t.tjPlanInfo._id},t.appointmentInfo&&t.appointmentInfo._id&&(a.appointmentId=t.appointmentInfo._id),e.next=4,u.default.getRequiredCheckItemList(a);case 4:n=e.sent,t.requiredItems=n.data.map((function(t){return(0,c.default)((0,c.default)({},t),{},{price:t.oldPrice>=0?t.oldPrice:t.price})})),t.selectionQuota=t.selectQuota(t.tjPlan.age),t.contract=t.requiredItems.length>5;case 8:case"end":return e.stop()}}),e)})))()},getOccupationalHealth:function(t){var e=this;return(0,r.default)((0,i.default)().mark((function a(){var n,r;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(n=t.harmFactors,!(e.tjPlan.harmFactors&&e.tjPlan.harmFactors.length>0)){a.next=6;break}return a.next=4,u.default.getOccupationalHealth({harmFactors:n,organizationId:e.tjPlan.physicalExaminationOrgID});case 4:r=a.sent,e.occupationalItems=r.data.map((function(t){return(0,c.default)((0,c.default)({},t),{},{price:t.oldPrice>=0?t.oldPrice:t.price})}));case 6:case"end":return a.stop()}}),a)})))()},gotoAddItems:function(){uni.navigateTo({url:"/pages_user/pages/user/addItems"})},openPop:function(){this.$refs.popup.open("bottom"),this.showGraceDate=!0},openDialog:function(t){this.showDialog=!0,this.dialogContent=t.map((function(t){return t.name}))},closeDialog:function(){this.showDialog=!1,this.dialogContent=""},closeDate:function(){this.$refs.graceDate.hide()},showDate:function(){this.$refs.graceDate.open()},changeDate:function(t,e){var a=this;return(0,r.default)((0,i.default)().mark((function n(){var r,o,s;return(0,i.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a.selectedDate=t,a.selectIndex=e,r=a.checkItems.map((function(t){return(0,c.default)((0,c.default)({},t),{},{comments:t.comments.map((function(t){return{cnCode:t.cnCode,name:t.name}}))})})),o={appointDate:new Date(a.selectedDate).setHours(0,0,0,0),tjPlanId:a.tjPlan._id,userId:a.userId,adminUserId:a.adminUserId,appointmentStatus:a.tjPlan.appointmentStatus||0,physicalExaminationOrgId:a.tjPlan.physicalExaminationOrgID,physicalExaminationOrgName:a.tjPlan.physicalExaminationOrgName,address:a.tjPlan.address,checkItems:r,totalPrice:a.totalPrice,enterprisePay:a.enterprisePay,selfPay:a.selfPay,selectionQuota:a.selectionQuota,selectIndex:a.selectIndex},s=null,!a.appointmentInfo||!a.appointmentInfo._id){n.next=13;break}return o._id=a.appointmentInfo._id,o.tjPlanId=a.$store.state.user.tjPlanInfo._id,n.next=10,u.default.updateTjAppointment(o);case 10:s=n.sent,n.next=16;break;case 13:return n.next=15,u.default.createTjAppointment(o);case 15:s=n.sent;case 16:200===s.status?(a.closeDate(),uni.navigateBack({delta:1,success:function(){uni.$emit("refresh")}}),a.$store.state.user.userInfo.employeeId===a.userId?a.$store.commit("setAppointedInfo",s.data||o):a.$store.commit("setAppointedInfo",null),uni.showToast({title:"预约成功"})):uni.showToast({title:"预约失败",icon:"error"});case 17:case"end":return n.stop()}}),n)})))()},proxyReserve:function(t){var e=this;return(0,r.default)((0,i.default)().mark((function a(){var n;return(0,i.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,u.default.getTjPlan(t);case 2:n=a.sent,200===n.status&&(e.userInfo=n.data,e.tjPlan=n.data,e.tjPlan.proxyReserve=e.tjPlanInfo.proxyReserve,e.adminUserId=e.$store.state.user.userInfo.employeeId,n.data.totalPrice?(e.$store.commit("setAppointedInfo",n.data),e.$store.commit("setTjAppointInfo",{_id:n.data.tjPlanId})):(e.$store.commit("setAppointedInfo",null),e.$store.commit("setTjAppointInfo",e.tjPlan)));case 4:case"end":return a.stop()}}),a)})))()},getTjPlan:function(){var t=this;return(0,r.default)((0,i.default)().mark((function e(){var a,n;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.default.getTjPlan();case 2:a=e.sent,n=a.data.map((function(t){return(0,c.default)((0,c.default)({},t),{},{contract:t.contract||"-",phoneNum:t.phoneNum||"-"})})),t.$store.commit("setTjAppointInfo",n[0]);case 5:case"end":return e.stop()}}),e)})))()}}};e.default=p}).call(this,a("ba7c")["default"])},"9e24":function(t,e,a){"use strict";var n=a("7eae"),i=a.n(n);i.a},a75a:function(t,e,a){"use strict";a.r(e);var n=a("6c4e"),i=a("6f1c");for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a("efe5d");var o=a("828b"),c=Object(o["a"])(i["default"],n["b"],n["c"],!1,null,"97acceaa",null,!1,n["a"],void 0);e["default"]=c.exports},dd62:function(t,e,a){"use strict";var n=a("1cb5");n("flatMap")},efe5d:function(t,e,a){"use strict";var n=a("6a00"),i=a.n(n);i.a},f9a3:function(t,e,a){var n=a("c86c");e=n(!1),e.push([t.i,".grace-date[data-v-97acceaa]{position:fixed;z-index:1;left:-2000px;top:0;bottom:0;width:%?730?%;padding:0 %?10?%;display:flex;flex-direction:column;align-items:center;background:#fff;overflow:hidden}.grace-date-header[data-v-97acceaa]{display:flex;justify-content:center;flex-direction:row;text-align:center;margin-top:%?20?%}.grace-date-header-btn[data-v-97acceaa]{font-size:%?36?%;line-height:%?88?%;padding:0 %?10?%;color:#888}.grace-date-header-date[data-v-97acceaa]{line-height:%?88?%;font-size:%?36?%;margin:0 %?20?%}.grace-date-week[data-v-97acceaa]{text-align:center;width:%?702?%;display:flex;flex-wrap:nowrap;flex-direction:row}.grace-date-weeks[data-v-97acceaa]{display:block;width:%?100?%;height:%?80?%;text-align:center;font-size:%?32?%;line-height:%?80?%;color:#666}.grace-date-days[data-v-97acceaa]{width:%?702?%;display:flex;flex-direction:row;flex-wrap:wrap}.grace-date-ditems[data-v-97acceaa]{width:%?96?%;height:%?96?%;display:flex;flex-direction:column;align-items:center;justify-content:center;margin:%?2?%;background-color:#f6f7f8;border-radius:%?5?%}.grace-d-current[data-v-97acceaa]{background-color:#3688ff}.grace-d-current-txt[data-v-97acceaa]{color:#fff!important}.grace-date-day[data-v-97acceaa]{display:block;width:100%;height:%?38?%;line-height:%?38?%;text-align:center;font-size:%?32?%}.grace-date-nl[data-v-97acceaa]{display:block;width:100%;height:%?26?%;line-height:%?26?%;color:#888;font-size:%?20?%;text-align:center}.grace-date-btns[data-v-97acceaa]{display:flex;flex-wrap:nowrap;flex-direction:row;justify-content:space-between;position:absolute;z-index:1;left:0;bottom:20px;width:100%}.grace-date-btns-text[data-v-97acceaa]{display:block;color:#3688ff;line-height:%?100?%;font-size:%?30?%;text-align:center;width:%?300?%}.grace-date-time[data-v-97acceaa]{font-size:%?30?%;line-height:%?100?%;color:#666;border-top:1px solid #f6f6f6;border-bottom:1px solid #f6f6f6;padding:0 %?20?%}",""]),t.exports=e}}]);