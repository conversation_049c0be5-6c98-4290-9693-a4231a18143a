"use strict";
import req from '@/utils/http.js' //导入 封装的请求

export default {
    getExperts: data => {
        return req({
            url: `manage/consultation/getExperts`,
            method: 'get',
            data
        });
    },


    reserveList: data => {
      return req({
          url: `manage/consultation/reserveList`,
          method: 'get',
          data
      });
    },

    getAgoraConfig: data => {
      return req({
          url: `manage/consultation/getAgoraConfig`,
          method: 'get',
          data
      });
    },

    reserveRemote: data => {
        return req({
            url: `manage/consultation/reserveRemote`,
            method: 'post',
            data
        });
    },

      loginVerification: (data) => {
        return req({
          url: 'manage/user/loginVerification',
          method: 'get',
          data,
        });
      },
      //删除
      deleteReserve:(data)=>{
        return req({
            url: `manage/consultation/deleteReserve`,
            method: 'post',
            data
        });
      }
}