import req from '@/utils/http.js' //导入 封装的请求
let api = {
	followupRecordList: data => {
	    return req({
	        url: 'manage/rehab/followupRecordList', 
	        method: 'get',  
			data
	    })
	},
	treatmentInformationList: data => {
	    return req({
	        url: 'manage/rehab/treatmentInformationList', 
	        method: 'get',  
			data
	    })
	},
	treatmentInformationDetail: data => {
	    return req({
	        url: 'manage/rehab/treatmentInformationDetail', 
	        method: 'get',  
			data
	    })
	},
	medicationGuidanceList: data => {
	    return req({
	        url: 'manage/rehab/medicationGuidanceList', 
	        method: 'get',  
			data
	    })
	},
	medicationGuidanceDetail: data => {
	    return req({
	        url: 'manage/rehab/medicationGuidanceDetail', 
	        method: 'get',  
			data
	    })
	},
	recoveryInfo: data => {
	    return req({
	        url: 'manage/rehab/recoveryInfo', 
	        method: 'get',  
			data
	    })
	},
	recoveryInfoUpload: data => {
	    return req({
	        url: 'manage/rehab/recoveryInfoUpload', 
	        method: 'get',  
			data
	    })
	},
	personnel: data => {
	    return req({
	        url: 'manage/rehab/personnel', 
	        method: 'get',  
			data
	    })
	},
	station: data => {
	    return req({
	        url: 'manage/rehab/station', 
	        method: 'get',  
			data
	    })
	},
	appointment: data => {
	    return req({
	        url: 'manage/rehab/appointment', 
	        method: 'get',  
			data
	    })
	},
	createAppointment: data => {
	    return req({
	        url: 'manage/rehab/createAppointment', 
	        method: 'post',  
			data
	    })
	},
	createRehabGuideApplication: data => {
	    return req({
	        url: 'manage/rehab/createRehabGuideApplication', 
	        method: 'post',  
			data
	    })
	},
  getDiseaseClassify: data => {
	    return req({
	        url: 'manage/eHealthRecord/getDiseaseClassify', 
	        method: 'get',  
			data
	    })
	},
  medicationGuidanceApply: data => {
    return req({
        url: 'manage/rehab/medicationGuidanceApply', 
        method: 'get',  
    data
    })
  }, 
  medicationGuidanceApplyAdd: data => {
    return req({
        url: 'manage/rehab/medicationGuidanceApplyAdd', 
        method: 'post',  
    data
    })
},
medicationGuidanceApplyFileDelete: data => {
  return req({
      url: 'manage/rehab/medicationGuidanceApplyFileDelete', 
      method: 'delete',  
  data
  })
},
medicationGuidanceApplyDetail: data => {
  return req({
      url: 'manage/rehab/medicationGuidanceApplyDetail', 
      method: 'get',  
  data
  })
}, 

medicationGuidanceApplyCancel: data => {
  return req({
      url: 'manage/rehab/medicationGuidanceApplyCancel', 
      method: 'delete',  
  data
  })
},

getMedicationGuidanceDetail: data => {
  return req({
    url: 'manage/rehab/getMedicationGuidanceDetail', 
    method: 'get',  
    data
  })
}, 
informationNewsPagePatient: data => {
  return req({
    url: 'manage/rehab/informationNewsPagePatient', 
    method: 'get',  
    data
  })
}, 
informationNewsDetail: data => {
  return req({
    url: 'manage/rehab/informationNewsDetail', 
    method: 'get',  
    data
  })
}, 
// 康复指导
rehabGuidanceApplyPage: data => {
  return req({
      url: 'manage/rehab/rehabGuidanceApplyPage', 
      method: 'get',  
  data
  })
}, 
rehabGuidanceApplyAdd: data => {
  return req({
      url: 'manage/rehab/rehabGuidanceApplyAdd', 
      method: 'post',  
  data
  })
},
rehabGuidanceApplyFileDelete: data => {
  return req({
      url: 'manage/rehab/rehabGuidanceApplyFileDelete', 
      method: 'post',  
  data
  })
},
rehabGuidanceApplyDetail: data => {
  return req({
      url: 'manage/rehab/rehabGuidanceApplyDetail', 
      method: 'get',  
  data
  })
}, 

rehabGuidanceApplyCancel: data => {
  return req({
      url: 'manage/rehab/rehabGuidanceApplyCancel', 
      method: 'delete',  
  data
  })
},


informationNewsRehabPagePatient: data => {
  return req({
      url: 'manage/rehab/informationNewsRehabPagePatient', 
      method: 'get',  
  data
  })
}, 
rehabGuidanceApplyExportPatient: data => {
  return req({
      url: 'manage/rehab/rehabGuidanceApplyExportPatient', 
      method: 'get',  
      data,
  })
}, 
};
export default api;  //导出