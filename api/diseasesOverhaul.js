"use strict";
import req from '@/utils/http.js' //导入 封装的请求

let diseasesOverhaulApi = {
  //     GET
  // ​/manage​/diseasesOverhaul​/list
  // 获取检修维护列表
  list: (data) => {
    return req({
      url: 'manage/diseasesOverhaul/list',
      method: 'get',
      data,
    });
  },
  // POST
  // ​/manage​/diseasesOverhaul​/add
  // 添加检修维护
  add: (data) => {
    return req({
      url: 'manage/diseasesOverhaul/add',
      method: 'post',
      data,
    });
  },
  // PUT
  // ​/manage​/diseasesOverhaul​/update
  // 修改检修维护
    update: (data) => {
      console.log('进入update',data)
    return req({
      url: 'manage/diseasesOverhaul/update',
      method: 'put',
      data,
    });
  },
  // DELETE
  // ​/manage​/diseasesOverhaul​/delete
  // 删除检修维护
  delete: (data) => {
    return req({
      url: 'manage/diseasesOverhaul/delete',
      method: 'delete',
      data,
    });
  },
  //    workPlace
  getWorkplace: (data) => {
    return req({
      url: 'manage/diseasesOverhaul/getWorkplace',
      method: 'get',
      data,
    });
  },
    // 模糊查人/manage/diseasesOverhaul/searchPerson
    searchPerson: (data) => {
        return req({
            url: 'manage/diseasesOverhaul/searchPerson',
            method: 'get',
            data,
        })
    }
};
export default diseasesOverhaulApi