"use strict";
import req from '@/utils/http.js' //导入 封装的请求

export default {
    


    reserveList: data => {
      return req({
          url: `manage/remoteClinic/reserveList`,
          method: 'get',
          data
      });
    },

    reserveRemote: data => {
        return req({
            url: `manage/remoteClinic/reserveRemote`,
            method: 'post',
            data
        });
    },

    //删除
    deleteReserve:(data)=>{
      return req({
          url: `manage/remoteClinic/deleteReserve`,
          method: 'post',
          data
      });
    }
}