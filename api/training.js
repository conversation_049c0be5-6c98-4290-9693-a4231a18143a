"use strict";
import req from '@/utils/http.js' //导入 封装的请求

let trainingApi = {
	// 获取热门课程列表
	getHotCourses: param => {
		return req({
			url: 'manage/training/courses/getHotCourses',
			method: 'get',
			param
		})
	},


	getClassification: param => {
		return req({
			url: 'manage/training/courses/getClassification',
			method: 'get',
			data: param
		})
	},


	getCourseOne: param => {
		return req({
			url: 'manage/training/courses/getCourseOne',
			method: 'get',
			data: param
		})
	},

	// 获取播放地址
	getVideoUrl: param => {
		console.log(param)
		return req({
			url: 'manage/training/courses/getVideoUrl',
			method: 'get',
			data: param
		})
	},

	// 更新进度
	updateCourseProgress: data => {
		return req({
			url: 'manage/training/courses/updateCourseProgress',
			method: 'post',
			data
		})
	},

	likeCourse: data => {
		return req({
			url: 'manage/training/courses/likeCourse',
			method: 'post',
			data
		})
	},

	searchCourse: data => {
		return req({
			url: 'manage/training/courses/searchCourse',
			method: 'get',
			data
		})
	},

	getCourseByClass: data => {
		return req({
			url: 'manage/training/courses/getCourseByClass',
			method: 'get',
			data
		})
	},

	creatComment: data => {
		return req({
			url: 'manage/training/courses/creatComment',
			method: 'post',
			data
		})
	},

	getComments: data => {
		return req({
			url: 'manage/training/courses/getComments',
			method: 'get',
			data
		})
	},

	createCourseReply: data => {
		return req({
			url: 'manage/training/courses/createReply',
			method: 'post',
			data
		})
	},

	likeCourseComment: data => {
		return req({
			url: 'manage/training/courses/likeComment',
			method: 'post',
			data
		})
	},

	getMycourses: data => {
		return req({
			url: 'manage/training/courses/getMyclourses',
			method: 'post',
			data
		})
	},

	createBulletScreenComment: data => {
		return req({
			url: 'manage/training/courses/createBulletScreenComment',
			method: 'post',
			data
		})
	},
	getBulletScreenComment: data => {
		return req({
			url: 'manage/training/courses/getBulletScreenComment',
			method: 'get',
			data
		})
	},
	// 获取企业管理员的培训列表
	adminTrainingList: data => {
		return req({
			url: 'manage/adminTraining/list',
			method: 'post',
			data
		})
	},
	// 获取个人的培训列表
	personalTrainingList: data => {
		return req({
			url: 'manage/adminTraining/personalTrainingList',
			method: 'post',
			data
		})
	},
	// 创建一条个人培训
	createPersonalTraining: data => {
		return req({
			url: 'manage/adminTraining/createPersonalTraining',
			method: 'post',
			data
		})
	},
	// 跟新一条个人培训
	updatePersonalTraining: data => {
		return req({
			url: 'manage/adminTraining/updatePersonalTraining',
			method: 'post',
			data
		})
	},
	// 彻底删除一条个人培训（自主培训-公开课）
	delPersonalTraining: data => {
		return req({
			url: 'manage/adminTraining/delPersonalTraining',
			method: 'post',
			data
		})
	},
	// 获取个人培训记录详情
	getPersonalTraining: data => {
		return req({
			url: 'manage/adminTraining/getPersonalTraining',
			method: 'post',
			data
		})
	},
	// 获取培训详情
	getAdminTrainingDetail: data => {
		return req({
			url: 'manage/adminTraining/getDetail',
			method: 'post',
			data
		})
	},

	// 更新培训完成状态
	updateCompleteState: data => {
		return req({
			url: 'manage/training/courses/updateCompleteState',
			method: 'get',
			data,
		})
	},

	// 获取暂停时间
	getPauseTime: data => {
		return req({
			url: 'manage/training/courses/getPauseTime',
			method: 'get',
			data,
		})
	},
	// 获取员工培训列表
	employeesTrainingList: data => {
		return req({
			url: 'manage/employeeTraining/list',
			method: 'post',
			data
		})
	},
	getSign: data => {
		return req({
			url: 'app/user/getSign',
			method: 'post',
			data
		})
	},
	getFacePicture: data=>{
		return req({
			url: 'app/user/getFacePicture',
			method: 'post',
			data
		})
	}
};
export default trainingApi;