"use strict";
import req from '@/utils/http.js' //导入 封装的请求

let diagnosisApi = {
	// 获取诊断机构列表
	getInstitutionInfo: data => {
		return req({
			url: `app/diagnosis/getInstitutionList`,
			method: 'get',
			data
		});
	},
	// 获取诊断机构详情
	getInsDetail: data => {
		return req({
			url: `app/diagnosis/getInstitutionDetail`,
			method: 'get',
			data
		})
	},
	// 获取字典类型数据
	getDictType: data => {
		return req({
			url: `app/diagnosis/getDictData`,
			method: 'get',
			data
		})
	},
	// 新增职业病诊断申请
	addDiagnosis: data => {
		return req({
			url: `app/diagnosis/applyDiagnosis`,
			method: 'post',
			data
		})
	},
	// 获取职业病诊断结果列表
	getDiagnosisInfo: data => {
		return req({
			url: `app/diagnosis/getDiagnosisList`,
			method: 'get',
			data
		});
	},
	// 获取职业病诊断详情
	getDiagnosisDetail: data => {
		return req({
			url: `app/diagnosis/detailDiagnosis`,
			method: 'get',
			data
		})
	},
	// 编辑职业病诊断申请
	editDiagnosis: data => {
		return req({
			url: `app/diagnosis/editDiagnosis`,
			method: 'put',
			data
		})
	},
	// 提交职业病诊断申请
	submitDiagnosis: data => {
		return req({
			url: `app/diagnosis/submit`,
			method: 'put',
			data
		})
	},

	// 待上传资料详情
	getProvideFile: data => {
		return req({
			url: `app/diagnosis/provideFile`,
			method: 'get',
			data
		})
	},
	// 获取疑似职业病列表
	getSuspectList: data => {
		return req({
			url: `manage/btHealthCheck/getSuspectList`,
			method: 'get',
			data
		})
	},
	// 下载职业病诊断证明书
	downloadDiagnosisFile: data => {
		return req({
			url: `app/diagnosis/downloadFile`,
			method: 'post',
			data,
		})
	},
	// 保存职业史
	saveDiagnosisJobHistory: data => {
		return req({
			url: `app/diagnosis/diagnosisJobHistory`,
			method: 'post',
			data
		})
	},
	// 删除职业史
	deleteDiagnosisJobHistory: data => {
		return req({
			url: `app/diagnosis/deleteDiagnosisJobHistory`,
			method: 'delete',
			data
		})
	},
	// 删除接触史
	deleteDiagnosisJobHistoryHazard: data => {
		return req({
			url: `app/diagnosis/deleteDiagnosisJobHistoryHazard`,
			method: 'delete',
			data
		})
	},
	// 获取工伤待遇查询接口
	getInjuredBenefit: data => {
		return req({
			url: `manage/injury/getBenefits`,
			method: 'get',
			data
		})
	},
	// 根据id获取职业病诊断类别
	getDiagnosisDisease: data => {
		return req({
			url: `app/diagnosis/getDiagnosisDisease`,
			method: 'get',
			data
		})
	},

  // 获取诊断过程附件
  getDiagnosisProcessFile: data => {
    return req({
      url: `app/diagnosis/getDiagnosisProcessFile`,
      method: 'get',
      data,
    })
  },

  // 删除诊断附件
  deleteDiagnosisFile: data => {
    return req({
      url: `app/diagnosis/deleteDiagnosisFile`,
      method: 'delete',
      data
    })
  },
}

export default diagnosisApi