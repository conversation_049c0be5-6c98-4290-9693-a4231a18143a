"use strict";
import req from '@/utils/http.js' //导入 封装的请求
import config from '@/common.js';

let api = {
	// 根据企业id获取企业所在地的监管单位
	getSupervision: data => {
	    return req({
	        url: 'manage/complaints/getSupervision', 
	        method: 'get',  
			data // companyId
	    })
	},
	// 添加投诉举报
	add: data => {
	    return req({
	        url: 'manage/complaints/add', 
	        method: 'post',  
			data
	    })
	},
	// 补充投诉举报
	update: data => {
	    return req({
	        url: 'manage/complaints/update', 
	        method: 'post',  
			data
	    })
	},
	// 删除投诉举报
	delete: data => {
	    return req({
	        url: 'manage/complaints/delete', 
	        method: 'get',  
			data
	    })
	},
	// 获取投诉举报
	get: data => {
	    return req({
	        url: 'manage/complaints/get', 
	        method: 'get',  
			data // userId
	    })
	},
	// 获取投诉举报详情
	getDetail: data => {
	    return req({
	        url: 'manage/complaints/getDetail', 
	        method: 'get',  
			data // _id
	    })
	},
	// 评价
	scoring: data => {
	    return req({
	        url: 'manage/complaints/scoring', 
	        method: 'post',  
			data
	    })
	},
	// 上传图片
	uploadImage: (config.apiServer ||  '') + 'app/user/uploadComplaintsImage'
};
export default api;  //导出
