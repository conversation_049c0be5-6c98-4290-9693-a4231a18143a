"use strict";
import req from '@/utils/http.js' //导入 封装的请求

let employeeApi = {

    // 获取工作场所信息；
    stationInfo2: data => {
        return req({
            url: 'manage/adminuser/stationInfo2',
            method: 'post',
            data
        })
    },
    // 获取工作场所危害因素检测结果；
    getCheckResult2: data => {
        return req({
            url: 'manage/adminuser/getCheckResult2',
            method: 'post',
            data
        })
    },
    // 获取岗位信息；
    stationInfo: data => {
        return req({
            url: 'manage/adminuser/stationInfo',
            method: 'post',
            data
        })
    },
    getCheckResult: data => {
        return req({
            url: 'manage/adminuser/getCheckResult',
            method: 'post',
            data
        })
    },
    getDefendproducts: data => {
        return req({
            url: 'manage/adminuser/getDefendproducts',
            method: 'post',
            data
        })
    },
    receiveProducts: data => {
        return req({
            url: 'manage/adminuser/receiveProducts',
            method: 'post',
            data
        })
    },
	getLaborIsEdit: data => {
	    return req({
	        url: 'manage/adminuser/getLaborIsEdit',
	        method: 'get',
	        data
	    })
	},
	getHistoryList: data => {
	    return req({
	        url: 'manage/adminuser/getHistoryList',
	        method: 'get',
	        data
	    })
	},
	addHistoryList: data => {
	    return req({
	        url: 'manage/adminuser/addHistoryList',
	        method: 'post',
	        data
	    })
	},
	deleteHistoryList: data => {
	    return req({
	        url: 'manage/adminuser/deleteHistoryList',
	        method: 'post',
	        data
	    })
	},
	editHistoryList: data => {
	    return req({
	        url: 'manage/adminuser/editHistoryList',
	        method: 'post',
	        data
	    })
	},
    ppeCabinetQRcode: data => { // 二维码扫描为自提柜时
        return req({
            url: 'mqtt/issueOrder',
            method: 'post',
            data
        })
    },
    getPPEVideo:data =>{
        return req({
            url: 'app/ppeVideo/getPPEVideo',
            method: 'get',
            data
        })
    },
      // 获取岗位信息；
    getStationChange: data => {
        return req({
            url: 'manage/adminuser/getStationChange',
            method: 'get',
            data
        })
    },
	// 获取获取单条文档类别；
	getOne: data => {
	    return req({
	        url: 'manage/blacklist/contentCategory',
	        method: 'get',
	        data
	    })
	},
	// 获取职业健康黑名单列表；
	contentGetlist: data => {
	    return req({
	        url: 'manage/blacklist/contentList',
	        method: 'get',
	        data
	    })
	},
	// 获取职业健康黑详情
	contentListDetail: data => {
	    return req({
	        url: 'manage/blacklist/contentListDetail',
	        method: 'get',
	        data
	    })
	},
   
};
export default employeeApi;