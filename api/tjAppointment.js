"use strict";
import req from '@/utils/http.js' //导入 封装的请求

let api = {

    // 获取转岗详细信息
    getTjPlan: (data) => {
        return req({
            url: 'manage/tjAppointment/getTjPlan',
            method: 'get',
            data,
        });
    },

    // 获取员工信息
    getTjEmployee: (data) => {
        return req({
            url: 'manage/tjAppointment/getTjEmployee',
            method: 'post',
            data,
        });
    },

    // 获取当前员工已预约体检
    getTjAppointment: (data) => {
        return req({
            url: 'manage/tjAppointment/getTjAppointment',
            method: 'get',
            data,
        });
    },

    // 获取必选项目列表
    getRequiredCheckItemList: (data) => {
        return req({
            url: 'manage/tjAppointment/getRequiredCheckItemList',
            method: 'get',
            data,
        });
    },

    // 获取职业病项目列表
    getOccupationalHealth: (data) => {
        return req({
            url: 'manage/tjAppointment/getOccupationalHealth',
            method: 'get',
            data,
        });
    },

    // 获取选件项目列表
    getOptionalCheckItemList: (data) => {
        return req({
            url: 'manage/tjAppointment/getOptionalCheckItemList',
            method: 'get',
            data,
        });
    },

    // 
    createTjAppointment: (data) => {
        return req({
            url: 'manage/tjAppointment/createTjAppointment',
            method: 'post',
            data,
        });
    },
    // 
    cancelTjAppointment: (data) => {
        return req({
            url: 'manage/tjAppointment/cancelTjAppointment',
            method: 'post',
            data,
        });
    },
    // 
    updateTjAppointment: (data) => {
        return req({
            url: 'manage/tjAppointment/updateTjAppointment',
            method: 'post',
            data,
        });
    },

    getTjPlanCount: (data) => {
        return req({
            url: 'manage/tjAppointment/getTjPlanCount',
            method: 'get',
            data,
        });
    },
    // 获取问卷答卷地址
    getQuestionnaireUrl: (data) => {
        return req({
            url: 'manage/tjAppointment/getQuestionnaireUrl',
            method: 'get',
            data,
        });
    },
}

export default api;  //导出